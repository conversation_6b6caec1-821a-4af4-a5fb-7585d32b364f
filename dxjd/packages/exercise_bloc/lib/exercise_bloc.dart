/// Modern exercise module with BLoC architecture
library exercise_bloc;

// Presentation layer exports
export 'src/presentation/pages/exercise_page.dart';

// BLoC exports
export 'src/presentation/bloc/exercise/exercise_bloc.dart';
export 'src/presentation/bloc/exercise/exercise_event.dart';
export 'src/presentation/bloc/exercise/exercise_state.dart';

export 'src/presentation/bloc/listen/listen_bloc.dart';
export 'src/presentation/bloc/listen/listen_event.dart' hide AudioCompleted;
export 'src/presentation/bloc/listen/listen_state.dart';

export 'src/presentation/bloc/browse/browse_bloc.dart';
export 'src/presentation/bloc/browse/browse_event.dart';
export 'src/presentation/bloc/browse/browse_state.dart';

export 'src/presentation/bloc/mode_switch/mode_switch_bloc.dart';
export 'src/presentation/bloc/mode_switch/mode_switch_event.dart';
export 'src/presentation/bloc/mode_switch/mode_switch_state.dart';

// Widget exports
export 'src/presentation/widgets/exercise_view.dart';
export 'src/presentation/widgets/listen_view.dart';
export 'src/presentation/widgets/browse_view.dart';
export 'src/presentation/widgets/video_view.dart';
export 'src/presentation/widgets/exercise_bottom_bar.dart';

// Domain layer exports
export 'src/domain/entities/question_entity.dart';
export 'src/domain/entities/exercise_progress.dart';
export 'src/domain/repositories/question_repository.dart';
export 'src/domain/repositories/audio_repository.dart';
export 'src/domain/repositories/progress_repository.dart';

// Use cases exports
export 'src/domain/usecases/load_questions.dart' hide LoadQuestions;
export 'src/domain/usecases/answer_question.dart' hide AnswerQuestion;
export 'src/domain/usecases/play_audio.dart';
export 'src/domain/usecases/save_progress.dart' hide SaveProgress;

// Data layer exports (only interfaces, not implementations)
export 'src/data/models/question_model.dart';

// Dependency injection
export 'src/injection/injection.dart';

// Module initialization
export 'src/exercise_module.dart';
