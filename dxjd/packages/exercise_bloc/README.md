# Exercise BLoC

Modern exercise module with BLoC architecture for driving exam app.

## 🎯 Overview

This package is a complete rewrite of the original exercise module using modern Flutter architecture patterns:

- **B<PERSON>o<PERSON> Pattern**: Clean separation of business logic and UI
- **Repository Pattern**: Abstracted data access layer
- **Dependency Injection**: Testable and maintainable code
- **Clean Architecture**: Clear separation of concerns

## 🏗️ Architecture

```
lib/
├── src/
│   ├── presentation/          # UI Layer
│   │   ├── pages/            # Screen widgets
│   │   ├── widgets/          # Reusable UI components
│   │   └── bloc/             # BLoC classes
│   ├── domain/               # Business Logic Layer
│   │   ├── entities/         # Business entities
│   │   ├── repositories/     # Repository interfaces
│   │   └── usecases/         # Business use cases
│   └── data/                 # Data Layer
│       ├── models/           # Data models
│       ├── repositories/     # Repository implementations
│       └── datasources/      # Data sources (API, DB, etc.)
└── exercise_bloc.dart        # Public API
```

## 🚀 Features

### Exercise Modes
- **做题模式**: Interactive question answering with immediate feedback
- **听题模式**: Audio-based learning with playback controls
- **背题模式**: Question browsing and review
- **视频模式**: Video-based learning content

### Key Capabilities
- ✅ Single choice, multiple choice, and true/false questions
- ✅ Audio playback with background support
- ✅ Progress tracking and statistics
- ✅ Error collection and review
- ✅ Bookmark functionality
- ✅ Offline support
- ✅ Real-time synchronization

## 🎨 UI Consistency

This package maintains **100% UI consistency** with the original implementation:
- Same visual design and styling
- Identical user interactions
- Preserved animations and transitions
- Compatible with existing themes

## 📱 Usage

```dart
// Initialize the exercise module
await ExerciseModule.initialize();

// Navigate to exercise page
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => ExercisePage(
      subject: 1,
      type: 'chapter',
      sortId: 1,
    ),
  ),
);
```

## 🧪 Testing

The package includes comprehensive tests:
- Unit tests for BLoCs and repositories
- Widget tests for UI components
- Integration tests for complete flows

Run tests:
```bash
flutter test
```

## 🔧 Development

### Code Generation
```bash
flutter packages pub run build_runner build
```

### Dependencies
This package depends on several local packages:
- `component_library`: Shared UI components
- `api`: Network layer
- `tools`: Utility functions
- `domain_models`: Shared data models

## 📄 License

This package is part of the driving exam app and follows the same licensing terms.
