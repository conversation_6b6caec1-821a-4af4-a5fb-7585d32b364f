import Flutter
import UIKit
import AMPSAdSDK

// MARK: - BeiziNativeViewFactory
public class BeiziNativeViewFactory: NSObject, FlutterPlatformViewFactory {
    private var messenger: FlutterBinaryMessenger
    private var plugin: BeiziAdSdkPlugin?
    
    init(messenger: FlutterBinaryMessenger,plugin: BeiziAdSdkPlugin) {
        self.messenger = messenger
        self.plugin = plugin
        super.init()
    }
    
    public func create(
        withFrame frame: CGRect,
        viewIdentifier viewId: Int64,
        arguments args: Any?
    ) -> FlutterPlatformView {
        return BeiziNativeView(
            frame: frame,
            viewIdentifier: viewId,
            arguments: args,
            binaryMessenger: messenger,
            plugin:self.plugin!
        )
    }
    
    public func createArgsCodec() -> FlutterMessageCodec & NSObjectProtocol {
        return FlutterStandardMessageCodec.sharedInstance()
    }
}

// MARK: - BeiziNativeView
public class BeiziNativeView: NSObject, FlutterPlatformView {
    private var _view: UIView
    private var currentBannerAdView: UIView?
    private var cWidth: CGFloat
    private var cHeight: CGFloat
    private var plugin: BeiziAdSdkPlugin?
    init(
        frame: CGRect,
        viewIdentifier viewId: Int64,
        arguments args: Any?,
        binaryMessenger messenger: FlutterBinaryMessenger?,
        plugin:BeiziAdSdkPlugin
    ) {
        self.plugin = plugin
        _view = UIView(frame: frame)
        self.cWidth = (args as? [String: Any])?["width"] as? CGFloat ?? 353
        self.cHeight = (args as? [String: Any])?["height"] as? CGFloat ?? 100
        _view.translatesAutoresizingMaskIntoConstraints = false
        super.init()
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleBannerAdDidLoad(_:)),
            name: NSNotification.Name("BeiziBannerAdDidLoadNotification"),
            object: nil
        )
        self.plugin!.loadNativeAd()
        createNativeView(view: _view)
    }
    
    public func view() -> UIView {
        return _view
    }
    
    @objc func handleBannerAdDidLoad(_ notification: Notification) {
        guard let unifiedNative = notification.object as? UnifiedNative else {
            print("BannerPlatformView: Received notification but failed to cast object to AMPSBannerAd")
            return
        }
            refreshView(unifiedNative: unifiedNative)
    }
    
    
    func refreshView(unifiedNative : UnifiedNative){
        DispatchQueue.global().async {
            DispatchQueue.main.async { [self] in
            _view.backgroundColor = UIColor(hex: "#FF828B91")
                    // 移除旧的广告视图（如果有）
                    self.currentBannerAdView?.removeFromSuperview()
                    let customView = CustomUIView(
                        imageUrl: unifiedNative.bannerAd.adArray[0].imageUrl,
                        title: unifiedNative.bannerAd.adArray[0].title,
                        desc: unifiedNative.bannerAd.adArray[0].desc,
                        iconUrl: unifiedNative.bannerAd.adArray[0].iconUrl,
                        logoUrl: unifiedNative.bannerAd.adArray[0].adLogoUrl,
                        actionText: unifiedNative.bannerAd.adArray[0].actionText,
                        width: cWidth,
                        height: cHeight,
                        name: unifiedNative.channelName
                    )
                    self.currentBannerAdView = customView
                    if let adView = self.currentBannerAdView {
                        adView.translatesAutoresizingMaskIntoConstraints = false
                        self._view.addSubview(adView)
                        // 设置约束使其填充容器
                        NSLayoutConstraint.activate([
                            adView.leadingAnchor.constraint(equalTo: self._view.leadingAnchor),
                            adView.trailingAnchor.constraint(equalTo: self._view.trailingAnchor),
                            adView.topAnchor.constraint(equalTo: self._view.topAnchor),
                            adView.bottomAnchor.constraint(equalTo: self._view.bottomAnchor)
                        ])
                        unifiedNative.view.registerClickableViews([self._view])
                    }
        
        }
      }
    }
    
    func createNativeView(view _view: UIView) {
        DispatchQueue.global().async {
            DispatchQueue.main.async { [self] in
                setDefault()
            }
        }
    }
    
    func setDefault() {
        let defaultBannerView = UIImageView()
        defaultBannerView.frame = CGRect(x: 0, y: 0, width: cWidth, height: cHeight)
        NetworkImageLoader.loadImage(from: "https://dx-stg.oss-cn-shenzhen.aliyuncs.com/app/resources/dxjd-home-banner-default.png", into: defaultBannerView)
        defaultBannerView.contentMode = .scaleToFill
        self._view.clipsToBounds = true
        self._view.addSubview(defaultBannerView)
    }
}

// MARK: - 网络图片加载器
class NetworkImageLoader {
    static func loadImage(from urlString: String, into imageView: UIImageView) {
        guard let url = URL(string: urlString) else {
            if #available(iOS 13.0, *) {
                imageView.image = UIImage(systemName: "photo")
            }
            return
        }
        
        let task = URLSession.shared.dataTask(with: url) { data, response, error in
            if let error = error {
                print("Error loading image: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    if #available(iOS 13.0, *) {
                        imageView.image = UIImage(systemName: "photo")
                    }
                }
                return
            }
            
            if let data = data, let image = UIImage(data: data) {
                DispatchQueue.main.async {
                    imageView.image = image
                }
            }
        }
        task.resume()
    }
}

// MARK: - 自定义UI视图
class CustomUIView: UIView {
    private(set) var imageUrl: String!
    private(set) var title: String!
    private(set) var desc: String!
    private(set) var iconUrl: String!
    private var logoUrl: String?
    private(set) var logoName: String!
    private var actionText: String?
    private(set) var cWidth: CGFloat!
    private(set) var cHeight: CGFloat!
    
    // 组件声明
    private let leftRedView = UIImageView()
    private let rightContainer = UIView()
    private let titleLabel = UILabel()
    private let descriptionLabel = UILabel()
    private let logoCircle = UIImageView()
    private let additional = UIView()
    private let additionalLabel = UILabel()
    private let rightAdIconView = UIImageView()
    private let closeButton = UIButton(type: .custom)
    init(imageUrl: String, title: String, desc: String, iconUrl: String, logoUrl: String? = nil, actionText: String? = nil, width: CGFloat, height: CGFloat, name: String) {
        self.imageUrl = imageUrl
        self.title = title
        self.desc = desc
        self.iconUrl = iconUrl
        self.logoUrl = logoUrl
        self.actionText = actionText
        self.cWidth = width
        self.cHeight = height
        self.logoName = name
        super.init(frame: .zero)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    // 配置视图
    private func setupView() {
        // 设置整体尺寸
        self.translatesAutoresizingMaskIntoConstraints = false
        self.layer.cornerRadius = 10
        self.clipsToBounds = true
        NSLayoutConstraint.activate([
            self.widthAnchor.constraint(equalToConstant: cWidth),
            self.heightAnchor.constraint(equalToConstant: cHeight)
        ])
        self.backgroundColor = UIColor(hex: "#FF828B91")
        NetworkImageLoader.loadImage(from: imageUrl, into: leftRedView)
        addSubview(leftRedView)
        leftRedView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            leftRedView.leadingAnchor.constraint(equalTo: leadingAnchor),
            leftRedView.topAnchor.constraint(equalTo: topAnchor),
            leftRedView.bottomAnchor.constraint(equalTo: bottomAnchor),
            leftRedView.widthAnchor.constraint(equalToConstant: 158)
        ])
        
        // 右边容器
        rightContainer.backgroundColor = UIColor(hex: "#FF828B91")
        addSubview(rightContainer)
        rightContainer.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            rightContainer.leadingAnchor.constraint(equalTo: leftRedView.trailingAnchor, constant: 10),
            rightContainer.trailingAnchor.constraint(equalTo: trailingAnchor),
            rightContainer.topAnchor.constraint(equalTo: topAnchor),
            rightContainer.bottomAnchor.constraint(equalTo: bottomAnchor)
        ])
        
        // 第一行 - 标题
        let firstLine = UIStackView()
        firstLine.axis = .horizontal
        firstLine.distribution = .fill
        
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 14, weight: .bold)
        firstLine.addArrangedSubview(titleLabel)
        firstLine.addArrangedSubview(UIView()) // 弹簧视图
          
        closeButton.setImage(UIImage(named: "icon_close_write"), for: .normal)
        closeButton.addTarget(self, action: #selector(closeButtonTapped), for: .touchUpInside)
        NSLayoutConstraint.activate([
            closeButton.widthAnchor.constraint(equalToConstant: 20),
            closeButton.heightAnchor.constraint(equalToConstant: 20)
        ])
        firstLine.addArrangedSubview(closeButton)
          
        // 第二行 - 描述
        descriptionLabel.text = desc
        descriptionLabel.font = UIFont.systemFont(ofSize: 12)
        descriptionLabel.setContentHuggingPriority(.defaultHigh, for: .vertical)
        descriptionLabel.setContentCompressionResistancePriority(.defaultHigh, for: .vertical)
        descriptionLabel.numberOfLines = 2
        descriptionLabel.lineBreakMode = .byTruncatingTail
        descriptionLabel.setContentHuggingPriority(.defaultLow, for: .vertical)
        descriptionLabel.setContentCompressionResistancePriority(.defaultLow, for: .vertical)
        descriptionLabel.adjustsFontSizeToFitWidth = false
        
        // 第三行
        let thirdLine = UIStackView()
        thirdLine.axis = .horizontal
        thirdLine.alignment = .center
        thirdLine.spacing = 8
        
        logoCircle.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            logoCircle.widthAnchor.constraint(equalToConstant: 20),
            logoCircle.heightAnchor.constraint(equalToConstant: 20)
        ])
        NetworkImageLoader.loadImage(from: iconUrl, into: logoCircle)
        logoCircle.layer.cornerRadius = 10
        logoCircle.clipsToBounds = true
        thirdLine.addArrangedSubview(logoCircle)
        
        additional.layer.cornerRadius = 10
        additional.clipsToBounds = true
        additional.backgroundColor = .white
        additional.layoutMargins = UIEdgeInsets(top: 3, left: 17, bottom: 3, right: 17)
        additionalLabel.text = actionText
        additionalLabel.textColor = .black
        additionalLabel.font = UIFont.systemFont(ofSize: 12)
        additional.addSubview(additionalLabel)
        
        additionalLabel.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            additionalLabel.leadingAnchor.constraint(equalTo: additional.layoutMarginsGuide.leadingAnchor),
            additionalLabel.trailingAnchor.constraint(equalTo: additional.layoutMarginsGuide.trailingAnchor),
            additionalLabel.topAnchor.constraint(equalTo: additional.layoutMarginsGuide.topAnchor),
            additionalLabel.bottomAnchor.constraint(equalTo: additional.layoutMarginsGuide.bottomAnchor)
        ])
        thirdLine.addArrangedSubview(additional)
        thirdLine.addArrangedSubview(UIView()) // 弹簧视图
        
        if let logoUrl = logoUrl, !logoUrl.isEmpty {
            NetworkImageLoader.loadImage(from: logoUrl, into: rightAdIconView)
            rightAdIconView.layoutMargins = UIEdgeInsets(top: 2, left: 2, bottom: 2, right: 2)
            rightAdIconView.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                rightAdIconView.widthAnchor.constraint(equalToConstant: 20),
                rightAdIconView.heightAnchor.constraint(equalToConstant: 20)
            ])
            thirdLine.addArrangedSubview(rightAdIconView)
        } else {
            let textLogo = UILabel()
            textLogo.text = "\(logoName!)广告"
            textLogo.font = UIFont.systemFont(ofSize: 8, weight: .medium)
            thirdLine.addArrangedSubview(textLogo)
        }
        
        // 整体垂直布局
        let verticalStack = UIStackView()
        verticalStack.axis = .vertical
        verticalStack.spacing = 5
        verticalStack.alignment = .fill
        verticalStack.distribution = .fill
        
        let spacerView = UIView()
        spacerView.translatesAutoresizingMaskIntoConstraints = false
        spacerView.setContentHuggingPriority(.defaultLow, for: .vertical)
        spacerView.setContentCompressionResistancePriority(.defaultLow, for: .vertical)
        
        verticalStack.addArrangedSubview(firstLine)
        verticalStack.addArrangedSubview(descriptionLabel)
        verticalStack.addArrangedSubview(spacerView)
        verticalStack.addArrangedSubview(thirdLine)
        verticalStack.layoutMargins = UIEdgeInsets(top: 0, left: 0, bottom: 9, right: 0)
        verticalStack.isLayoutMarginsRelativeArrangement = true
        
        rightContainer.addSubview(verticalStack)
        verticalStack.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            verticalStack.leadingAnchor.constraint(equalTo: rightContainer.leadingAnchor, constant: 5),
            verticalStack.trailingAnchor.constraint(equalTo: rightContainer.trailingAnchor, constant: -5),
            verticalStack.topAnchor.constraint(equalTo: rightContainer.topAnchor, constant: 5),
            verticalStack.bottomAnchor.constraint(equalTo: rightContainer.bottomAnchor, constant: -5)
        ])
    }
    
    @objc private func closeButtonTapped() {
        print("关闭按钮被点击")
        BeiziAdSdkPlugin.flutterChannel?.invokeMethod("nativeAdOnTapClose", arguments: "信息流广告点击关闭")
         // 可以添加代理或闭包回调
     }
    
}

// MARK: - UIColor扩展
extension UIColor {
    convenience init?(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet(charactersIn: "#").union(.whitespaces))
        
        var hexNumber: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&hexNumber)
        
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (hexNumber >> 8) * 17, (hexNumber >> 4 & 0xF) * 17, (hexNumber & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, hexNumber >> 16, hexNumber >> 8 & 0xFF, hexNumber & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (hexNumber >> 24, hexNumber >> 16 & 0xFF, hexNumber >> 8 & 0xFF, hexNumber & 0xFF)
        default:
            return nil
        }
        
        self.init(
            red: CGFloat(r) / 255,
            green: CGFloat(g) / 255,
            blue: CGFloat(b) / 255,
            alpha: CGFloat(a) / 255
        )
    }
} 
