import 'dart:async';
import 'dart:io';

import 'package:api/api.dart';
import 'package:component_library/component_library.dart';
import 'package:domain_models/domain_models.dart';
import 'package:flutter/widgets.dart';
import 'package:mine/mine.dart';
import 'package:push_repository/push_repository.dart';
import 'package:tools/tools.dart';
import 'package:user_repository/src/mappers/domain_to_cache.dart';
import 'package:user_repository/src/mappers/mappers.dart';
import 'package:key_value_storage/key_value_storage.dart';
import 'package:meta/meta.dart';
import 'package:rxdart/rxdart.dart';
import 'package:user_repository/src/models/user_account_dm.dart';
import 'package:user_repository/src/user_local_storage.dart';
import 'package:user_repository/src/user_secure_storage.dart';
import 'package:get/get.dart';

class UserRepository {
  UserRepository({
    required KeyValueStorage noSqlStorage,
    required this.remoteApi,
    required PushRepository pushRepository,
    @visibleForTesting UserLocalStorage? localStorage,
    @visibleForTesting UserSecureStorage? secureStorage,
  })  : _localStorage = localStorage ??
            UserLocalStorage(
              noSqlStorage: noSqlStorage,
            ),
        _secureStorage = secureStorage ?? const UserSecureStorage(),
        _pushRepository = pushRepository;

  final Api remoteApi;
  final UserLocalStorage _localStorage;
  final UserSecureStorage _secureStorage;
  final PushRepository _pushRepository;
  final BehaviorSubject<DarkModePreference> _darkModePreferenceSubject =
      BehaviorSubject();
  UserAccount? _userAccountDM;
  Map studentInfo = {};

  Future<UserAccount?> get userAccountDM async {
    return _userAccountDM ??= await getUserAccountInfo();
  }

  final String cityName = 'city_name';
  final String cityCode = 'city_code';

  final BehaviorSubject<UserAccount?> _userSubject = BehaviorSubject();

  // Future<void> signIn(String userName, String password) async {
  //   try {
  //     // 用户名是否存在
  //     final exist = await remoteApi.userIsExist(userName: userName);
  //     if (!exist) {
  //       throw DomainException(code: 0, message: '用户名未注册！');
  //     }
  //     // 申请加密key
  //     final passwordKeyRM = await remoteApi.getPwdKey(client: 10);
  //
  //     // 用户名密码登录
  //     var key = passwordKeyRM.key;
  //     var secret = passwordKeyRM.secret;
  //     final aesKey = Key.fromUtf8(secret.substring(0, 16));
  //     final iv = IV.fromLength(16);
  //
  //     final encryptor = Encrypter(AES(aesKey, mode: AESMode.ecb));
  //     final encryptedPwd = encryptor
  //         .encrypt('${key}@${password}'.toUpperCase(), iv: iv)
  //         .base16
  //         .toUpperCase();
  //
  //     final token = await remoteApi.loginByUserName(
  //         client: 10, flag: userName, key: key, passWord: encryptedPwd);
  //
  //     // 存储token
  //     await _secureStorage.upsertUserInfo(
  //       username: userName,
  //       token: token.token,
  //     );
  //
  //     // 用户信息
  //     final userRM = await remoteApi.getUserInfo();
  //     final userDM = userRM.toDomainModel();
  //
  //     _userSubject.add(
  //       userDM,
  //     );
  //
  //     final userCM = userDM.toCacheModel();
  //     await _localStorage.upsertUserCM(userCM);
  //
  //     // FIXME: 这里用于测试token续期的，正式需要删除，仅仅测试目的
  //     // Future.delayed(const Duration(seconds: 2), () async {
  //     //   Api.testToken = true;
  //     //   await remoteApi.getUserInfo();
  //     //   await remoteApi.getCourseListPage(1);
  //     //   await remoteApi.getCourseListPage(1);
  //     //
  //     //   // remoteApi.getCourseListPage(1);
  //     // });
  //   } catch (exception) {
  //     if (exception is HttpsException) {
  //       throw DomainException(
  //         code: exception.exceptionCode,
  //         message: exception.message,
  //       );
  //     } else {
  //       rethrow;
  //     }
  //   }
  // }

  // Future<void> signUp(
  //   String userName,
  //   String password,
  // ) async {
  //   try {
  //     // 用户名是否存在
  //     final exist = await remoteApi.userIsExist(userName: userName);
  //     if (exist) {
  //       throw DomainException(code: 0, message: '用户名已注册！');
  //     }
  //     // 申请加密key
  //     final passwordKeyRM = await remoteApi.getPwdKey(client: 10);
  //
  //     // 用户名密码登录
  //     var key = passwordKeyRM.key;
  //     var secret = passwordKeyRM.secret;
  //     final aesKey = Key.fromUtf8(secret.substring(0, 16));
  //     final iv = IV.fromLength(16);
  //
  //     final encryptor = Encrypter(AES(aesKey, mode: AESMode.ecb));
  //     final encryptedPwd = encryptor
  //         .encrypt('${key}@${password}'.toUpperCase(), iv: iv)
  //         .base16
  //         .toUpperCase();
  //
  //     final token = await remoteApi.registerByUserName(
  //         client: 10, flag: userName, key: key, passWord: encryptedPwd);
  //
  //     // 存储token
  //     await _secureStorage.upsertUserInfo(
  //       username: userName,
  //       token: token.token,
  //     );
  //
  //     // 用户信息
  //     final userRM = await remoteApi.getUserInfo();
  //     final userDM = userRM.toDomainModel();
  //
  //     _userSubject.add(
  //       userDM,
  //     );
  //
  //     final userCM = userDM.toCacheModel();
  //     await _localStorage.upsertUserCM(userCM);
  //   } catch (exception) {
  //     if (exception is HttpsException) {
  //       throw DomainException(
  //         code: exception.exceptionCode,
  //         message: exception.message,
  //       );
  //     } else {
  //       rethrow;
  //     }
  //   }
  // }
  //
  // Future<void> signOut() async {
  //   final _userName = await _secureStorage.getUsername();
  //   await remoteApi.logout(client: 10, flag: _userName ?? '');
  //   _userSubject.add(null);
  //   await _secureStorage.deleteUserInfo();
  //   await _localStorage.deleteUserCM();
  // }

  // void clearUserInfo() {
  //   _secureStorage.deleteUserInfo();
  //   _localStorage.deleteUserCM();
  //   _userSubject.add(null);
  // }
  RxBool isExistBuyVip = false.obs;

  setVipProduct() async {
    try {
      // precacheImage(AssetImage( "assets/home_img/home_tab_vip_1_bg.png"),Get.context!);
      // precacheImage(AssetImage( "assets/home_img/home_tab_vip_2_bg.png"),Get.context!);
      // precacheImage(AssetImage("assets/home_img/home_tab_vip_3_bg.png"),Get.context!);
      // precacheImage(AssetImage( "assets/home_img/home_tab_vip_4_bg.png"),Get.context!);
      Map<String, dynamic> map = await getOpenVipProduct();
      vipProductMap = UserVipModel.fromJson(map);
      if(Get.isRegistered<MineController>()){
        Get.find<MineController>().refreshVipStatus();
      }
      if (vipProductMap.vip1) {
        await precacheImage(AssetImage( "assets/home_img/home_tab_vip_1_bg.png"),Get.context!);
      }
      if (vipProductMap.vip2) {
        await precacheImage(AssetImage( "assets/home_img/home_tab_vip_2_bg.png"),Get.context!);
      }
      if (vipProductMap.vip3) {
        await precacheImage(AssetImage("assets/home_img/home_tab_vip_3_bg.png"),Get.context!);
      }
      if (vipProductMap.vip4) {
        await precacheImage(AssetImage( "assets/home_img/home_tab_vip_4_bg.png"),Get.context!);
      }
      isBuyVip();
    } catch (e) {
      debugPrint("setVipProduct error:----------> ${e.toString()}");
    }
  }

  isBuyVip() {
    if (vipProductMap.vip1 ||
        vipProductMap.vip2 ||
        vipProductMap.vip3 ||
        vipProductMap.vip4) isExistBuyVip.value = true;
  }

  ///type
  ///1 科目三地区vip视频(包含点位图)
  ///2 科目三真实路考
  ///3 科目三3d练车
  ///4 科目三灯光模拟
  ///5 点位图
  Future<bool> isActivateVip(int type, {String? division}) async {
    if (type == 3 || type == 4) {
      if (vipProductMap.vip3) {
        return true;
      }
    }
    switch (type) {
      case 1:
        return vipProductMap.areaVideoProducts!['3'][division!] ?? false;
      case 2:
        return (vipProductMap.areaVideoProducts!['3'][division!] ?? false) &&
            (vipProductMap.vip3 ||
                (vipProductMap.subject3FunctionProducts?.realExam ?? false));
      case 3:
        return (vipProductMap.subject3FunctionProducts?.b3DPractice ?? false)
            ? true
            : await timeComparison(
                await PreferencesService()
                    .getInt('33dliancheExperienceStartTime'),
                '33dliancheExperienceStartTime');
      case 4:
        return (vipProductMap.subject3FunctionProducts?.lightingSimulation ??
                false)
            ? true
            : await timeComparison(
                await PreferencesService()
                    .getInt('dengguangmoniExperienceStartTime'),
                'dengguangmoniExperienceStartTime');

      default:
        return false;
    }
  }

//  判断点位图是否是vip
  isPointVip(String? division, String? productId) {
    return pointVipMap[productId] ?? false;
  }

//  体验时间比较
  Future<bool> timeComparison(int? tmp, String key) async {
    if (tmp == null) {
      return false;
    }
    int nowTmp = await getServerTime();
    // 将传入的时间戳转换为DateTime对象，注意时间戳是以秒为单位的
    final DateTime pastDateTime =
        DateTime.fromMillisecondsSinceEpoch(tmp * 1000, isUtc: true).toLocal();
    final DateTime nowDateTime =
        DateTime.fromMillisecondsSinceEpoch(nowTmp, isUtc: true).toLocal();
    // 计算两者之间的差异
    final Duration difference = nowDateTime.difference(pastDateTime);
    if (difference.inMinutes < 30) {
      return true;
    } else {
      PreferencesService().removeKey(key);
      return false;
    }
  }

  ///获取科目会员
  getSubVip(int type) {
    switch (type) {
      case 1:
        return vipProductMap.vip1;
      case 2:
        return vipProductMap.vip2;
      case 3:
        return vipProductMap.vip3;
      case 4:
        return vipProductMap.vip4;
      default:
        return false;
    }
  }

  //查看电子合同
  viewPdfContract() async {
    try {
      var map = await remoteApi.viewPdfContract();
      return map;
    } catch (e) {
      debugPrint("viewPdfContract error:----------> ${e.toString()}");
    }
  }

  //  获取服务器时间
  Future<int> getServerTime() async {
    try {
      final tmp = await remoteApi.getServerTime();
      return tmp;
    } catch (e) {
      if (e is HttpsException) {
        throw DomainException(code: e.exceptionCode, message: e.message);
      } else {
        rethrow;
      }
    }
  }

  ///判断权益是否存在
  /// 地区视频vip 1 地区线路vip 2
  determineWhetherEquityExists({required int type, required int district}) {}

  UserVipModel vipProductMap = UserVipModel();
  Map<String, bool> pointVipMap = {};
  cleanVipMap() {
    vipProductMap = UserVipModel();
  }

  Future<Map<String, dynamic>> getOpenVipProduct() async {
    Map<String, dynamic> map = {};
    try {
      map = await remoteApi.getOpenVipProduct();
    } catch (e) {
      if (e is HttpsException) {
        throw DomainException(code: e.exceptionCode, message: e.message);
      } else {
        rethrow;
      }
    }
    return map;
  }

  Future<Map<String, dynamic>> getApplePayId(String id) async {
    Map<String, dynamic> map = {};
    try {
      map = await remoteApi.getApplePayId(id);
    } catch (e) {
      if (e is HttpsException) {
        throw DomainException(code: e.exceptionCode, message: e.message);
      } else {
        rethrow;
      }
    }
    return map;
  }

  Future uploadAppleVoucher(
      String id, String transactionId, String receipt) async {
    try {
      await remoteApi.uploadAppleVoucher(id, transactionId, receipt);
    } catch (e) {
      if (e is HttpsException) {
        throw DomainException(code: e.exceptionCode, message: e.message);
      } else {
        rethrow;
      }
    }
  }

  Future<Map<String, dynamic>> searchApplePayStatus(String id) async {
    Map<String, dynamic> map = {};
    try {
      map = await remoteApi.searchApplePayStatus(id);
    } catch (e) {
      if (e is HttpsException) {
        throw DomainException(code: e.exceptionCode, message: e.message);
      } else {
        rethrow;
      }
    }
    return map;
  }

  Future<void> renewalToken() async {
    await _localStorage.deleteUserAccountCM();
    _userSubject.add(
      null,
    );
    try {
      final userName = await _secureStorage.getUsername();
      final oldToken = await _secureStorage.getUserToken();
      if (oldToken == null || userName == null) {
        _userSubject.add(
          null,
        );
      } else {
        final newToken = await remoteApi.refreshToken(userName);
        if (newToken.isEmpty) {
          cleanVipMap();
          await _secureStorage.deleteUserInfo();
          await _localStorage.deleteUserAccountCM();
          _userSubject.add(
            null,
          );
          await PreferencesService().setString('useTopicType', '');
          await PreferencesService().setInt('useDivision', 0);
          // ITools.get().setUserAccount(Map<String, dynamic>());
          await ITools.get().setUserAccount(Map<String, dynamic>());
          return;
        }
        // 存储token
        await _secureStorage.upsertUserInfo(
          username: userName,
          token: newToken['token'],
        );

        // 用户信息
        // final userAccountRM = await remoteApi.queryUserInfo();
        // final userDM = userAccountRM.toDoMainModels();
        //
        // _userSubject.add(
        //   userDM,
        // );
        // final userCM = userDM.doMainToCacheModel();
        // await _localStorage.upsertUserAccountCM(userCM);
        queryUserInfo(showLoading: false);
      }
    } catch (exception) {
      cleanVipMap();
      await _secureStorage.deleteUserInfo();
      await _localStorage.deleteUserAccountCM();
      _userSubject.add(
        null,
      );
      await PreferencesService().setString('useTopicType', '');
      await PreferencesService().setInt('useDivision', 0);
      await ITools.get().setUserAccount(Map<String, dynamic>());
      // ITools.get().setUserAccount(Map<String, dynamic>());
      if (exception is HttpsException) {
        throw DomainException(
          code: exception.exceptionCode,
          message: exception.message,
        );
      } else {
        rethrow;
      }
    }
  }

  //
  Stream<UserAccount?> getUserAccount() async* {
    if (!_userSubject.hasValue) {
      final userCM = await _localStorage.getUserAccount();
      if (userCM != null) {
        final user = userCM.toDoMain();
        _userSubject.add(
          user,
        );
      } else {
        _userSubject.add(
          null,
        );
      }
    }
    yield* _userSubject.stream;
  }

  void setStudentInfo(Map info) {
    studentInfo = info;
  }

  // Future refreshToken() async {
  //   Map refreshToken;
  //   try {
  //     final userName = await _secureStorage.getUsername();
  //     refreshToken = await remoteApi.refreshToken(userName!);
  //   }catch(e){
  //     debugPrint("refreshToken error:----------> ${e.toString()}");
  //     return null;
  //   }
  //
  //   return refreshToken;
  // }

  ///账号密码登录
  Future<String?> getToken(userName, passWord, key) async {
    String? result;
    try {
      result = await remoteApi.loginPassWord(
          userName: userName, passWord: passWord, key: key);
    } catch (e) {
      if (e is HttpsException) {
        throw DomainException(code: e.exceptionCode, message: e.message);
      } else {
        rethrow;
      }
    }
    return result;
  }

  ///密码登陆前获取密钥KEY
  Future<Map> getPassWordKey() async {
    Map result;
    try {
      result = await remoteApi.getPassWordKey();
    } catch (e) {
      if (e is HttpsException) {
        throw DomainException(code: e.exceptionCode, message: e.message);
      } else {
        rethrow;
      }
    }
    return result;
  }

  ///用户手机号登录
  Future<UserAccount?> phoneLogin(phone, ver) async {
    UserAccount userAccountDM;
    try {
      final mapInfo = await remoteApi.getPhoneLogin(phone, ver);
      if (mapInfo['token'] != null) {
        await _secureStorage.upsertUserInfo(
            username: phone, token: mapInfo['token']);

        final userAccountRM = await queryUserInfo();
        userAccountDM = userAccountRM.toDoMainModels();
        // final userAccountRM=await queryUserInfo();
        //
        // userAccountDM=userAccountRM.toDoMainModels();

        // final userAccountCM = userAccountDM.doMainToCacheModel();
        //
        // await _localStorage.upsertUserAccountCM(userAccountCM);

        return userAccountDM;
      }
    } catch (e) {
      if (e is HttpsException) {
        throw DomainException(code: e.exceptionCode, message: e.message);
        // Toast.show(e.message);
        // debugPrint("HttpsException error:----------> ${e.message}");
      } else {
        rethrow;
        // Toast.show(e.toString());
      }
      // return null;
    }
    return null;
  }

  ///微信号登录
  Future weChatLogin(code) async {
    final mapInfo;
    try {
      mapInfo = await remoteApi.getWeChatLogin(code);
    } catch (e) {
      if (e is HttpsException) {
        throw DomainException(code: e.exceptionCode, message: e.message);
      } else {
        rethrow;
      }
    }

    return mapInfo;
  }

  ///绑定微信号
  Future bindWeChat(code) async {
    final temp;
    try {
      temp = await remoteApi.bindWeChat(code);
    } catch (e) {
      if (e is HttpsException) {
        throw DomainException(code: e.exceptionCode, message: e.message);
      } else {
        rethrow;
      }
    }
    return temp;
  }

  ///获取验证
  Future<Map> getVer(phone) async {
    Map result;
    try {
      result = await remoteApi.getPhoneVerLogin(phone);
    } catch (e) {
      if (e is HttpsException) {
        throw DomainException(code: e.exceptionCode, message: e.message);
      } else {
        rethrow;
      }
    }
    return result;
  }

  ///Apple登录
  Future<UserAccount?> loginByApple(
      {name, email, userIdentifier, identityToken, authorizationCode}) async {
    UserAccount userAccountDM;
    try {
      final mapInfo = await remoteApi.loginWithAppleId(
          name: name,
          email: email,
          userIdentifier: userIdentifier,
          identityToken: identityToken,
          authorizationCode: authorizationCode);
      if (mapInfo['token'] != null) {
        await setUserToken(mapInfo['token']);
        // await setUserTokenInfo(authorizationCode, mapInfo['token']);
        final userAccountRM = await queryUserInfo();
        userAccountDM = userAccountRM.toDoMainModels();
        // _userSubject.add(
        //   userAccountDM,
        // );
        //
        // final userAccountCM = userAccountDM.doMainToCacheModel();
        // // final a=userAccountCM.toDoMain();
        //
        // await _localStorage.upsertUserAccountCM(userAccountCM);

        return userAccountDM;
      }
    } catch (e) {
      if (e is HttpsException) {
        throw DomainException(code: e.exceptionCode, message: e.message);
        // Toast.show(e.message);
        // debugPrint("HttpsException error:----------> ${e.message}");
      } else {
        rethrow;
        // Toast.show(e.toString());
        // debugPrint("error:----------> ${e.toString()}");
      }
      // return null;
    }
    return null;
  }

  Future<UserAccount?> getUserAccountInfo() async {
    UserAccount? userAccount;
    try {
      UserAccountCM? userAccountCM = await _localStorage.getUserAccount();
      userAccount = userAccountCM?.toDoMain();
    } catch (e) {
      return null;
    }
    return userAccount;
  }

  ///绑定手机号（一键验证）
  Future bindPhoneOneKey(accessToken) async {
    Map result;
    try {
      result = await remoteApi.bindPhoneWithOneKey(accessToken);
    } catch (e) {
      if (e is HttpsException) {
        throw DomainException(code: e.exceptionCode, message: e.message);
      } else {
        rethrow;
      }
    }

    return result;
  }

  ///绑定手机号（手机验证码验证）
  Future bindPhoneWithMobileVerify(String phone, String code) async {
    Map result;
    try {
      result = await remoteApi.bindPhoneWithMobileVerify(phone, code);
    } catch (e) {
      if (e is HttpsException) {
        throw DomainException(code: e.exceptionCode, message: e.message);
      } else {
        rethrow;
      }
    }
    return result;
  }


  ///绑定手机号
  Future vXbindPhoneOneKey({
    String? mobile,
    String? code,
    required String accessToken,
    required String openId,
    required int isSimple,
    String? aliAccessToken,
  }) async {
    Map result;
    try {
      result = await remoteApi.vXPhoneWithOneKey(
          accessToken: accessToken,
          openId: openId,
          isSimple: isSimple,
          aliAccessToken: aliAccessToken,
          code: code,
          mobile: mobile);
      return result;
    } catch (e) {
      if (e is HttpsException) {
        throw DomainException(code: e.exceptionCode, message: e.message);
      } else {
        rethrow;
      }
    }
  }

  ///查询用户账号信息
  Future<UserAccountRm> queryUserInfo({bool showLoading = true,bool isNeedUpdateInfo = true}) async {
    final UserAccountRm userAccountRM;
    try {
      userAccountRM = await remoteApi.queryUserInfo(showLoading: showLoading,isNeedUpdateInfo: isNeedUpdateInfo);
      final userAccountDM = userAccountRM.toDoMainModels();

      if (userAccountDM.bindMobile != null && userAccountDM.bindMobile != '') {
        await setUserName(userAccountDM.bindMobile!);
      }

      // dxjd
      // await queryDxjdToken();
      // ITools.get().setUserAccount(userAccountDM);

      _userSubject.add(
        userAccountDM,
      );
      final userAccountCM = userAccountDM.doMainToCacheModel();

      await _localStorage.upsertUserAccountCM(userAccountCM);
      _userAccountDM = userAccountCM.toDoMain();
      checkOrRegisterPushId();
    } catch (e) {
      if (e is HttpsException) {
        throw DomainException(code: e.exceptionCode, message: e.message);
      } else {
        rethrow;
      }
    }
    return userAccountRM;
  }

  void checkOrRegisterPushId() async {
    final register = await _pushRepository.getDeviceToken();

    final result = await queryPushData();
    if (result != null &&
        result['PushCategory'] != null &&
        result['PushCategory'] != '') {
      int category = result['PushCategory'];
      int platform = Platform.isAndroid ? 2 : 1;
      if (category == platform && result['PushIdAndroid'] == register ||
          result['PushIdIos'] == register) {
        debugPrint('register=== success:-----$register');
      } else {
        await registerPushId(pushId: register);
      }
    }
  }

  ///修改用户账号密码
  Future modifyUserPassword(String oldPassword, String newPassword) async {
    try {
      Map keyMap = await getPassWordKey();
      final result = await remoteApi.modifyUserPassword(
          keyMap['key'],
          EncryptUtils.aesEncrypt(oldPassword, keyMap['key'], keyMap['secret']),
          EncryptUtils.aesEncrypt(
              newPassword, keyMap['key'], keyMap['secret']));
      return result;
    } catch (e) {
      if (e is HttpsException) {
        throw DomainException(code: e.exceptionCode, message: e.message);
      } else {
        rethrow;
      }
    }
  }

  ///重置用户密码（忘记密码）
  Future resetPassword(
      {required String phone,
      required String ver,
      required String account,
      required String passWord}) async {
    try {
      Map keyMap = await getPassWordKey();
      final result = await remoteApi.resetPassword(keyMap['key'],
          phone: phone,
          ver: ver,
          account: account,
          passWord: EncryptUtils.aesEncrypt(
              passWord, keyMap['key'], keyMap['secret']));
      return result;
    } catch (e) {
      if (e is HttpsException) {
        throw DomainException(code: e.exceptionCode, message: e.message);
      } else {
        rethrow;
      }
    }
  }

  ///查询计时平台学员档案
  Future<Map> queryStudentDoc(String id) async {
    final Map result;
    try {
      result = await remoteApi.queryStudentDoc(id);
    } catch (e) {
      if (e is HttpsException) {
        throw DomainException(code: e.exceptionCode, message: e.message);
      } else {
        rethrow;
      }
    }
    return result;
  }

  ///绑定计时平台学员档案
  Future<Map> bindStudentDoc(String id) async {
    final result;
    try {
      result = await remoteApi.bindStudentDoc(id);
    } catch (e) {
      if (e is HttpsException) {
        throw DomainException(code: e.exceptionCode, message: e.message);
      } else {
        rethrow;
      }
    } finally {
      queryUserInfo();
    }
    return result;
  }

  ///退出登录
  Future<Map> loginOut() async {
    var result;
    _userAccountDM = null;
    try {
      final userName = await _secureStorage.getUsername();
      result = await remoteApi.loginOut(userName ?? '');
      await _secureStorage.deleteUserInfo();
      await _localStorage.deleteUserAccountCM();
      _userSubject.add(
        null,
      );
      isExistBuyVip.value = false;
    } catch (e) {
      await _secureStorage.deleteUserInfo();
      await _localStorage.deleteUserAccountCM();
      result = null;
      _userSubject.add(
        null,
      );
      rethrow;
    }
    return result;
  }

  ///更新学员培训信息
  Future<Map> updateStudentTrainType(String topicType, int division) async {
    final upData;
    try {
      upData = await remoteApi.updateStudentTrainType(topicType, division);
    } catch (e) {
      if (e is HttpsException) {
        throw DomainException(code: e.exceptionCode, message: e.message);
      } else {
        rethrow;
      }
    } finally {
      queryUserInfo();
    }
    return upData;
  }

  ///更新学员头像
  Future<bool> updateUserAvatar(
    String url,
  ) async {
    try {
      await remoteApi.updateUserAvatar(
        url,
      );
      return true;
    } catch (e) {
      if (e is HttpsException) {
        throw DomainException(code: e.exceptionCode, message: e.message);
      } else {
        rethrow;
      }
    }
  }

  ///用户注销
  Future<Map?> logoff(mobile, verifyCode) async {
    final temp;
    try {
      temp = await remoteApi.logoff(mobile, verifyCode);
      // await queryUserInfo();
      await _secureStorage.deleteUserInfo();
      await _localStorage.deleteUserAccountCM();
      isExistBuyVip.value = false;
    } catch (e) {
      if (e is HttpsException) {
        throw DomainException(code: e.exceptionCode, message: e.message);
      } else {
        rethrow;
      }
      // return null;
    }
    return temp;
  }

  Map accountInfo = {};
  setAccountInfo(Map info) {
    accountInfo = info;
  }

  ///查询用户账户开通信息
  Future<Map> queryUserAccountOpenInfo() async {
    Map result;
    try {
      result = await remoteApi.queryUserAccountOpenInfo();
    } catch (e) {
      if (e is HttpsException) {
        throw DomainException(code: e.exceptionCode, message: e.message);
      } else {
        rethrow;
      }
    }
    return result;
  }

  ///商城支付获取支付凭证
  Future<Map?> getOrderId({orderId}) async {
    final temp;
    try {
      temp = await remoteApi.getShopPayToken(orderId: orderId);
    } catch (e) {
      if (e is HttpsException) {
        throw DomainException(code: e.exceptionCode, message: e.message);
      } else {
        rethrow;
      }
      // return null;
    }
    return temp;
  }

  ///刷新学员档案
  Future refreshStudentDoc() async {
    try {
      await remoteApi.refreshStudentDoc();
    } catch (e) {
      if (e is HttpsException) {
        throw DomainException(code: e.exceptionCode, message: e.message);
      } else {
        rethrow;
      }
    }
  }

  ///查询学习证明上传状态
  Future<Map?> getStudyProveUploadStatus({subjectId}) async {
    final temp;
    try {
      temp =
          await remoteApi.getStudyProveUploadStatus(subjectId: subjectId ?? 1);
    } catch (e) {
      if (e is HttpsException) {
        throw DomainException(code: e.exceptionCode, message: e.message);
      } else {
        rethrow;
      }
      // return null;
    }
    return temp;
  }

  ///注册推送
  Future<Map?> registerPushId({pushId}) async {
    final temp;
    try {
      temp = await remoteApi.registerPushId(pushId: pushId ?? '');
    } catch (e) {
      if (e is HttpsException) {
        throw DomainException(code: e.exceptionCode, message: e.message);
      } else {
        rethrow;
      }
      // return null;
    }
    return temp;
  }

  ///查询推送数据
  Future<Map?> queryPushData() async {
    final temp;
    try {
      temp = await remoteApi.queryPushData();
    } catch (e) {
      if (e is HttpsException) {
        throw DomainException(code: e.exceptionCode, message: e.message);
      } else {
        rethrow;
      }
      // return null;
    }
    return temp;
  }

  Future<String?> getUserToken() {
    return _secureStorage.getUserToken();
  }

  Future<String?> getUserName() {
    return _secureStorage.getUsername();
  }

  Future setUserToken(String token) async {
    return _secureStorage.setUserToken(token);
  }

  Future setUserName(String name) {
    return _secureStorage.setUserName(name);
  }

  Future setUserTokenInfo(String phone, String? token) {
    return _secureStorage.upsertUserInfo(username: phone, token: token);
  }

  //本地存储判断是否注册pushId
  final String isRegister = 'isRegister';
  Future<bool> isRegisterPushId() async {
    final prefs = PreferencesService();
    return await prefs.getBool(isRegister) ?? false;
  }

  Future setIsRegisterPushId(bool isR) async {
    final prefs = PreferencesService();
    await prefs.setBool(isRegister, isR);
  }

  // Future<void> setCityAndDivision(String city,String division) async {
  //   final prefs = PreferencesService();
  //
  //   await prefs.setString(cityName, city);
  //   await prefs.setString(cityCode, division);
  // }
  //
  // Future<String?> getCity() async {
  //   final prefs = PreferencesService();
  //   return await prefs.getString(cityName);
  // }
  //
  // Future<String> getCityDivision() async {
  //   final prefs = PreferencesService();
  //   return await prefs.getString(cityCode)??'';
  // }

  Future deleteSecureStorage() {
    return _secureStorage.deleteAll();
  }

  Future<bool> isAutoLogin() async {
    final _token = await getUserToken();
    return _token != null;
  }

  Stream<DarkModePreference> getDarkModePreference() async* {
    if (!_darkModePreferenceSubject.hasValue) {
      final storedPreference = await _localStorage.getDarkModePreference();
      _darkModePreferenceSubject.add(
        storedPreference?.toDomainModel() ??
            DarkModePreference.useSystemSettings,
      );
    }

    yield* _darkModePreferenceSubject.stream;
  }
}

// 8- 14
extension dxjd on UserRepository {
  // 获取dxjd token
  Future<String?> queryDxjdToken() async {
    String? token;
    for (var i = 0; i < 5; i++) {
      try {
        final mobile = await getUserName();
        token = await remoteApi.getDxjdToken(mobile: mobile ?? '');
        if (token != null && token.isNotEmpty) {
          HttpDao.get().setToken(token);
          await setDxjdUserToken(token);
          debugPrint('==================== dxjd token: $token');
          break;
        }
      } catch (e) {
        debugPrint(e.toString());
      }
      await Future.delayed(const Duration(seconds: 1));
    }
    return token;
  }

  // 刷新dxjd token
  Future<String?> refreshDxjdToken() async {
    String? token;
    for (var i = 0; i < 5; i++) {
      try {
        final mobile = await getUserName();
        final oldToken = await getDxjdUserToken();
        token = await remoteApi.renewDxjdToken(
            mobile: mobile ?? '', token: oldToken ?? '');
        if (token != null && token.isNotEmpty) {
          HttpDao.get().setToken(token);
          await setDxjdUserToken(token);
          break;
        }
      } catch (e) {
        debugPrint(e.toString());
      }
      await Future.delayed(const Duration(seconds: 1));
    }
    return token;
  }

  // set dxjd token
  Future setDxjdUserToken(String token) {
    return _secureStorage.setDxjdUserToken(token);
  }

  // 获取dxjd token
  Future<String?> getDxjdUserToken() {
    return _secureStorage.getDxjdUserToken();
  }
}
