import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../domain/entities/question_entity.dart';
import '../../../domain/entities/exercise_progress.dart';
import '../../../domain/usecases/load_questions.dart' as usecases;
import '../../../domain/usecases/answer_question.dart' as usecases;
import '../../../domain/usecases/save_progress.dart' as usecases;
import '../../../domain/repositories/question_repository.dart';
import 'exercise_event.dart';
import 'exercise_state.dart';

/// BLoC for managing exercise state
class ExerciseBloc extends Bloc<ExerciseEvent, ExerciseState> {
  ExerciseBloc({
    required usecases.LoadQuestions loadQuestions,
    required usecases.AnswerQuestion answerQuestion,
    required usecases.SaveProgress saveProgress,
    required QuestionRepository questionRepository,
  })  : _loadQuestions = loadQuestions,
        _answerQuestion = answerQuestion,
        _saveProgress = saveProgress,
        _questionRepository = questionRepository,
        super(const ExerciseInitial()) {
    on<LoadQuestions>(_onLoadQuestions);
    on<AnswerQuestion>(_onAnswerQuestion);
    on<NextQuestion>(_onNextQuestion);
    on<PreviousQuestion>(_onPreviousQuestion);
    on<JumpToQuestion>(_onJumpToQuestion);
    on<ToggleCollection>(_onToggleCollection);
    on<DeleteQuestion>(_onDeleteQuestion);
    on<SubmitExercise>(_onSubmitExercise);
    on<ResetExercise>(_onResetExercise);
    on<LoadErrorQuestions>(_onLoadErrorQuestions);
    on<LoadCollectedQuestions>(_onLoadCollectedQuestions);
  }

  final usecases.LoadQuestions _loadQuestions;
  final usecases.AnswerQuestion _answerQuestion;
  final usecases.SaveProgress _saveProgress;
  final QuestionRepository _questionRepository;

  /// Handle loading questions
  Future<void> _onLoadQuestions(
    LoadQuestions event,
    Emitter<ExerciseState> emit,
  ) async {
    emit(const ExerciseLoading());

    try {
      final params = usecases.LoadQuestionsParams(
        subject: event.subject,
        type: event.type,
        ids: event.ids,
        sortId: event.sortId,
        mainId: event.mainId,
        childId: event.childId,
        luid: event.luid,
      );

      final questions = await _loadQuestions(params);

      if (questions.isEmpty) {
        emit(const ExerciseError('No questions found'));
        return;
      }

      final progress = ExerciseProgress(
        subject: event.subject,
        type: event.type,
        currentIndex: 0,
        totalQuestions: questions.length,
        correctCount: 0,
        wrongCount: 0,
        answeredQuestions: const {},
        startTime: DateTime.now(),
      );

      emit(ExerciseLoaded(
        questions: questions,
        currentIndex: 0,
        progress: progress,
        canGoNext: questions.length > 1,
        canGoPrevious: false,
      ));
    } catch (e) {
      emit(ExerciseError('Failed to load questions: ${e.toString()}'));
    }
  }

  /// Handle answering a question
  Future<void> _onAnswerQuestion(
    AnswerQuestion event,
    Emitter<ExerciseState> emit,
  ) async {
    final currentState = state;
    if (currentState is! ExerciseLoaded) return;

    if (event.questionIndex < 0 ||
        event.questionIndex >= currentState.questions.length) {
      return;
    }

    try {
      final question = currentState.questions[event.questionIndex];

      // Don't allow re-answering
      if (question.isAnswered) return;

      final params = usecases.AnswerQuestionParams(
        question: question,
        userAnswer: event.answer,
        subject: currentState.progress.subject,
      );

      final result = await _answerQuestion(params);

      // Update question with answer
      final updatedQuestion = question.copyWith(
        userAnswer: event.answer,
        isAnswered: true,
        isCorrect: result.isCorrect,
        answerCount: question.answerCount + 1,
        correctCount: result.isCorrect
            ? question.correctCount + 1
            : question.correctCount,
        errorCount:
            !result.isCorrect ? question.errorCount + 1 : question.errorCount,
      );

      // Update questions list
      final updatedQuestions =
          List<QuestionEntity>.from(currentState.questions);
      updatedQuestions[event.questionIndex] = updatedQuestion;

      // Update progress
      final updatedAnswers =
          Map<int, String>.from(currentState.progress.answeredQuestions);
      updatedAnswers[question.id] = event.answer;

      final updatedProgress = currentState.progress.copyWith(
        correctCount: result.isCorrect
            ? currentState.progress.correctCount + 1
            : currentState.progress.correctCount,
        wrongCount: !result.isCorrect
            ? currentState.progress.wrongCount + 1
            : currentState.progress.wrongCount,
        answeredQuestions: updatedAnswers,
      );

      // Save progress
      await _saveProgress(usecases.SaveProgressParams(
        type: usecases.ProgressType.exercise,
        exerciseProgress: updatedProgress,
      ));

      emit(QuestionAnswered(
        questions: updatedQuestions,
        currentIndex: currentState.currentIndex,
        progress: updatedProgress,
        isCorrect: result.isCorrect,
        correctAnswer: result.correctAnswer,
        explanation: result.explanation,
        skills: result.skills,
        autoAdvance: result.isCorrect, // Auto advance on correct answer
      ));

      // Auto advance to next question if correct (after a delay)
      if (result.isCorrect &&
          currentState.currentIndex < updatedQuestions.length - 1) {
        await Future.delayed(const Duration(milliseconds: 1500));
        add(const NextQuestion());
      }
    } catch (e) {
      emit(ExerciseError('Failed to answer question: ${e.toString()}'));
    }
  }

  /// Handle next question navigation
  void _onNextQuestion(
    NextQuestion event,
    Emitter<ExerciseState> emit,
  ) {
    final currentState = state;
    if (currentState is ExerciseLoaded || currentState is QuestionAnswered) {
      final questions = currentState is ExerciseLoaded
          ? currentState.questions
          : (currentState as QuestionAnswered).questions;
      final currentIndex = currentState is ExerciseLoaded
          ? currentState.currentIndex
          : (currentState as QuestionAnswered).currentIndex;
      final progress = currentState is ExerciseLoaded
          ? currentState.progress
          : (currentState as QuestionAnswered).progress;

      if (currentIndex < questions.length - 1) {
        final newIndex = currentIndex + 1;
        emit(ExerciseLoaded(
          questions: questions,
          currentIndex: newIndex,
          progress: progress.copyWith(currentIndex: newIndex),
          canGoNext: newIndex < questions.length - 1,
          canGoPrevious: true,
        ));
      }
    }
  }

  /// Handle previous question navigation
  void _onPreviousQuestion(
    PreviousQuestion event,
    Emitter<ExerciseState> emit,
  ) {
    final currentState = state;
    if (currentState is ExerciseLoaded || currentState is QuestionAnswered) {
      final questions = currentState is ExerciseLoaded
          ? currentState.questions
          : (currentState as QuestionAnswered).questions;
      final currentIndex = currentState is ExerciseLoaded
          ? currentState.currentIndex
          : (currentState as QuestionAnswered).currentIndex;
      final progress = currentState is ExerciseLoaded
          ? currentState.progress
          : (currentState as QuestionAnswered).progress;

      if (currentIndex > 0) {
        final newIndex = currentIndex - 1;
        emit(ExerciseLoaded(
          questions: questions,
          currentIndex: newIndex,
          progress: progress.copyWith(currentIndex: newIndex),
          canGoNext: true,
          canGoPrevious: newIndex > 0,
        ));
      }
    }
  }

  /// Handle jump to specific question
  void _onJumpToQuestion(
    JumpToQuestion event,
    Emitter<ExerciseState> emit,
  ) {
    final currentState = state;
    if (currentState is ExerciseLoaded || currentState is QuestionAnswered) {
      final questions = currentState is ExerciseLoaded
          ? currentState.questions
          : (currentState as QuestionAnswered).questions;
      final progress = currentState is ExerciseLoaded
          ? currentState.progress
          : (currentState as QuestionAnswered).progress;

      if (event.index >= 0 && event.index < questions.length) {
        emit(ExerciseLoaded(
          questions: questions,
          currentIndex: event.index,
          progress: progress.copyWith(currentIndex: event.index),
          canGoNext: event.index < questions.length - 1,
          canGoPrevious: event.index > 0,
        ));
      }
    }
  }

  /// Handle toggle collection
  Future<void> _onToggleCollection(
    ToggleCollection event,
    Emitter<ExerciseState> emit,
  ) async {
    final currentState = state;
    if (currentState is! ExerciseLoaded) return;

    try {
      final question = currentState.questions[event.questionIndex];
      final newCollectionStatus = !question.isCollected;

      await _questionRepository.toggleCollection(
        questionId: question.id,
        isCollected: newCollectionStatus,
      );

      final updatedQuestion = question.copyWith(
        isCollected: newCollectionStatus,
      );

      final updatedQuestions =
          List<QuestionEntity>.from(currentState.questions);
      updatedQuestions[event.questionIndex] = updatedQuestion;

      emit(QuestionCollectionToggled(
        questions: updatedQuestions,
        currentIndex: currentState.currentIndex,
        progress: currentState.progress,
        isCollected: newCollectionStatus,
      ));
    } catch (e) {
      emit(ExerciseError('Failed to toggle collection: ${e.toString()}'));
    }
  }

  /// Handle delete question
  Future<void> _onDeleteQuestion(
    DeleteQuestion event,
    Emitter<ExerciseState> emit,
  ) async {
    final currentState = state;
    if (currentState is! ExerciseLoaded) return;

    try {
      final question = currentState.questions[event.questionIndex];

      await _questionRepository.deleteQuestion(
        questionId: question.id,
        type: currentState.progress.type,
      );

      final updatedQuestions =
          List<QuestionEntity>.from(currentState.questions);
      updatedQuestions.removeAt(event.questionIndex);

      if (updatedQuestions.isEmpty) {
        emit(const ExerciseError('No more questions available'));
        return;
      }

      // Adjust current index if necessary
      var newIndex = currentState.currentIndex;
      if (newIndex >= updatedQuestions.length) {
        newIndex = updatedQuestions.length - 1;
      }

      final updatedProgress = currentState.progress.copyWith(
        currentIndex: newIndex,
        totalQuestions: updatedQuestions.length,
      );

      emit(QuestionDeleted(
        questions: updatedQuestions,
        currentIndex: newIndex,
        progress: updatedProgress,
      ));
    } catch (e) {
      emit(ExerciseError('Failed to delete question: ${e.toString()}'));
    }
  }

  /// Handle submit exercise
  Future<void> _onSubmitExercise(
    SubmitExercise event,
    Emitter<ExerciseState> emit,
  ) async {
    final currentState = state;
    if (currentState is! ExerciseLoaded) return;

    try {
      final finalProgress = currentState.progress.copyWith(
        endTime: DateTime.now(),
      );

      await _saveProgress(usecases.SaveProgressParams(
        type: usecases.ProgressType.exercise,
        exerciseProgress: finalProgress,
      ));

      emit(ExerciseCompleted(
        progress: finalProgress,
        questions: currentState.questions,
      ));
    } catch (e) {
      emit(ExerciseError('Failed to submit exercise: ${e.toString()}'));
    }
  }

  /// Handle reset exercise
  void _onResetExercise(
    ResetExercise event,
    Emitter<ExerciseState> emit,
  ) {
    emit(const ExerciseInitial());
  }

  /// Handle load error questions
  Future<void> _onLoadErrorQuestions(
    LoadErrorQuestions event,
    Emitter<ExerciseState> emit,
  ) async {
    emit(const ExerciseLoading());

    try {
      final questions = await _questionRepository.getErrorQuestions(
        subject: event.subject,
      );

      if (questions.isEmpty) {
        emit(const ExerciseError('No error questions found'));
        return;
      }

      final progress = ExerciseProgress(
        subject: event.subject,
        type: 'error',
        currentIndex: 0,
        totalQuestions: questions.length,
        correctCount: 0,
        wrongCount: 0,
        answeredQuestions: const {},
        startTime: DateTime.now(),
      );

      emit(ExerciseLoaded(
        questions: questions,
        currentIndex: 0,
        progress: progress,
        canGoNext: questions.length > 1,
        canGoPrevious: false,
      ));
    } catch (e) {
      emit(ExerciseError('Failed to load error questions: ${e.toString()}'));
    }
  }

  /// Handle load collected questions
  Future<void> _onLoadCollectedQuestions(
    LoadCollectedQuestions event,
    Emitter<ExerciseState> emit,
  ) async {
    emit(const ExerciseLoading());

    try {
      final questions = await _questionRepository.getCollectedQuestions(
        subject: event.subject,
      );

      if (questions.isEmpty) {
        emit(const ExerciseError('No collected questions found'));
        return;
      }

      final progress = ExerciseProgress(
        subject: event.subject,
        type: 'collect',
        currentIndex: 0,
        totalQuestions: questions.length,
        correctCount: 0,
        wrongCount: 0,
        answeredQuestions: const {},
        startTime: DateTime.now(),
      );

      emit(ExerciseLoaded(
        questions: questions,
        currentIndex: 0,
        progress: progress,
        canGoNext: questions.length > 1,
        canGoPrevious: false,
      ));
    } catch (e) {
      emit(
          ExerciseError('Failed to load collected questions: ${e.toString()}'));
    }
  }
}
