import 'package:equatable/equatable.dart';

/// Base class for mode switch events
abstract class ModeSwitchEvent extends Equatable {
  const ModeSwitchEvent();

  @override
  List<Object?> get props => [];
}

/// Event to switch to exercise mode
class SwitchToExercise extends ModeSwitchEvent {
  const SwitchToExercise({
    this.confirmationRequired = false,
    this.reason,
  });

  final bool confirmationRequired;
  final String? reason;

  @override
  List<Object?> get props => [confirmationRequired, reason];
}

/// Event to switch to listen mode
class SwitchToListen extends ModeSwitchEvent {
  const SwitchToListen({
    this.confirmationRequired = false,
    this.reason,
  });

  final bool confirmationRequired;
  final String? reason;

  @override
  List<Object?> get props => [confirmationRequired, reason];
}

/// Event to switch to browse mode
class SwitchToBrowse extends ModeSwitchEvent {
  const SwitchToBrowse({
    this.confirmationRequired = false,
    this.reason,
  });

  final bool confirmationRequired;
  final String? reason;

  @override
  List<Object?> get props => [confirmationRequired, reason];
}

/// Event to switch to video mode
class SwitchToVideo extends ModeSwitchEvent {
  const SwitchToVideo();
}

/// Event to confirm mode switch
class ConfirmModeSwitch extends ModeSwitchEvent {
  const ConfirmModeSwitch({
    required this.targetMode,
  });

  final ExerciseMode targetMode;

  @override
  List<Object> get props => [targetMode];
}

/// Event to cancel mode switch
class CancelModeSwitch extends ModeSwitchEvent {
  const CancelModeSwitch();
}

/// Event to initialize mode
class InitializeMode extends ModeSwitchEvent {
  const InitializeMode({
    required this.initialMode,
    this.exerciseType,
  });

  final ExerciseMode initialMode;
  final String? exerciseType;

  @override
  List<Object?> get props => [initialMode, exerciseType];
}

/// Exercise modes enumeration
enum ExerciseMode {
  exercise,  // 做题模式
  listen,    // 听题模式
  browse,    // 背题模式
  video,     // 视频模式
}

/// Extension for mode names
extension ExerciseModeExtension on ExerciseMode {
  String get displayName {
    switch (this) {
      case ExerciseMode.exercise:
        return '做题';
      case ExerciseMode.listen:
        return '听题';
      case ExerciseMode.browse:
        return '背题';
      case ExerciseMode.video:
        return '视频';
    }
  }

  int get index {
    switch (this) {
      case ExerciseMode.exercise:
        return 0;
      case ExerciseMode.listen:
        return 1;
      case ExerciseMode.browse:
        return 2;
      case ExerciseMode.video:
        return 3;
    }
  }

  static ExerciseMode fromIndex(int index) {
    switch (index) {
      case 0:
        return ExerciseMode.exercise;
      case 1:
        return ExerciseMode.listen;
      case 2:
        return ExerciseMode.browse;
      case 3:
        return ExerciseMode.video;
      default:
        return ExerciseMode.exercise;
    }
  }
}
