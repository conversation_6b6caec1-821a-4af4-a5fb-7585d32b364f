import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:app_links/app_links.dart';
import 'package:domain_models/domain_models.dart';
import 'package:dxjd/app_routes.dart';
import 'package:dxjd/bottom_tabbar_controller.dart';
import 'package:dxjd/mainController.dart';
import 'package:dxjd/tab_container_screen.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_inapp_purchase/flutter_inapp_purchase.dart';
import 'package:home/home.dart';
import 'package:home/src/home/<USER>/common_view.dart';
import 'package:api/api.dart';
import 'package:component_library/component_library.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluwx/fluwx.dart';
import 'package:key_value_storage/key_value_storage.dart';
import 'package:mine/mine.dart';
import 'package:mop/mop.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:quiz/quiz.dart';
import 'package:rxdart/rxdart.dart';
import 'package:timing_repository/src/timing_local_storage.dart';
import 'package:timing_repository/src/timing_repo_api_ext.dart';
import 'package:timing_repository/src/timing_repo_db_ext.dart';
import 'package:timing_repository/src/timing_rulers_mixin.dart';
import 'package:timing_repository/src/widgets/widgets.dart';
import 'package:timing_repository/src/widgets/xian_train_dialog.dart';
import 'package:tools/tools.dart' hide PermissionUtils;
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:user_repository/user_repository.dart';
import 'package:home_repository/home_repository.dart';
import 'package:http/http.dart' as http;
import '../timing_repository.dart';
import 'package:intl/intl.dart';

// 培训日志类型
enum TrainLogType {
  // 签到
  signIn,
  // 过程
  process,
  // 签退
  signOut,
  //补传签退
  supplementSignOut,
}

// 过程抓拍类型： 1.均匀间隔 2.随机间隔 3.仅拍一次 ; 默认拍一次
enum ProcessCaptureType {
  // 均匀间隔
  uniform,
  // 随机间隔
  random,
  // 仅拍一次
  onlyOnce,
}

typedef FutureCallback = Future<Map<String, dynamic>> Function(
    Map<String, dynamic>);

class TimingRepository with WidgetsBindingObserver, TimingRulersMixin {
  TimingRepository._internal({
    BuildContext? context,
    required KeyValueStorage noSqlStorage,
    required this.remoteApi,
    required HomeRepository homeRepository,
    required UserRepository userRepository,
    @visibleForTesting TimingLocalStorage? localStorage,
  })  : localStorage = localStorage ??
            TimingLocalStorage(
              noSqlStorage: noSqlStorage,
            ),
        _userRepository = userRepository,
        homeRepository = homeRepository,
        _context = context {
    WidgetsBinding.instance.addObserver(this);
    // 创建一个新的Stream，它会在500毫秒内只接收最后一次更新
    final debouncedStream = _userRepository
        .getUserAccount()
        .debounceTime(const Duration(milliseconds: 500));
    _userSubscription = debouncedStream.listen((user) {
      if (user != null && user.isBind != null && user.isBind == 1) {
        // // 请求计时规则
        // loadTimingRule();
        // remoteOpenStatus().then((value) => _remoteOpenStatus = value);
        // practiceOpenStatus().then((value) => _practiceOpenStatus = value);

        // 测试计时规则，暂且每次都请求
        cleanTimingRule();
        // loadTimingRule();
      } else {
        // 清空本地计时规则
        cleanTimingRule();
      }
      // 注册Mop小程序通信API
      registerFinClipTimingApi();
    });
  }

  static TimingRepository? _instance;
  StreamSubscription? _userSubscription;

  static TimingRepository init({
    BuildContext? context,
    required KeyValueStorage noSqlStorage,
    required Api remoteApi,
    required HomeRepository homeRepository,
    required UserRepository userRepository,
    @visibleForTesting TimingLocalStorage? localStorage,
  }) {
    _instance ??= TimingRepository._internal(
      context: context,
      noSqlStorage: noSqlStorage,
      remoteApi: remoteApi,
      userRepository: userRepository,
      homeRepository: homeRepository,
      localStorage: localStorage,
    );
    return _instance!;
  }

  static TimingRepository get instance {
    if (_instance == null) {
      throw Exception('You must call init() before using instance');
    }
    return _instance!;
  }

  /// ----------------------------------属性-------------------------------------
  final BuildContext? _context;
  final Api remoteApi;
  final TimingLocalStorage localStorage;
  final UserRepository _userRepository;
  final HomeRepository homeRepository;
  // final BehaviorSubject<void> _showProcessDialogSubject = BehaviorSubject();

  // 是否远程开启状态
  bool? _remoteOpenStatus;

  // 是否从业开启状态
  bool? _practiceOpenStatus;

  // 当前科目
  int _subject = 1;

  int get subject => _subject;

  // 当前培训日志类型
  TrainLogType _logType = TrainLogType.signIn;

  TrainLogType get logType => _logType;

  // 最近一次拍照信息: 时间，照片URL，等信息
  Map<String, dynamic> _lastPhotoInfo = {};

  Map<String, dynamic> get lastPhotoInfo => _lastPhotoInfo;

  // 是否广东学员
  bool _isGuangdongStudent = false;

  bool get isGuangdongStudent => _isGuangdongStudent;

  set setGuangdongStudent(bool value) {
    _isGuangdongStudent = value;
  }

  String _guangDongWeChatSignInCode = '';

  String get guangDongWeChatSignInCode => _guangDongWeChatSignInCode;

  // 培训ID
  String _trainId = '';

  String get trainId => _trainId;

  // 微信
  WeChatLaunchMiniProgramResponse? _result;
  final Fluwx fluwx = Fluwx();
  late Function(WeChatResponse) responseListener;
  final _weChatMiniProgramExtMsgController =
      StreamController<String>.broadcast();
  // 计时器
  Timer? _timer;

  Timer? get timer => _timer;
  StreamSubscription? appleStream;
  StreamSubscription? _purchaseErrorSubscription;
  int _counter = 0;
  int _processCounter = 0;
  int _localMinute = 0;
  int _detachedTime = 0;
  // 0 未请求 1 请求中 2请求结束 3请求成功
  int isApplePaying = 0;
  // 当前过程拍照的触发时间
  int _currentProcessTriggerTime = 0;

  final Random _random = Random();

  int _defaultUploadHeartBeatMinutes = 5;

  // 分钟间隔
  int? _uploadHeartMinutes; // 心跳间隔
  int? _processMinutes; // 随机拍照间隔

  bool _processNeedOneTime = false;

  // 驾校配置
  late Map<String, dynamic>? _schoolConfig;

  bool get allowProcessCapture {
    final map = jsonDecode(_schoolConfig?['TR_004']);
    if (kDebugMode) return true;
    return map['en'] ?? false;
  }

  // 抓拍类型:1.均匀间隔 2.随机间隔 3.仅拍一次 ; 默认拍一次
  int get processCaptureType {
    final map = jsonDecode(_schoolConfig?['TR_004']);
    return map['type'] ?? 3;
  }

  ProcessCaptureType get processCaptureEnumType {
    final map = jsonDecode(_schoolConfig?['TR_004']);
    final type = map['type'] ?? 3;
    if (type == 1) {
      return ProcessCaptureType.uniform;
    } else if (type == 2) {
      return ProcessCaptureType.random;
    } else {
      return ProcessCaptureType.onlyOnce;
    }
  }

  // 随机间隔时间范围: 默认5分钟
  int get processCaptureRandomMinutes {
    final map = jsonDecode(_schoolConfig?['TR_004']);
    return map['random'] ?? 5;
  }

  /// --------------------------------培训条件判断--------------------------------
  // 正常培训前校验：true 继续路由下一级；false 阻断路由
  // isStrict: 是否是严格计时
  Future<bool> validateBeforeTraining(
      {Function(bool)? continues,
      bool isStrictTraining = false,
      int subject = 1}) async {
    //检测相机权限，如果没有开启，会有弹框提醒
    final isCameraPermission =
        await PermissionUtils.checkCameraPermission(_context!);
    if (!isCameraPermission) {
      return false;
    }

    // 检测本地待上传记录，可以指定过滤条件，有效分钟数，默认是小于1 分钟的都过滤了
    final trainLogs = await localStorage.getTrainLogList();
    if (trainLogs.isNotEmpty) {
      // 处理学时补传
      _handleSupplementTrainLog(trainLogs.first);
      return false;
    }

    Loading.dismiss();
    Loading.show();

    // 是否绑定驾校
    bool isSchoolBound = await checkSchoolBound();
    if (!isSchoolBound) {
      Loading.dismiss();
      return true;
    }

    // 检测是否允许培训
    bool isTrainingAllowed = await checkTrainingAllowed();
    if (!isTrainingAllowed) {
      Loading.dismiss();
      return true;
    }

    // 检测是否用户自己关闭了计时
    final isTimingSwitchOn = await getTimingSwitchStatus();
    if (isTimingSwitchOn == false) {
      Toast.show('前往我的开启计时开关可以继续计时~', duration: 3);
      return true;
    }

    // 是否是严格计时
    final isStrictTiming = await checkStrictTiming();
    // 如果开启了严格计时且不是检测是否严格计时，直接返回
    if (isStrictTiming && !isStrictTraining) {
      Loading.dismiss();
      return true;
    }

    Loading.dismiss();

    // 弹出确认科目的选择框
    final makeSureStart = await showStartTimingDialog(subject);
    // 没有返回值
    if (!makeSureStart) {
      Loading.dismiss();
      return true;
    }
    // 设置当前计时科目
    _subject = subject;
    setTimingSubject(subject);

    // 跳转拍照界面
    final photoInfo = await _navigateToPhotoScreen(TrainLogType.signIn); // 签到拍照
    if (photoInfo == null) {
      return false;
    }
    // 设置最近的拍照信息
    _lastPhotoInfo = photoInfo;

    // 检测是否是广东学员，如果是需要弹框跳转微信小程序签到，然后继续流程
    final isGuangdongStudent = await checkIsGuangdongStudent();
    setGuangdongStudent = isGuangdongStudent;
    if (isGuangdongStudent) {
      // 弹框跳转微信小程序签到，获取签到码
      final signInCode = await _handleGuangdongMiniProgramSignCode();
      if (signInCode == null) {
        Loading.dismiss();
        return false;
      }
      if (signInCode.isSuccessful && signInCode.errCode == 0) {
        final map = jsonDecode(signInCode.extMsg ?? '');
        if (map! is Map) {
          Loading.dismiss();
          return false;
        }
        final code = map['code'];
        final expire = map['expirationtime'];
        final stu = map['stunum'];

        // 如果有签到码获取异常时，提前返回，提示重新尝试！
        if (code == null) {
          Loading.dismiss();
          return false;
        }

        final msg = {
          'gdex': {'code': code, 'expire': expire ?? '', 'stu': stu ?? ''}
        };

        final msgStr = jsonEncode(msg);
        _guangDongWeChatSignInCode = msgStr;
        Success.show('签到成功');
      }
    }

    // 调用开启培训接口
    final trainId = await startTrainRequest();
    if (trainId == null) {
      return true;
    } else {
      Success.show('开始计时~');
      // 设置当前培训ID
      _trainId = trainId;
      // 添加培训记录
      upsertTrainLog();
      // 重置一下本地计时器以及相关的变量
      _resetTimer();
      // 开启本地计时器，用于辅助远程心跳机制，保存本地的计时记录数据
      _startTimer();
    }
    return true;
  }

  // 是否严格计时
  Future<bool> checkStrictTiming() async {
    // 是否严格计时
    return false;
  }

  // 是否已绑定驾校
  Future<bool> checkSchoolBound() async {
    // UserCM? user = await _localStorage.getUserCM();
    return true;
  }

  // 是否允许培训
  Future<bool> checkTrainingAllowed() async {
    // 检测是否允许培训
    return true;
  }

  // 检测是否是广东学员
  Future<bool> checkIsGuangdongStudent() async {
    // 检测是否是广东学员
    return false;
  }

  /// ---------------------------------培训UI---------------------------------
  // 弹出科目选择框
  Future<int?> _showSubjectSelectionDialog() async {
    // 默认选中科目
    final defaultSubject = await getTimingSubject();

    final res = await showDialog<int>(
      context: _context!,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('选择科目'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              ListTile(
                title: const Text('科目一'),
                leading: Radio<int>(
                  value: 1,
                  groupValue: _subject,
                  onChanged: (int? value) {
                    Navigator.of(context).pop(1);
                  },
                ),
              ),
              ListTile(
                title: const Text('科目四'),
                leading: Radio<int>(
                  value: 4,
                  groupValue: _subject,
                  onChanged: (int? value) {
                    Navigator.of(context).pop(4);
                  },
                ),
              ),
            ],
          ),
        );
      },
    );

    return res;
  }

  bool isNativeCamera = false;
  late Completer<Map<String, dynamic>?> _takePhotoCompleter;

  // 跳转拍照界面
  Future<Map<String, dynamic>?> _navigateToPhotoScreen(
      TrainLogType logType) async {
    // 跳转拍照界面
    final navigationResult = await Navigator.of(_context!).push(
      MaterialPageRoute(
        fullscreenDialog: true,
        builder: (context) => TakePhotoView(
          userRepository: _userRepository,
          status: TakePhotoStatus.Start,
          isNativeCamera: isNativeCamera,
          exchangeCamera: (isNativeCamera) {
            // 切换摄像头
          },
          checkPhoto: (imageFile) async {
            await Future.delayed(const Duration(milliseconds: 1000));
            return Future.value({'success': false, 'failReason': '非活体'});
          },
        ),
      ),
    );
    return navigationResult;
  }

  Future<Map<String, dynamic>?> navigateToPhotoScreen(
      TrainLogType logType) async {
    _takePhotoCompleter = Completer();
    _pushTakePhotoView(logType);
    return _takePhotoCompleter.future;
    // // 跳转拍照界面
    // var navigationResult = await Navigator.of(_context!).push(
    //   MaterialPageRoute(
    //     fullscreenDialog: true,
    //     builder: (context) => TakePhotoView(
    //       userRepository: _userRepository,
    //       status: TakePhotoStatus.Start,
    //       isNativeCamera: isNativeCamera,
    //       exchangeCamera: (newNativeCamera) async {
    //         // 切换摄像头
    //       },
    //     ),
    //   ),
    // );
    //
    // // 继续等待；
    // if (navigationResult != null && navigationResult['wait']) {
    //   return null;
    // }
    //
    // return navigationResult;
  }

  Future<void> _pushTakePhotoView(TrainLogType logType) async {
    final cameraPermission =
        await PermissionUtils.checkCameraPermission(_context!);
    if (!cameraPermission) {
      Navigator.of(_context).pop();
      return;
    }

    Navigator.of(_context)
        .push(
      MaterialPageRoute(
        fullscreenDialog: true,
        builder: (context) => TakePhotoView(
          userRepository: _userRepository,
          status: TakePhotoStatus.Start,
          isNativeCamera: isNativeCamera,
          checkPhoto: (imageFile) {
            return Future.value({'success': true, 'failReason': ''});
          },
          exchangeCamera: (newNativeCamera) async {
            // 切换摄像头
            if (isNativeCamera != newNativeCamera) {
              // 关闭当前的 TakePhotoView
              Navigator.of(context).pop({'wait': true});
              Loading.show(msg: '初始化相机...');
              isNativeCamera = newNativeCamera;
              Future.delayed(const Duration(milliseconds: 500), () async {
                Loading.dismiss();
                // 再次打开一个新的 TakePhotoView
                _pushTakePhotoView(logType);
              });
            }
          },
        ),
      ),
    )
        .then((result) {
      if (result != null && result is Map && result.keys.contains('wait')) {
        // 如果需要继续等待，就不完成 completer
      } else {
        // 否则，完成 completer 并返回结果
        if (!_takePhotoCompleter.isCompleted) {
          _takePhotoCompleter.complete(result);
        }
      }
    });
  }

  // 弹出过程拍照弹框
  Future<void> _showProcessDialog() async {
    // 弹出过程拍照弹框
    await showDialog(
      context: _context!,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('过程拍照'),
          content: const Text('请拍照'),
          actions: <Widget>[
            TextButton(
              child: const Text('确定'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  // 展示补传学时弹框，App退入后台超时，以及App开启新的培训之前检测有待上传学时的时候触发
  Future<void> _handleSupplementTrainLog(TrainLogCM trainLogCM) async {
    // 1. 弹出补传学时弹框，询问是否补传
    final supplement = await showDialog(
      context: _context!,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('补传学时'),
          content: const Text('是否补传学时'),
          actions: <Widget>[
            TextButton(
              child: const Text('确定'),
              onPressed: () {
                Navigator.of(context).pop(true);
              },
            ),
            TextButton(
              child: const Text('放弃补传'),
              onPressed: () {
                Navigator.of(context).pop(false);
              },
            ),
          ],
        );
      },
    );

    if (supplement == false) {
      // TODO: 可以加一个提醒，放弃会删除本地记录
      // 删除本地培训记录
      localStorage.deleteTrainLog(trainLogCM.trainId);
      return;
    }

    // 2. 跳转补拍结束照片界面
    final photoInfo =
        await _navigateToPhotoScreen(TrainLogType.supplementSignOut);
    if (photoInfo != null) {
      trainLogCM.supplement = true;
      // 上传补传学时
      await supplementTrainRequest(trainLogCM);
    }
  }

  // 西安驾培学时的严正通告弹框
  Future<void> showXiAnDriveSchoolNoticeDialog(
      {int? remainingSeconds, String? content}) async {
    // 弹出西安驾培学时的严正通告弹框
    await showDialog(
      context: _context!,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return XianTrainDialog(
            remainingSeconds: remainingSeconds ?? 5, content: content);
      },
    );
  }

  // 弹出开始计时弹框
  Future<bool> showStartTimingDialog(int subject) async {
    // 弹出开始计时弹框
    return await showDialog(
      context: _context!,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StartTimingDialog(
          isRecorded: false,
          subject: subject,
        );
      },
    );
  }

  // 弹出结束计时弹框
  Future<void> showEndTimingDialog(int subject) async {
    // 弹出结束计时弹框
    return await showDialog(
      context: _context!,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return EndTimingDialog(
          subject: subject,
        );
      },
    );
  }

  // 弹出过程拍照弹框
  Future<void> showProcessTakePhotoDialog() async {
    // 弹出过程拍照弹框
    await showDialog(
      context: _context!,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const ProcessTakePhotoDialog(
          subject: 1,
        );
      },
    );
  }

  // 弹出补传学时弹框
  Future<void> showSupplementEndTimingDialog() async {
    // 弹出补传学时弹框
    await showDialog(
      context: _context!,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const SupplementEndTimingDialog(
          subject: 1,
        );
      },
    );
  }

  // 弹出放弃补传学时弹框
  Future<void> showGiveUpSupplementDialog() async {
    // 弹出放弃补传学时弹框
    await showDialog(
      context: _context!,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const GiveUpSupplementDialog();
      },
    );
  }

  /// -----------------------------------微信-----------------------------------
  // 弹出跳转广东小程序弹框
  Future<WeChatLaunchMiniProgramResponse?>
      _handleGuangdongMiniProgramSignCode() async {
    final completer = Completer<WeChatLaunchMiniProgramResponse?>();
    responseListener = (response) {
      if (response is WeChatLaunchMiniProgramResponse) {
        final _res = 'isSuccessful:${response.isSuccessful}';
        Loading.dismiss();
        completer.complete(response);
      }
    };
    fluwx.addSubscriber(responseListener);
    // 弹出跳转广东小程序弹框
    final res = await showCupertinoDialog(
        context: _context!,
        barrierDismissible: false,
        builder: (BuildContext ctx) {
          return CupertinoAlertDialog(
            title: const Text(
              '温馨提醒',
              style: TextStyle(color: Colors.red),
            ),
            content:
                const Text('应广东省监管平台要求：需在“粤学车”小程序中进行签到验证才能计时(2分钟内完成)，是否前往？'),
            actions: <Widget>[
              Container(
                decoration: const BoxDecoration(
                    border: Border(
                        right: BorderSide(color: Color(0xfff1f1f1), width: 0.5),
                        top: BorderSide(color: Color(0xfff1f1f1), width: 0.5))),
                child: TextButton(
                  child: const Text(
                    '前往签到',
                  ),
                  onPressed: () {
                    Navigator.of(_context).pop(true);
                    Loading.show(msg: '正在等待小程序回应...');
                    launchMiniProgram();
                  },
                ),
              ),
              Container(
                decoration: const BoxDecoration(
                    border: Border(
                        top: BorderSide(color: Color(0xfff1f1f1), width: 0.5))),
                child: TextButton(
                  child: const Text(
                    '取消',
                    style: TextStyle(color: Colors.red),
                  ),
                  onPressed: () {
                    Navigator.of(_context).pop();
                  },
                ),
              )
            ],
          );
        });

    if (res == null && !completer.isCompleted) {
      Loading.dismiss();
      fluwx.removeSubscriber(responseListener);
      completer.complete(null);
    }
    return await completer.future;
  }

  // 启动广东驾培小程序获取签到码
  Future<void> launchMiniProgram() async {
    const path =
        'pages/soild/soild-sign-in?from=app&appName=大象驾考&responseType=code';
    fluwx.open(
        target: MiniProgram(
            username: 'gh_7a520a05e35a',
            path: path,
            miniProgramType: WXMiniProgramType.release));
  }

  /// ---------------------------------培训计时器---------------------------------
  _resetTimer() {
    if (_timer != null) {
      _timer?.cancel();
      _timer = null;
    }

    _counter = 0;
    _processCounter = 0;
    _processNeedOneTime = false;
    _localMinute = 0;
  }

  // 开启计时器
  _startTimer(
      {int? uploadHeartMinutes,
      bool isAllowProcessCapture = false,
      ProcessCaptureType? processCaptureType,
      int? randomProcessMinutes}) {
    // 心跳间隔分钟
    _uploadHeartMinutes = uploadHeartMinutes ??
        _uploadHeartMinutes ??
        _defaultUploadHeartBeatMinutes;

    // 抓拍类型:1.均匀间隔 2.随机间隔 3.仅拍一次 ; 默认拍一次
    if (processCaptureType != null &&
        processCaptureType == ProcessCaptureType.random) {
      // 随机过程分钟（1-X 分钟）
      if (randomProcessMinutes != null && randomProcessMinutes > 0) {
        _processMinutes = 1 + _random.nextInt(randomProcessMinutes);
      }
    } else if (processCaptureType != null &&
        processCaptureType == ProcessCaptureType.uniform) {
      // 均匀过程分钟
      _processMinutes = randomProcessMinutes;
    } else if (processCaptureType != null &&
        processCaptureType == ProcessCaptureType.onlyOnce) {
      _processNeedOneTime = true;
      // 仅拍一次
      _processMinutes = randomProcessMinutes;
    } else {
      // 如果没有外部设置，则给一个默认随机值
      _processMinutes ??= 0;
    }

    if (_timer != null) {
      _timer?.cancel();
      _timer = null;
    }

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) async {
      if (_trainId.isEmpty) {
        return;
      }
      debugPrint('+++++++++++ 正在计时:$_counter秒');
      _counter++;

      if (_counter % 60 == 0 && _counter > 0) {
        // 本地更新一次分钟数
        _localMinute++;
        updateLocalMinute(_localMinute);
      }

      if (_counter % (_uploadHeartMinutes! * 60) == 0) {
        // 每5分钟上传一次心跳
        uploadHeartbeatRequest(trainId: _trainId);
      }
      // 如果允许过程抓拍才执行过程抓拍弹框逻辑
      if (_processMinutes != 0 &&
          _counter % (_processMinutes! * 60) == 0 &&
          isAllowProcessCapture) {
        // 抓拍一次的逻辑
        if (_processCounter >= 1 && _processNeedOneTime) {
          return;
        }

        final time = DateTime.now().millisecondsSinceEpoch;
        // 本地记录一下当前过程抓拍时间
        _currentProcessTriggerTime = time;
        updateProcessPhotoTime(trainId: _trainId, processTime: time);
        // 展示过程拍照弹框
        _showProcessDialog();
        await Future.delayed(const Duration(milliseconds: 1000));
        _processCounter++;
      }
    });
  }

  // 培训计时器暂停
  void _pauseTiming() {
    // 暂停计时
    _timer?.cancel();
    // 校验一次心跳
    uploadHeartbeatRequest(trainId: _trainId);
  }

  // 培训计时器继续
  void _resumeTiming() {
    // 如果计时器已经存在，先取消它
    if (_timer != null) {
      _timer?.cancel();
      _timer = null;
    }
    // 继续计时
    _startTimer(
        uploadHeartMinutes: _defaultUploadHeartBeatMinutes,
        isAllowProcessCapture: allowProcessCapture,
        processCaptureType: processCaptureEnumType,
        randomProcessMinutes: processCaptureRandomMinutes);
    // 校验一次心跳
    uploadHeartbeatRequest(trainId: _trainId);

    // 检测一下培训记录是否合法有效
    queryTrainDetailRequest();
  }

  void disposeTimer() {
    _timer?.cancel();
    _timer = null;
  }

  /// -------------------------------前后台切换监听-------------------------------
  //  前后台切换监听
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // // 如果是培训ID为空，或计时器为空，则直接返回
    if (_trainId.isEmpty || _timer == null) {
      return;
    }
    if (state == AppLifecycleState.resumed) {
      //TODO: 这里最好也刷新token，避免因为token过期导致的计时异常问题
      // _userRepository.refreshToken().then((value) {
      // 应用回到前台且正在计时，继续计时
      _resumeTiming();

      // });
    } else if (state == AppLifecycleState.paused) {
      // 应用进入后台且正在计时，暂停计时
      _pauseTiming();
    } else if (state == AppLifecycleState.detached) {}
  }

  dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _result = null;
    fluwx.removeSubscriber(responseListener);
  }
}

/// -----------------------------注册FinClip小程序API-----------------------------
///
extension FinClipApi on TimingRepository {
  // 打开计时小程序
  void openTimingMiniProgram({String? path}) {
    // Mop.instance.openApplet('fc2230308094514309',
    //     path: path ?? 'pages/timekeeping/index/index');
    JumpSmallProgramUtils.jump(
        path ?? 'pages/timekeeping/index/index', "fc2230308094514309");
    // PermissionUtils.checkCameraPermissionNoContext().then((value) {
    //   if (value) {
    //     // Mop.instance.closeAllApplets();
    //     // 打开小程序
    //     Mop.instance.openApplet('fc2230308094514309', path: path??'pages/timekeeping/index/index');
    //   } else {
    //     Toast.show('请前往设置开启相机权限', duration: 5);
    //   }
    // });
  }

  ///type
  ///1 地区vip视频
  ///2 真实路考vip视频
  ///3 驾考3D视频
  ///4 灯光模拟
  ///5 单项讲解
  Future goBuyTypeVip(int type) async {
    CityInfo? city = homeRepository.cityInfo;

    debugPrint("${city?.cityCode}----goBuyTypeVip city-----${city?.cityName}");

    int province =
        stringToIntConverterProvince(homeRepository.cityInfo?.cityCode);
    String cityName =
        cityMap[homeRepository.cityInfo?.cityCode.substring(0, 4)];
    final shopPath =
        '/pages/vip/part3/part3?province=$province&city=${homeRepository.cityInfo?.cityCode}&cityname=$cityName';
    switch (type) {
      case 1:
        // Mop.instance.openApplet('fc2259710004813765',
        //     path: shopPath + '&type=changeability');
        JumpSmallProgramUtils.jump(
            '$shopPath&type=kaochangditu', "fc2259710004813765");
      case 2:
        // Mop.instance.openApplet('fc2259710004813765',
        // path: shopPath + '&type=zhenshilukao');
        JumpSmallProgramUtils.jump(
            '$shopPath&type=zhenshilukao', "fc2259710004813765");

      case 3:
        // Mop.instance.openApplet('fc2259710004813765',
        //     path: shopPath + '&type=3dlianche');
        JumpSmallProgramUtils.jump(
            '$shopPath&type=3dlianche', "fc2259710004813765");
      case 4:
        // Mop.instance.openApplet('fc2259710004813765',
        //     path: shopPath + '&type=dengguangmoni');
        JumpSmallProgramUtils.jump(
            '$shopPath&type=dengguangmoni', "fc2259710004813765");
      case 5:
        // Mop.instance.openApplet('fc2259710004813765',
        //     path: shopPath + '&type=danxiangjiangjie');
        JumpSmallProgramUtils.jump(
            '$shopPath&type=danxiangjiangjie', "fc2259710004813765");
      case 6:
      // Mop.instance.openApplet('fc2259710004813765',
      //     path: shopPath + '&type=danxiangjiangjie');
        JumpSmallProgramUtils.jump(
            '$shopPath&type=quanshiluxian', "fc2259710004813765");
      default:
        return false;
    }
  }

  // 打开微信客服小程序
  void openFluwxKefuMiniProgram() {
    const path = 'pages/home/<USER>';
    fluwx.open(
        target: MiniProgram(
            username: 'gh_b47e16cc160c',
            path: path,
            miniProgramType: WXMiniProgramType.release));
  }

  // 打开微信种草领奖小程序
  void openFluwxLingJiangMiniProgram() {
    const path = 'pages/home/<USER>';
    fluwx.open(
        target: MiniProgram(
            username: 'gh_b47e16cc160c',
            path: path,
            miniProgramType: WXMiniProgramType.release));
  }

  // 打开微信关注抖音有礼领奖小程序
  void openFluwxGzdyMiniProgram() {
    const path = 'pages/home/<USER>';
    fluwx.open(
        target: MiniProgram(
            username: 'gh_b47e16cc160c',
            path: path,
            miniProgramType: WXMiniProgramType.release));
  }

  // 打开微信客服小程序心理测评
  void openFluwxKefuMiniProgramWithPsychologicalEvaluation() {
    const path = 'pages/home/<USER>';
    fluwx.open(
        target: MiniProgram(
            username: 'gh_b47e16cc160c',
            path: path,
            miniProgramType: WXMiniProgramType.release));
  }

  // 打开微信小程序
  void openFluwxMiniProgram(String path, String name) {
    fluwx.open(
        target: MiniProgram(
            username: name,
            path: path,
            miniProgramType: WXMiniProgramType.release));
  }

  //分享成绩单海报
  Future shareGraded() async {
    try {
      BuryingPointUtils.instance.addPoint(
          buryingPointList: BuryingPointList(
              eventType: 5,
              entranceType: 1,
              action: 3, browseDuration: 0)
      );
      EasyLoading.show();
      ScoreListModel scoreListModel = await homeRepository.getScoreList();

      UserAccount? userAccount = await _userRepository.userAccountDM;
      // 头像字段：cover
      // 姓名字段：name
      // 驾校字段：platSchool
      // 车型字段：trainType
      // 业务类型字段：businessType
      // 日期字段：dateOne、dateTwo、dateThree、dateFour
      // 成绩字段：scoreOne、scoreTwo、scoreThree、scoreFour
      // 是否合格字段：qualifiedOne、qualifiedTwo、qualifiedThree、qualifiedFour
      Map<String, dynamic> map = {
        "cover": userAccount?.image,
        "name": userAccount?.name,
        "platSchool": userAccount?.platSchoolName,
        "trainType": userAccount?.platTrainType,
        // "businessType":userAccount?.,
      };
      var sub1 = getLastScoreForSubject(scoreListModel.list, 1);
      if (sub1 != false) {
        sub1 = sub1 as ListScoreElement;
        map.addAll({
          "dateOne": formatTimestamp(sub1.createTime),
          "scoreOne": sub1.score.toString(),
          "qualifiedOne": sub1.qualified == 1 ? "是" : "否",
        });
      }
      var sub2 = getLastScoreForSubject(scoreListModel.list, 2);
      if (sub2 != false) {
        sub2 = sub2 as ListScoreElement;
        map.addAll({
          "dateTwo": formatTimestamp(sub2.createTime),
          "scoreTwo": sub2.score.toString(),
          "qualifiedTwo": sub2.qualified == 1 ? "是" : "否",
        });
      }
      var sub3 = getLastScoreForSubject(scoreListModel.list, 3);
      if (sub3 != false) {
        sub3 = sub3 as ListScoreElement;
        map.addAll({
          "dateThree": formatTimestamp(sub3.createTime),
          "scoreThree": sub3.score.toString(),
          "qualifiedThree": sub3.qualified == 1 ? "是" : "否",
        });
      }
      var sub4 = getLastScoreForSubject(scoreListModel.list, 4);
      if (sub4 != false) {
        sub4 = sub4 as ListScoreElement;
        map.addAll({
          "dateFour": formatTimestamp(sub4.createTime),
          "scoreFour": sub4.score.toString(),
          "qualifiedOne": sub4.qualified == 1 ? "是" : "否",
        });
      }

      const String url = 'https://dxjk.daxiangjd.com/poster/v1/build/poster';
      const String token = 'JEAJcFRtXczVGOUwNSaWSpYQrQXnMZem';
      final response = await http.post(
        Uri.parse(url),
        headers: <String, String>{
          'Token': token,
        },
        body: jsonEncode(<String, dynamic>{
          'uuid': _userRepository.isExistBuyVip()
              ? '2d685bb51f500ff5'
              : 'bac067cf49e900c7',
          'payload': json.encode(map),
          'b64': true
        }),
      );
      EasyLoading.dismiss();
      if (response.statusCode == 200) {
        if (Platform.isIOS) {
          PermissionStatus status = await Permission.photos.status;
          if (status == PermissionStatus.denied) {
            status = await Permission.photos.request();
            return;
          } else if (status == PermissionStatus.permanentlyDenied) {
            Toast.show('请给大象驾到Pro授权相册权限以便于保存图片分享抖音！');
            Future.delayed(Duration(seconds: 1), () {
              openAppSettings();
            });
            return;
          }
        }
        MethodChannel channel = MethodChannel('xw.dxjk.share');
        final result =
            await channel.invokeMethod('shareImage', {'base64': response.body});
        if (result == 'noInstall') {
          Toast.show('应用未安装，请安装后再试');
        } else if (result is Map) {
          if (result['status'] == 'fail') {
            Toast.show('分享失败,错误码${result["errorCode"]}');
          }
        }
        print('请求成功: ${response.body}');
      } else {
        print('请求失败: ${response.statusCode}');
      }
      return {};
    } catch (e) {
      EasyLoading.dismiss();
      Toast.show('分享失败');
      return {};
    }
  }

  dynamic getLastScoreForSubject(List<ListScoreElement> scores, int subject) {
    // 筛选出所有符合条件的成绩
    List<ListScoreElement> subjectScores =
        scores.where((score) => score.subject == subject).toList();

    // 返回最后一个，如果没有则返回false
    return subjectScores.isNotEmpty ? subjectScores.last : false;
  }

  String formatTimestamp(int timestamp) {
    // 将时间戳转换为 DateTime 对象
    DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);

    // 使用 DateFormat 格式化日期时间
    DateFormat formatter = DateFormat('yyyy-MM-dd HH:mm');
    return formatter.format(dateTime);
  }

  void registerFinClipTimingApi() async {
    bool isAppleCancelPay = false;

    ///start=======================商城小程序=======================///
    //将小程序传递的数据复制到剪切板上
    Mop.instance.registerExtensionApi('setClipboardData', (params) async {
      debugPrint("copyData:${params['data']}");
      try {
        // Toast.show('复制成功${params['data']}', duration: 2);
        Clipboard.setData(ClipboardData(text: params['data']));
        return {'copyData': true};
      } catch (e) {
        return {'copyData': false};
      }
    });

    //获取当前的地理位置
    Mop.instance.registerExtensionApi('getLocationInfo', (params) async {
      debugPrint("getLocation:$params");
      try {
        final location = await AMapUtils().startLocation(Get.context!);
        return {'location': location!.toJson()};
      } catch (e) {
        return {'location': {}};
      }
    });

    //直播跳转
    Mop.instance.registerExtensionApi('toLivePlatform', (params) async {
      debugPrint("toLivePlatform:$params");
      try {
        if (params['params']['PlatformType'] == 1 ||
            params['params']['PlatformType'] == 3) {
          launchUrl(Uri.parse(params['params']['PlatformLink']));
        } else {
          Map map = jsonDecode(params['params']['PlatformLink']);
          openFluwxMiniProgram(
            map["Path"],
            map['AppId'],
          );
        }
        return {};
      } catch (e) {
        return {};
      }
    });

    // 在线客服
    Mop.instance.registerExtensionApi('contactOnlineService', (params) async {
      debugPrint("contactOnlineService:$params");
      try {
        const path = 'pages/home/<USER>';
        fluwx.open(
            target: MiniProgram(
                username: 'gh_b47e16cc160c',
                path: path,
                miniProgramType: WXMiniProgramType.release));
        return {'contactOnlineService': true};
      } catch (e) {
        return {'contactOnlineService': false};
      }
    });

    // 跳转到APP功能页
    Mop.instance.registerExtensionApi('toNativeAppByNotice', (params) async {
      try {
        JumpSmallProgramUtils.close('fc2230308094514309', true);
        PushJumpPage.pushClickEvent(
            json.decode(params['params']['RedirectPath']), false);
        return {'contactOnlineService': true};
      } catch (e) {
        return {'contactOnlineService': false};
      }
    });

    // 在线客服
    Mop.instance.registerExtensionApi('contactOnlineService', (params) async {
      debugPrint("contactOnlineService:$params");
      try {
        const path = 'pages/home/<USER>';
        fluwx.open(
            target: MiniProgram(
                username: 'gh_b47e16cc160c',
                path: path,
                miniProgramType: WXMiniProgramType.release));
        return {'contactOnlineService': true};
      } catch (e) {
        return {'contactOnlineService': false};
      }
    });

    //补偿客服
    Mop.instance.registerExtensionApi('addClaimsCustomerService',
        (params) async {
      debugPrint("addClaimsCustomerService:$params");
      try {
        var serviceAd = 'https://work.weixin.qq.com/kfid/kfc9be3550112fa751d';
        launchUrl(Uri.parse(serviceAd));
        return {'addClaimsCustomerService': true};
      } catch (e) {
        return {'addClaimsCustomerService': false};
      }
    });

    // 跳转到微信小程序的功能页
    Mop.instance.registerExtensionApi('toWechatMiniProgram', (params) async {
      debugPrint("toWechatMiniProgram:$params");
      try {
        final path = params['params']['RedirectPath'];
        final name = params['params']['RedirectApp'];
        fluwx.open(
            target: MiniProgram(
                username: name,
                path: path,
                miniProgramType: WXMiniProgramType.release));
        return {'contactOnlineService': true};
      } catch (e) {
        return {'contactOnlineService': false};
      }
    });

    // 开通vip支付成功回调
    Mop.instance.registerExtensionApi('payVIPSuccess', (params) async {
      debugPrint("payVIPSuccess:$params");
      try {
        JumpSmallProgramUtils.close('fc2259710004813765', false);
        if (params['params']['goods']['Type'] == "vip3map") {
          // 刷新vip开通状态
          Future.delayed(const Duration(seconds: 1), () async {
            await Get.find<SectionThreeController>(tag: ExaminationPageState.key.toString(),).getPointVipOpenConfig();
            if (Get.isRegistered<SubjectThreeCatalogController>()) {
              Get.find<SubjectThreeCatalogController>().update();
            }
            if (Get.isRegistered<DetailPointMapController>()) {
              Get.find<DetailPointMapController>().update();
            }
          });
        } else if (params['params']['goods']['Type'] == "vip3place" ||
            params['params']['goods']['Type'] == "vip3") {
          // 刷新vip开通状态
          await _userRepository.setVipProduct();
          Get.find<ExaminationController>(tag: ExaminationPageState.key.toString()).update();
          if (Get.isRegistered<SectionThreeController>(tag: ExaminationPageState.key.toString(),)) {
            Future.delayed(const Duration(seconds: 1), () async {
              await Get.find<SectionThreeController>(tag: ExaminationPageState.key.toString(),).getPointVipOpenConfig();
              if (Get.isRegistered<SubjectThreeCatalogController>()) {
                Get.find<SubjectThreeCatalogController>().update();
              }
              if (Get.isRegistered<DetailPointMapController>()) {
                Get.find<DetailPointMapController>().update();
              }
            });
          }
        } else if (params['params']['goods']['Type'] == "course") {
        } else {
          await _userRepository.setVipProduct();
          await _userRepository.queryUserInfo(isNeedUpdateInfo: false);
          Get.find<ExaminationController>(tag: ExaminationPageState.key.toString()).update();
        }
        return {'contactOnlineService': true};
      } catch (e) {
        return {'contactOnlineService': false};
      }
    });

    // 苹果支付状态
    Mop.instance.registerExtensionApi('getUserPayStatus', (params) async {
      debugPrint("getUserPayStatus:$params");
      try {
        if (isAppleCancelPay) {
          print('交易取消，通知小程序');
        }
        return {'Status': isAppleCancelPay ? 1 : 0};
      } catch (e) {
        return {'Status': 1};
      }
    });
    // 去登录
    Mop.instance.registerExtensionApi('toLogin', (params) async {
      debugPrint("toLogin:$params");
      try {
        JumpSmallProgramUtils.close('fc2259710004813765', true);
        // 如果未登录，跳转到登录页面
        if (Platform.isAndroid) {
          Get.toNamed(AppRoutes.wechatLogin);
        } else {
          bool isInstalledVx = await fluwx.isWeChatInstalled;
          if (isInstalledVx) {
            Get.toNamed(AppRoutes.wechatLogin);
          } else {
            Get.toNamed(AppRoutes.phoneLogin);
          }
        }
        return {'Status': isAppleCancelPay ? 1 : 0};
      } catch (e) {
        return {'Status': 1};
      }
    });
    // 消息未读点击事件
    Mop.instance.registerExtensionApi('unloadNoticePage', (params) async {
      try {
        Future.delayed(Duration(seconds: 2), () {
          homeRepository.getUnReadMessage();
        });
        return {'isCloseApplet': 'isCloseApplet'};
      } catch (e) {
        return {'isCloseApplet': false};
      }
    });
    //跳转支付小程序
    Mop.instance.registerExtensionApi('ApplyPayOrder', (params) async {
      isAppleCancelPay = false;
      debugPrint("ApplyPayOrder:${params['params']['OrderId']}");
      try {
        if (Platform.isAndroid || params['params']['isUseNature']) {
          if (params['params']['PaymentMethod'] == 'A02') {
            aliPay(orderId: params['params']['OrderId']);
          } else {
            responseListener = (response) {
              if (response is WeChatLaunchMiniProgramResponse) {
                final _res = 'isSuccessful:${response.isSuccessful}';
                // Toast.show(re);
                _weChatMiniProgramExtMsgController.add(response.extMsg ?? '');
              }
            };
            fluwx.addSubscriber(responseListener);
            // final orderId=await _userRepository.getOrderId(orderId: params['params']['OrderId']);
            // debugPrint("orderId:---$orderId");
            final _token = await _userRepository.getUserToken();
            final _account = await _userRepository.getUserName();
            debugPrint("account:---$_account");
            debugPrint("token:---$_token");
            final path =
                'pages/index/index?t=$_token&oi=${params['params']['OrderId']}&uf=$_account&ty=4';
            fluwx.open(
                target: MiniProgram(
                    username: 'gh_d8c0d081380a',
                    path: path,
                    miniProgramType: WXMiniProgramType.release));

            String extMsg = await _weChatMiniProgramExtMsgController.stream
                .asBroadcastStream()
                .first
                .timeout(const Duration(seconds: 120));
            if (Platform.isAndroid) {
              // 将当前正在运行的最后一个打开的小程序移至任务栈前台，仅Android生效，
              Mop.instance.moveCurrentAppletToFront();
            }
            return {'openPaymentMiniProgram': extMsg};
          }
          return {'openPaymentMiniProgram': 'extMsg'};
        } else {
          String orderId = params['params']['OrderId'];
          var orderDetail = await _userRepository.getApplePayId(orderId);
          await FlutterInappPurchase.instance.initialize();
          var products = await FlutterInappPurchase.instance
              .getProducts([orderDetail['AppleGoodsId']]);
          bool isExitProduct = products.any(
              (element) => element.productId == orderDetail['AppleGoodsId']);
          if (!isExitProduct) {
            JumpSmallProgramUtils.close('fc2259710004813765', true);
            isAppleCancelPay = true;
            Toast.show(
              '没有找到相关产品，请联系客服',
            );
            return {};
          }
          appleStream =
              FlutterInappPurchase.purchaseUpdated.listen((productItem) async {
            if (productItem != null) {
              print('当前支付状态${productItem.transactionStateIOS}------------');
              if (productItem.transactionStateIOS ==
                  TransactionState.purchased) {
                await _userRepository.uploadAppleVoucher(
                    orderId,
                    productItem.transactionId!,
                    productItem.transactionReceipt!);
                Timer.periodic(Duration(seconds: 1), (timer) async {
                  Map map = await _userRepository.searchApplePayStatus(orderId);
                  if (map['Status'] != 0) {
                    print('支付结束------------');
                    timer.cancel();
                    if (map['Status'] == 1) {
                      print('支付成功------------');
                      FlutterInappPurchase.instance
                          .finishTransaction(productItem);
                    }
                  }
                });
              } else if (productItem.transactionStateIOS ==
                  TransactionState.restored) {
                print('支付失败------------');
                isAppleCancelPay = true;
                FlutterInappPurchase.instance.clearTransactionIOS();
              }
            }
            appleStream?.cancel();
          });
          _purchaseErrorSubscription =
              FlutterInappPurchase.purchaseError.listen((purchaseError) {
            // 检查错误代码是否为用户取消支付
            if (purchaseError?.code == 'E_USER_CANCELLED') {
              print('取消支付------------');
              isAppleCancelPay = true;
              FlutterInappPurchase.instance.clearTransactionIOS();
              // FlutterInappPurchase.instance.finishTransactionIOS(products)
              // 在这里处理用户取消支付的逻辑
            }
            print('purchase-error: $purchaseError');
            appleStream?.cancel();
            _purchaseErrorSubscription?.cancel();
          });
          FlutterInappPurchase.instance
              .requestPurchase(orderDetail['AppleGoodsId']);
        }
        // FlutterInappPurchase.instance.
        return {};
      } catch (e) {
        return {'openPaymentMiniProgram': false};
      }
    });

    //修改城市
    Mop.instance.registerExtensionApi('editCity', (params) async {
      debugPrint("xxxxxx:$params");
      try {
        JumpSmallProgramUtils.close('fc2259710004813765', false);
        return {'xxx': 'isCloseApplet'};
      } catch (e) {
        return {'xxxx': false};
      }
    });

    //立即刷题使用
    Mop.instance.registerExtensionApi('toBrushingQuestionsByVideoTopic',
        (params) async {
      debugPrint("getUserPayStatus:$params");
      try {
        JumpSmallProgramUtils.close('fc2230308094514309', true);
        if (!MainController.isLoginIntercept()) {
          return {};
        }
        ExaminationController examinationController =
            Get.find<ExaminationController>(tag: ExaminationPageState.key.toString());
        if (examinationController.currentIndex == 3) {
          JumpUtils.get().jumpMixed(4, isGoWatchVideo: true);
        } else {
          JumpUtils.get().jumpMixed(1, isGoWatchVideo: true);
        }
        return {};
      } catch (e) {
        return {};
      }
    });

    //获取关闭商城小程序行为
    Mop.instance.registerExtensionApi('getCloseAppletAction', (params) async {
      debugPrint("getCloseAppletAction:$params");
      try {
        // final miniProgramPath = await PreferencesService().getString('mini_program_path') ?? '';
        // if(miniProgramPath=='/pages/index/index'){
        Get.toNamed(AppRoutes.home);
        // }
        // final isCloseApplet = await getCloseApplet();
        return {'isCloseApplet': 'isCloseApplet'};
      } catch (e) {
        return {'isCloseApplet': false};
      }
    });

    //支付退出看广告去解锁权益
    Mop.instance.registerExtensionApi('toAd', (params) async {
      debugPrint("getCloseAppletAction:$params");
      try {
        int sub = params['params']['part'].toInt();
        ListData? AdvertisData;
        if (params['params']['type'] == 'kaochangluxiandianweitu') {
          AdvertisData = BeiziAdPlugin.instance.getAdvertisData(3);
          BeiziAdPlugin.instance.pointProductID = params['params']['ProductId'];
        } else if (params['params']['type'] == 'dengguangmoni') {
          AdvertisData = BeiziAdPlugin.instance.getAdvertisData(2);
        } else if (params['params']['type'] == '3dlianche') {
          if (sub == 3) {
            AdvertisData = BeiziAdPlugin.instance.getAdvertisData(4);
          } else {
            AdvertisData = BeiziAdPlugin.instance.getAdvertisData(1);
          }
        } else {}
        await JumpSmallProgramUtils.close('fc2259710004813765', false);
        EasyLoading.show();
        Future.delayed(Duration(seconds: 1), () {
          //延迟1秒
          BeiziAdPlugin.instance.getRewardVideoMethod(AdvertisData);
        });
        return {'isCloseApplet': 'isCloseApplet'};
      } catch (e) {
        return {'isCloseApplet': false};
      }
    });

    // 查看获取学习驾驶证明指引 onGetGuidelinesForLearning
    Mop.instance.registerExtensionApi('onGetGuidelinesForLearning',
        (params) async {
      debugPrint("onGetGuidelinesForLearning:$params");
      try {
        // _routerDelegate.push('/my_doc');
        Tools.get().jump(GuideLinesForLearning());
        JumpSmallProgramUtils.close('fc2230308094514309', false);
        return {'getGuidelinesForLearning': 'success'};
      } catch (e) {
        return {'getGuidelinesForLearning': false};
      }
    });

    //分享成绩单海报
    Mop.instance.registerExtensionApi('toShareGrade', (params) async {
      debugPrint("toShareGrade:$params");
      JumpSmallProgramUtils.close('fc2230308094514309', false);
      shareGraded();
      return {};
    });

    //切换tabbar首页
    Mop.instance.registerExtensionApi('toExamTab', (params) async {
      debugPrint("toExamTab:$params");
      JumpSmallProgramUtils.close('fc2259710004813765', false);
      if(Get.isRegistered<BottomTabBarController>(tag: TabContainerScreenState.key.toString())){
        BottomTabBarController bottomTabBarController = Get.find<BottomTabBarController>(tag: TabContainerScreenState.key.toString());
        if(bottomTabBarController.selectIndex!= 0){
          Get.find<ExaminationController>(tag: ExaminationPageState.key.toString()).startCurrentBannerScroll();
          bottomTabBarController.pageController.jumpToPage(0);
          bottomTabBarController.updateSelectIndex(0);
        }
      }
      return {};
    });

    //切换我的首页
    Mop.instance.registerExtensionApi('toPersonalTab', (params) async {
      debugPrint("toPersonalTab:$params");
      JumpSmallProgramUtils.close('fc2259710004813765', false);
      if(Get.isRegistered<BottomTabBarController>(tag: TabContainerScreenState.key.toString())){
      BottomTabBarController bottomTabBarController = Get.find<BottomTabBarController>(tag: TabContainerScreenState.key.toString());
        if(bottomTabBarController.selectIndex!= 2){
          Get.find<ExaminationController>(tag: ExaminationPageState.key.toString()).stopCurrentBannerScroll();
          bottomTabBarController.pageController.jumpToPage(2);
          bottomTabBarController.updateSelectIndex(2);
        }
      }
      return {};
    });
    ///end============================商城小程序=======================///

    // PreferencesService().setBool("fist_run", false);
    // FinClip获取用户信息
    // await Future.delayed(const Duration(milliseconds: 1000));
    Mop.instance.registerExtensionApi('getUserInfoModel', (params) async {
      debugPrint("getUserInfo:$params");
      try {
        final user = await _userRepository.getUserAccountInfo();
        return {'userInfo': user!.toJson()};
      } catch (e) {
        return {'userInfo': {}};
      }
    });

    // 是否第一次培训
    Mop.instance.registerExtensionApi('getFirstTrain', (params) async {
      debugPrint("isFirstTrain:$params");
      try {
        final isFirstTrain =
            await PreferencesService().getBool('app_fist_train') ?? true;
        return {'getFirstTrain': isFirstTrain};
      } catch (e) {
        return {'isFirstTrain': false};
      }
    });

    // 开始培训后回调
    Mop.instance.registerExtensionApi('startTrain', (params) async {
      debugPrint("startTrain:$params");
      try {
        final isFirstTrain =
            await PreferencesService().getBool('app_fist_train');
        if (isFirstTrain == null || isFirstTrain) {
          await PreferencesService().setBool('app_fist_train', false);
        }
        // TODO: app 端开始培训后，需要记录本地的培训记录
        return {'startTrain': true};
      } catch (e) {
        return {'startTrain': false};
      }
    });

    // 结束培训后回调
    Mop.instance.registerExtensionApi('endTrain', (params) async {
      debugPrint("endTrain:$params");
      try {
        final isFirstTrain =
            await PreferencesService().getBool('app_fist_train');
        if (isFirstTrain == null || isFirstTrain) {
          await PreferencesService().setBool('app_fist_train', false);
        }
        // TODO: app 端结束培训后，需要清除本地的培训记录
        return {'endTrain': true};
      } catch (e) {
        return {'endTrain': false};
      }
    });

    // FinClip获取用户token
    Mop.instance.registerExtensionApi('getToken', (params) async {
      debugPrint("getToken:$params");
      try {
        final _token = await _userRepository.getUserToken();
        if (_token == null) {
          // 如果token为空，弹出主动登录界面
          HttpEventBus.instance.commit(EventKeys.logout);
          return {'token': ''};
        }
        // return {'token': _token};
        return {'token': _token, "appVersion": await Tools.getAppVersion(),'DeviceModel':(Platform.isIOS?"APPLE":await Tools.getDeviceInfo()).toUpperCase()??'Unknown','SystemModel':await Tools.getPhoneModel()??'Unknown'};
        // return {'token': _token,"appVersion":"1.2.9","expires":7200};
      } catch (e) {
        return {'token': ''};
      }
    });

    // 刷新token
    Mop.instance.registerExtensionApi('refreshToken', (params) async {
      debugPrint("refreshToken:$params");
      try {
        await _userRepository.renewalToken();
        final _token = await _userRepository.getUserToken();
        if (_token == null) {
          // 如果token为空，弹出主动登录界面
          HttpEventBus.instance.commit(EventKeys.logout);
          return {'token': ''};
        }
        return {'token': '$_token'};
      } catch (e) {
        return {'token': ''};
      }
    });

    // 远程培训是否已开通
    Mop.instance.registerExtensionApi('remoteTimingIsOpen', (params) async {
      debugPrint("remoteTimingIsOpen:$params");
      try {
        _remoteOpenStatus ??= await remoteApi.remoteOpenStatus();
        return {'remoteTimingIsOpen': _remoteOpenStatus};
      } catch (e) {
        return {'remoteTimingIsOpen': false};
      }
    });

    // 从业是否已开通
    Mop.instance.registerExtensionApi('practiceIsOpen', (params) async {
      debugPrint("practiceIsOpen:$params");
      try {
        _practiceOpenStatus ??= await remoteApi.practiceOpenStatus();
        return {'practiceIsOpen': _practiceOpenStatus};
      } catch (e) {
        return {'practiceIsOpen': false};
      }
    });

    // 是否严格计时模式
    Mop.instance.registerExtensionApi('strictTiming', (params) async {
      debugPrint("strictTiming:$params");
      try {
        final isStrictTiming = await getStrictTimingIsOpen();
        return {'strictTiming': isStrictTiming};
      } catch (e) {
        return {'strictTiming': false};
      }
    });

    //  APP禁止未备案学员培训： 默认不禁止
    Mop.instance.registerExtensionApi('noRecordNotAllowTrain', (params) async {
      debugPrint("noRecordNotAllowTrain:$params");
      try {
        final noRecordNotAllowTrain = await getNoRecordStudentNotAllowTrain();
        return {'noRecordNotAllowTrain': noRecordNotAllowTrain};
      } catch (e) {
        return {'noRecordNotAllowTrain': false};
      }
    });

    // 未备案是否提示用户: 默认不提示
    Mop.instance.registerExtensionApi('noRecordStudentShowTips',
        (params) async {
      debugPrint("noRecordStudentShowTips:$params");
      try {
        final noRecordStudentShowTips = await getShowNoRecordStudent();
        return {'noRecordStudentShowTips': noRecordStudentShowTips};
      } catch (e) {
        return {'noRecordStudentShowTips': false};
      }
    });

    // 是否需要人证合一： 默认不需要
    Mop.instance.registerExtensionApi('needFaceAndIdCard', (params) async {
      debugPrint("needFaceAndIdCard:$params");
      try {
        final needFaceAndIdCard = await getOpenFaceAndIDCardUpdate();
        return {'needFaceAndIdCard': needFaceAndIdCard};
      } catch (e) {
        return {'needFaceAndIdCard': false};
      }
    });

    // 是否需要广东小程序签到码：默认不需要
    Mop.instance.registerExtensionApi('needGuangdongSignInCode',
        (params) async {
      debugPrint("needGuangdongSignInCode:$params");
      try {
        final needGuangdongSignInCode = await getOpenGuangDongMiniProgram();
        return {'needGuangdongSignInCode': needGuangdongSignInCode};
      } catch (e) {
        return {'needGuangdongSignInCode': false};
      }
    });

    // 是否需要人脸识别： 默认不需要
    Mop.instance.registerExtensionApi('needFaceRecognition', (params) async {
      debugPrint("needFaceRecognition:$params");
      try {
        final needFaceRecognition = await getFaceRecognitionIsOpen();
        return {'needFaceRecognition': needFaceRecognition};
      } catch (e) {
        return {'needFaceRecognition': false};
      }
    });

    // 人脸识别是否本人: 必须本人
    Mop.instance.registerExtensionApi('faceRecognitionIsSelf', (params) async {
      debugPrint("faceRecognitionIsSelf:$params");
      try {
        final faceRecognitionIsSelf = await getFaceRecognitionIsSelf();
        return {'faceRecognitionIsSelf': faceRecognitionIsSelf};
      } catch (e) {
        return {'faceRecognitionIsSelf': false};
      }
    });

    // 如果识别结果不是本人但是 允许用户点击确认然后 继续培训
    Mop.instance.registerExtensionApi('faceRecognitionIsNotSelfCanContinue',
        (params) async {
      debugPrint("faceRecognitionIsNotSelfCanContinue:$params");
      try {
        final faceRecognitionIsNotSelfCanContinue =
            await getFaceRecognitionIsNotSelfCanContinue();
        return {
          'faceRecognitionIsNotSelfCanContinue':
              faceRecognitionIsNotSelfCanContinue
        };
      } catch (e) {
        return {'faceRecognitionIsNotSelfCanContinue': false};
      }
    });

    // 不管人脸比对结果直接通过
    Mop.instance.registerExtensionApi('faceRecognitionDirectPass',
        (params) async {
      debugPrint("faceRecognitionDirectPass:$params");
      try {
        final faceRecognitionDirectPass = await getFaceRecognitionDirectPass();
        return {'faceRecognitionDirectPass': faceRecognitionDirectPass};
      } catch (e) {
        return {'faceRecognitionDirectPass': false};
      }
    });

    // APP开启人脸照片质量检测:低于照片质量阈值:___不予通过人脸检测.
    Mop.instance.registerExtensionApi('openFaceQualityCheck', (params) async {
      debugPrint("openFaceQualityCheck:$params");
      try {
        final openFaceQualityCheck = await getOpenFaceQualityCheck();
        return {'openFaceQualityCheck': openFaceQualityCheck};
      } catch (e) {
        return {'openFaceQualityCheck': false};
      }
    });

    // APP人脸照片质量阈值: 默认60
    Mop.instance.registerExtensionApi('faceQualityThreshold', (params) async {
      debugPrint("faceQualityThreshold:$params");
      try {
        final faceQualityThreshold = await getFaceQualityThreshold();
        return {'faceQualityThreshold': faceQualityThreshold};
      } catch (e) {
        return {'faceQualityThreshold': 60};
      }
    });

    // 是否开启AI活体检测: 默认关闭
    Mop.instance.registerExtensionApi('openFaceRecognitionLiveBody',
        (params) async {
      debugPrint("faceRecognitionLiveBody:$params");
      try {
        final faceRecognitionLiveBody = await getFaceRecognitionLiveBody();
        return {'openFaceRecognitionLiveBody': faceRecognitionLiveBody};
      } catch (e) {
        return {'openFaceRecognitionLiveBody': false};
      }
    });

    // 是否开启过程抓拍： 默认关闭
    Mop.instance.registerExtensionApi('snappingPhotosIsOpen', (params) async {
      debugPrint("snappingPhotosIsOpen:$params");
      try {
        final snappingPhotosIsOpen = await getSnappingPhotosIsOpen();
        return {'snappingPhotosIsOpen': snappingPhotosIsOpen};
      } catch (e) {
        return {'snappingPhotosIsOpen': false};
      }
    });

    // 固定X分钟抓拍
    Mop.instance.registerExtensionApi('snappingPhotosFixed', (params) async {
      debugPrint("snappingPhotosFixed:$params");
      try {
        final snappingPhotosFixed = await getSnappingPhotosFixed();
        return {'snappingPhotosFixed': snappingPhotosFixed};
      } catch (e) {
        return {'snappingPhotosFixed': false};
      }
    });

    // 随机X分钟抓拍
    Mop.instance.registerExtensionApi('snappingPhotosRandom', (params) async {
      debugPrint("snappingPhotosRandom:$params");
      try {
        final snappingPhotosRandom = await getSnappingPhotosRandom();
        return {'snappingPhotosRandom': snappingPhotosRandom};
      } catch (e) {
        return {'snappingPhotosRandom': false};
      }
    });

    // 仅拍一次
    Mop.instance.registerExtensionApi('snappingPhotosOnlyOnce', (params) async {
      debugPrint("snappingPhotosOnlyOnce:$params");
      try {
        final snappingPhotosOnlyOnce = await getSnappingPhotosOnlyOnce();
        return {'snappingPhotosOnlyOnce': snappingPhotosOnlyOnce};
      } catch (e) {
        return {'snappingPhotosOnlyOnce': false};
      }
    });

    // X分钟抓拍
    Mop.instance.registerExtensionApi('snappingPhotosMinutes', (params) async {
      debugPrint("snappingPhotosRandomMinutes:$params");
      try {
        final snappingPhotosRandomMinutes =
            await getSnappingPhotosRandomMinutes();
        return {'snappingPhotosMinutes': snappingPhotosRandomMinutes};
      } catch (e) {
        return {'snappingPhotosMinutes': 0};
      }
    });

    // 最小培训时长: 单位分钟
    Mop.instance.registerExtensionApi('minTrainingTime', (params) async {
      debugPrint("minTrainingTime:$params");
      try {
        final minTrainingTime = await getMinTrainMinutes();
        return {'minTrainingTime': minTrainingTime};
      } catch (e) {
        return {'minTrainingTime': 0};
      }
    });

    // 是否禁止跨天培训
    Mop.instance.registerExtensionApi('forbidCrossDayTraining', (params) async {
      debugPrint("forbidCrossDayTraining:$params");
      try {
        final forbidCrossDayTraining = await getNoCrossDayTrain();
        return {'forbidCrossDayTraining': forbidCrossDayTraining};
      } catch (e) {
        return {'forbidCrossDayTraining': false};
      }
    });

    // 几点几刻后禁止签到
    Mop.instance.registerExtensionApi('forbidSignInTime', (params) async {
      debugPrint("forbidSignInTime:$params");
      try {
        final forbidSignInTime = await getNoCrossDaySignInTime();
        return {'forbidSignInTime': forbidSignInTime};
      } catch (e) {
        return {'forbidSignInTime': ''};
      }
    });

    // 几点几刻强制拍照签退
    Mop.instance.registerExtensionApi('forceSignOutTime', (params) async {
      debugPrint("forceSignOutTime:$params");
      try {
        final forceSignOutTime = await getNoCrossDaySignOutTime();
        return {'forceSignOutTime': forceSignOutTime};
      } catch (e) {
        return {'forceSignOutTime': ''};
      }
    });

    // 几点提醒
    Mop.instance.registerExtensionApi('forceSignOutRemindTime', (params) async {
      debugPrint("remindTime:$params");
      try {
        final remindTime = await getNoCrossDayRemindTime();
        return {'forceSignOutRemindTime': remindTime};
      } catch (e) {
        return {'remindTime': ''};
      }
    });

    // 提醒内容
    Mop.instance.registerExtensionApi('forceSignOutRemindContent',
        (params) async {
      debugPrint("remindContent:$params");
      try {
        final remindContent = await getNoCrossDayRemindMessage();
        return {'forceSignOutRemindContent': remindContent};
      } catch (e) {
        return {'remindContent': ''};
      }
    });
  }

  void aliPay({String? orderId}) async {
    orderId ??= '030E043415';
    final payInfo = await homeRepository.aliPay(orderId: orderId);

    String payInfoString = payInfo['PayInfo'];

    debugPrint('支付宝支付参数：$payInfoString');

    // final result = "{"
    //     "\"appid\":\"${payInfo['appid'] ??
    //     ''}\",""\"body\":\"${payInfo['body'] ?? ''}\","
    //     "\"cusid\":\"${payInfo['cusid'] ?? ''}\","
    //     "\"expiretime\":\"${payInfo['expiretime'] ??
    //     ''}\",""\"isdirectpay\":\"${payInfo['isdirectpay'] ??
    //     ''}\",""\"notify_url\":\"${payInfo['notify_url'] ??
    //     ''}\",""\"paytype\":\"${payInfo['paytype'] ??
    //     ''}\",""\"randomstr\":\"${payInfo['randomstr'] ??
    //     ''}\",""\"remark\":\"${payInfo['remark'] ?? ''}\","
    //     "\"reqsn\":\"${payInfo['reqsn'] ?? ''}\",""\"sign\":\"${
    //     payInfo['sign']??''}\",""\"signtype\":\"${payInfo['signtype'] ??
    //     ''}\",""\"trxamt\":\"${payInfo['trxamt'] ??
    //     ''}\",""\"orgid\":\"${payInfo['orgid'] ??
    //     ''}\",""\"asinfo\":\"${payInfo['asinfo'] ??
    //     ''}\",""\"validtime\":\"${payInfo['validtime'] ??
    //     ''}\",""\"version\":\"${payInfo['version'] ?? ''}\"}";
    // debugPrint('alipay json: $result');

    final encodedJson = Uri.encodeComponent(payInfoString);
    final query = Uri.encodeComponent("payinfo=$encodedJson");

    //按照固定格式拼接完整URL
    final miniProgramPath = "alipays://platformapi/startapp?"
        "appId=2021001104615521&"
        "page=pages/orderDetail/orderDetail&"
        "thirdPartSchema=${Uri.encodeComponent('dxjkpro://sdk/')}'&"
        "query=$query";

    debugPrint('alipay miniProgramPath:$miniProgramPath');
    await launchUrl(
      Uri.parse(miniProgramPath),
      mode: LaunchMode.externalApplication,
    );
    if (Platform.isAndroid) {
      JumpSmallProgramUtils.close('fc2259710004813765', true);
    }

    final appLinks = AppLinks(); // AppLinks is singleton

    final sub = appLinks.uriLinkStream.listen((uri) async {
      debugPrint(
          'appLink uri: ${uri.scheme}===>${uri.queryParameters}=====>${uri.host}');
      if (uri.queryParameters['errmsg'] == '' ||
          uri.queryParameters['errmsg']!.isEmpty) {
        Toast.show('支付成功');
      } else {
        Toast.show('${uri.queryParameters['errmsg']}');
      }

      if (Platform.isAndroid) {
        const orderPath = '/pages/shop/order/list/list';
        JumpSmallProgramUtils.jump(orderPath, "fc2259710004813765");
        await _userRepository.setVipProduct();
        await _userRepository.queryUserInfo(isNeedUpdateInfo: false);
        Get.find<ExaminationController>(tag: ExaminationPageState.key.toString()).update();
      }
      // sub.cancel();
    });
  }
}
