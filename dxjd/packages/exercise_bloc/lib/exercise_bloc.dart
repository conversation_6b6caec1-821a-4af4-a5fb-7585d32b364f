/// Modern exercise module with BLoC architecture
library exercise_bloc;

// Presentation layer exports
export 'src/presentation/pages/exercise_page.dart';
export 'src/presentation/pages/mock_exam_page.dart';
export 'src/presentation/pages/real_exam_page.dart';

// BLoC exports
export 'src/presentation/bloc/exercise/exercise_bloc.dart';
export 'src/presentation/bloc/listen/listen_bloc.dart';
export 'src/presentation/bloc/browse/browse_bloc.dart';
export 'src/presentation/bloc/mode_switch/mode_switch_bloc.dart';

// Domain layer exports
export 'src/domain/entities/question_entity.dart';
export 'src/domain/entities/exercise_progress.dart';
export 'src/domain/repositories/question_repository.dart';
export 'src/domain/repositories/audio_repository.dart';
export 'src/domain/repositories/progress_repository.dart';

// Use cases exports
export 'src/domain/usecases/load_questions.dart';
export 'src/domain/usecases/answer_question.dart';
export 'src/domain/usecases/play_audio.dart';
export 'src/domain/usecases/save_progress.dart';

// Data layer exports (only interfaces, not implementations)
export 'src/data/models/question_model.dart';

// Dependency injection
export 'src/injection/injection.dart';

// Module initialization
export 'src/exercise_module.dart';
