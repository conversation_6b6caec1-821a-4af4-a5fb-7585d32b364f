import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../domain/entities/question_entity.dart';
import '../../../domain/entities/exercise_progress.dart';
import '../../../domain/usecases/load_questions.dart' as usecases;
import '../../../domain/usecases/play_audio.dart' as usecases;
import '../../../domain/usecases/save_progress.dart' as usecases;
import '../../../domain/repositories/audio_repository.dart';
import '../../../domain/repositories/question_repository.dart';
import '../../../domain/usecases/play_audio.dart'
    show AudioNotAvailableException;
import 'listen_event.dart';
import 'listen_state.dart' hide AudioCompleted;

/// BLoC for managing listen state and audio playback
class ListenBloc extends Bloc<ListenEvent, ListenState> {
  ListenBloc({
    required usecases.LoadQuestions loadQuestions,
    required usecases.PlayAudio playAudio,
    required usecases.SaveProgress saveProgress,
    required AudioRepository audioRepository,
  })  : _loadQuestions = loadQuestions,
        _playAudio = playAudio,
        _saveProgress = saveProgress,
        _audioRepository = audioRepository,
        super(const ListenInitial()) {
    on<LoadListenQuestions>(_onLoadListenQuestions);
    on<PlayQuestionAudio>(_onPlayQuestionAudio);
    on<PauseAudio>(_onPauseAudio);
    on<ResumeAudio>(_onResumeAudio);
    on<StopAudio>(_onStopAudio);
    on<SeekAudio>(_onSeekAudio);
    on<JumpToListenQuestion>(_onJumpToListenQuestion);
    on<ToggleQuestionDetails>(_onToggleQuestionDetails);
    on<SetPlaybackSpeed>(_onSetPlaybackSpeed);
    on<AudioCompleted>(_onAudioCompleted);
    on<AudioError>(_onAudioError);
    on<AudioPositionChanged>(_onAudioPositionChanged);
    on<RouteChanged>(_onRouteChanged);
    on<ConfigureAudioSession>(_onConfigureAudioSession);
    on<RemoveListenQuestion>(_onRemoveListenQuestion);

    // Subscribe to audio repository streams
    _initializeAudioStreams();
  }

  final usecases.LoadQuestions _loadQuestions;
  final usecases.PlayAudio _playAudio;
  final usecases.SaveProgress _saveProgress;
  final AudioRepository _audioRepository;

  StreamSubscription<Duration>? _positionSubscription;
  StreamSubscription<Duration>? _durationSubscription;
  StreamSubscription<AudioPlayerState>? _playerStateSubscription;
  StreamSubscription<void>? _completionSubscription;

  /// Initialize audio stream subscriptions
  void _initializeAudioStreams() {
    _positionSubscription = _audioRepository.positionStream.listen(
      (position) {
        if (state is ListenLoaded) {
          final currentState = state as ListenLoaded;
          add(AudioPositionChanged(
            position: position,
            duration: currentState.totalDuration,
          ));
        }
      },
    );

    _durationSubscription = _audioRepository.durationStream.listen(
      (duration) {
        if (state is ListenLoaded) {
          final currentState = state as ListenLoaded;
          add(AudioPositionChanged(
            position: currentState.currentPosition,
            duration: duration,
          ));
        }
      },
    );

    _playerStateSubscription = _audioRepository.playerStateStream.listen(
      (playerState) {
        if (playerState == AudioPlayerState.error) {
          add(const AudioError('Audio playback error'));
        }
      },
    );

    _completionSubscription = _audioRepository.onPlayerComplete.listen(
      (_) => add(const AudioCompleted()),
    );
  }

  @override
  Future<void> close() {
    _positionSubscription?.cancel();
    _durationSubscription?.cancel();
    _playerStateSubscription?.cancel();
    _completionSubscription?.cancel();
    _audioRepository.dispose();
    return super.close();
  }

  /// Handle loading questions for listening
  Future<void> _onLoadListenQuestions(
    LoadListenQuestions event,
    Emitter<ListenState> emit,
  ) async {
    emit(const ListenLoading());

    try {
      final params = usecases.LoadQuestionsParams(
        subject: event.subject,
        type: event.type,
        ids: event.ids,
        sortId: event.sortId,
        mainId: event.mainId,
        childId: event.childId,
        luid: event.luid,
      );

      final questions = await _loadQuestions(params);

      if (questions.isEmpty) {
        emit(const ListenError('No questions found'));
        return;
      }

      // Filter questions that have audio
      final questionsWithAudio = questions
          .where((q) => q.audioUrl != null && q.audioUrl!.isNotEmpty)
          .toList();

      if (questionsWithAudio.isEmpty) {
        emit(const ListenError('No questions with audio found'));
        return;
      }

      final progress = ListenProgress(
        subject: event.subject,
        currentIndex: 0,
        totalQuestions: questionsWithAudio.length,
        playedQuestions: const {},
      );

      // Configure audio session
      await _audioRepository.configureAudioSession(
        enableBackgroundPlayback: true,
        mixWithOthers: false,
      );

      emit(ListenLoaded(
        questions: questionsWithAudio,
        currentIndex: 0,
        progress: progress,
        playerState: AudioPlayerState.stopped,
        currentPosition: Duration.zero,
        totalDuration: Duration.zero,
        playbackSpeed: 1.0,
        showQuestionDetails: false,
        currentPlayingIndex: -1,
      ));
    } catch (e) {
      emit(ListenError('Failed to load questions: ${e.toString()}'));
    }
  }

  /// Handle playing question audio
  Future<void> _onPlayQuestionAudio(
    PlayQuestionAudio event,
    Emitter<ListenState> emit,
  ) async {
    final currentState = state;
    if (currentState is! ListenLoaded) return;

    if (event.questionIndex < 0 ||
        event.questionIndex >= currentState.questions.length) {
      return;
    }

    try {
      final question = currentState.questions[event.questionIndex];

      // Check if clicking on currently playing question (toggle play/pause)
      if (currentState.currentPlayingIndex == event.questionIndex &&
          currentState.isPlaying) {
        add(const PauseAudio());
        return;
      }

      // Check if clicking on currently paused question (resume)
      if (currentState.currentPlayingIndex == event.questionIndex &&
          currentState.isPaused) {
        add(const ResumeAudio());
        return;
      }

      // Play new question
      final params = usecases.PlayAudioParams(question: question);
      await _playAudio(params);

      // Update play statistics
      final updatedQuestion = question.copyWith(
        playCount: question.playCount + 1,
        lastPlayTime: DateTime.now(),
        isPlaying: true,
      );

      final updatedQuestions =
          List<QuestionEntity>.from(currentState.questions);
      updatedQuestions[event.questionIndex] = updatedQuestion;

      // Update progress
      final updatedPlayedQuestions =
          Map<int, int>.from(currentState.progress.playedQuestions);
      updatedPlayedQuestions[question.id] = updatedQuestion.playCount;

      final updatedProgress = currentState.progress.copyWith(
        currentIndex: event.questionIndex,
        playedQuestions: updatedPlayedQuestions,
        isPlaying: true,
      );

      // Save progress
      await _saveProgress(usecases.SaveProgressParams(
        type: usecases.ProgressType.listen,
        listenProgress: updatedProgress,
      ));

      emit(currentState.copyWith(
        questions: updatedQuestions,
        currentIndex: event.questionIndex,
        progress: updatedProgress,
        playerState: AudioPlayerState.playing,
        currentPlayingIndex: event.questionIndex,
      ));
    } catch (e) {
      if (e is AudioNotAvailableException) {
        emit(const ListenError('Audio not available for this question'));
      } else {
        emit(ListenError('Failed to play audio: ${e.toString()}'));
      }
    }
  }

  /// Handle pause audio
  Future<void> _onPauseAudio(
    PauseAudio event,
    Emitter<ListenState> emit,
  ) async {
    final currentState = state;
    if (currentState is! ListenLoaded) return;

    try {
      await _audioRepository.pause();

      // Update currently playing question
      if (currentState.currentPlayingIndex >= 0) {
        final playingQuestion =
            currentState.questions[currentState.currentPlayingIndex];
        final updatedQuestion = playingQuestion.copyWith(isPlaying: false);

        final updatedQuestions =
            List<QuestionEntity>.from(currentState.questions);
        updatedQuestions[currentState.currentPlayingIndex] = updatedQuestion;

        emit(currentState.copyWith(
          questions: updatedQuestions,
          playerState: AudioPlayerState.paused,
        ));
      }
    } catch (e) {
      emit(ListenError('Failed to pause audio: ${e.toString()}'));
    }
  }

  /// Handle resume audio
  Future<void> _onResumeAudio(
    ResumeAudio event,
    Emitter<ListenState> emit,
  ) async {
    final currentState = state;
    if (currentState is! ListenLoaded) return;

    try {
      await _audioRepository.resume();

      // Update currently playing question
      if (currentState.currentPlayingIndex >= 0) {
        final playingQuestion =
            currentState.questions[currentState.currentPlayingIndex];
        final updatedQuestion = playingQuestion.copyWith(isPlaying: true);

        final updatedQuestions =
            List<QuestionEntity>.from(currentState.questions);
        updatedQuestions[currentState.currentPlayingIndex] = updatedQuestion;

        emit(currentState.copyWith(
          questions: updatedQuestions,
          playerState: AudioPlayerState.playing,
        ));
      }
    } catch (e) {
      emit(ListenError('Failed to resume audio: ${e.toString()}'));
    }
  }

  /// Handle stop audio
  Future<void> _onStopAudio(
    StopAudio event,
    Emitter<ListenState> emit,
  ) async {
    final currentState = state;
    if (currentState is! ListenLoaded) return;

    try {
      await _audioRepository.stop();

      // Update all questions to not playing
      final updatedQuestions = currentState.questions
          .map((q) => q.copyWith(isPlaying: false))
          .toList();

      emit(currentState.copyWith(
        questions: updatedQuestions,
        playerState: AudioPlayerState.stopped,
        currentPosition: Duration.zero,
        currentPlayingIndex: -1,
      ));
    } catch (e) {
      emit(ListenError('Failed to stop audio: ${e.toString()}'));
    }
  }

  /// Handle seek audio
  Future<void> _onSeekAudio(
    SeekAudio event,
    Emitter<ListenState> emit,
  ) async {
    try {
      await _audioRepository.seekTo(event.position);
    } catch (e) {
      emit(ListenError('Failed to seek audio: ${e.toString()}'));
    }
  }

  /// Handle jump to specific question
  void _onJumpToListenQuestion(
    JumpToListenQuestion event,
    Emitter<ListenState> emit,
  ) {
    final currentState = state;
    if (currentState is! ListenLoaded) return;

    if (event.index >= 0 && event.index < currentState.questions.length) {
      emit(currentState.copyWith(
        currentIndex: event.index,
        progress: currentState.progress.copyWith(
          currentIndex: event.index,
        ),
      ));
    }
  }

  /// Handle toggle question details
  void _onToggleQuestionDetails(
    ToggleQuestionDetails event,
    Emitter<ListenState> emit,
  ) {
    final currentState = state;
    if (currentState is! ListenLoaded) return;

    emit(currentState.copyWith(
      showQuestionDetails: !currentState.showQuestionDetails,
    ));
  }

  /// Handle set playback speed
  Future<void> _onSetPlaybackSpeed(
    SetPlaybackSpeed event,
    Emitter<ListenState> emit,
  ) async {
    final currentState = state;
    if (currentState is! ListenLoaded) return;

    try {
      await _audioRepository.setPlaybackRate(event.speed);

      emit(currentState.copyWith(
        playbackSpeed: event.speed,
      ));
    } catch (e) {
      emit(ListenError('Failed to set playback speed: ${e.toString()}'));
    }
  }

  /// Handle audio completion
  Future<void> _onAudioCompleted(
    AudioCompleted event,
    Emitter<ListenState> emit,
  ) async {
    final currentState = state;
    if (currentState is! ListenLoaded) return;

    // Update currently playing question
    if (currentState.currentPlayingIndex >= 0) {
      final playingQuestion =
          currentState.questions[currentState.currentPlayingIndex];
      final updatedQuestion = playingQuestion.copyWith(isPlaying: false);

      final updatedQuestions =
          List<QuestionEntity>.from(currentState.questions);
      updatedQuestions[currentState.currentPlayingIndex] = updatedQuestion;

      emit(currentState.copyWith(
        questions: updatedQuestions,
        playerState: AudioPlayerState.completed,
        currentPlayingIndex: -1,
      ));

      // Auto play next question if available
      final nextIndex = currentState.currentPlayingIndex + 1;
      if (nextIndex < currentState.questions.length) {
        await Future.delayed(const Duration(milliseconds: 500));
        add(PlayQuestionAudio(questionIndex: nextIndex, autoPlay: true));
      }
    }
  }

  /// Handle audio error
  void _onAudioError(
    AudioError event,
    Emitter<ListenState> emit,
  ) {
    emit(ListenError('Audio error: ${event.error}'));
  }

  /// Handle audio position changes
  void _onAudioPositionChanged(
    AudioPositionChanged event,
    Emitter<ListenState> emit,
  ) {
    final currentState = state;
    if (currentState is! ListenLoaded) return;

    emit(currentState.copyWith(
      currentPosition: event.position,
      totalDuration: event.duration,
    ));
  }

  /// Handle route changes (pause on navigation)
  Future<void> _onRouteChanged(
    RouteChanged event,
    Emitter<ListenState> emit,
  ) async {
    final currentState = state;
    if (currentState is! ListenLoaded) return;

    if (event.isPushed && currentState.isPlaying) {
      // Pause when navigating away
      add(const PauseAudio());
    }
  }

  /// Handle configure audio session
  Future<void> _onConfigureAudioSession(
    ConfigureAudioSession event,
    Emitter<ListenState> emit,
  ) async {
    try {
      await _audioRepository.configureAudioSession(
        enableBackgroundPlayback: event.enableBackgroundPlayback,
        mixWithOthers: event.mixWithOthers,
      );
    } catch (e) {
      emit(ListenError('Failed to configure audio session: ${e.toString()}'));
    }
  }

  /// Handle remove question from listen list
  Future<void> _onRemoveListenQuestion(
    RemoveListenQuestion event,
    Emitter<ListenState> emit,
  ) async {
    final currentState = state;
    if (currentState is! ListenLoaded) return;

    try {
      // Stop audio if this question is currently playing
      if (currentState.currentPlayingIndex == event.questionIndex) {
        await _audioRepository.stop();
      }

      final updatedQuestions =
          List<QuestionEntity>.from(currentState.questions);
      updatedQuestions.removeAt(event.questionIndex);

      if (updatedQuestions.isEmpty) {
        emit(const ListenError('No more questions available'));
        return;
      }

      // Adjust current index if necessary
      var newIndex = currentState.currentIndex;
      var newPlayingIndex = currentState.currentPlayingIndex;

      if (newIndex >= updatedQuestions.length) {
        newIndex = updatedQuestions.length - 1;
      }

      if (newPlayingIndex == event.questionIndex) {
        newPlayingIndex = -1; // Stop playing
      } else if (newPlayingIndex > event.questionIndex) {
        newPlayingIndex -= 1; // Adjust index
      }

      final updatedProgress = currentState.progress.copyWith(
        currentIndex: newIndex,
        totalQuestions: updatedQuestions.length,
      );

      emit(currentState.copyWith(
        questions: updatedQuestions,
        currentIndex: newIndex,
        progress: updatedProgress,
        currentPlayingIndex: newPlayingIndex,
        playerState: newPlayingIndex == -1
            ? AudioPlayerState.stopped
            : currentState.playerState,
      ));
    } catch (e) {
      emit(ListenError('Failed to remove question: ${e.toString()}'));
    }
  }
}
