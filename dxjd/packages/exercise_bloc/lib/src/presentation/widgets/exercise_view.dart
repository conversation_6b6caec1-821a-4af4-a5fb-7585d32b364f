import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/entities/question_entity.dart';
import '../bloc/exercise/exercise_bloc.dart';
import '../bloc/exercise/exercise_event.dart';
import '../bloc/exercise/exercise_state.dart';
import 'common/exercise_styles.dart';
import 'common/option_widget.dart';
import 'common/question_image_widget.dart';

/// Exercise view widget for interactive question answering
/// Matches the original DoExercises implementation
class ExerciseView extends StatefulWidget {
  const ExerciseView({
    super.key,
    required this.subject,
    required this.type,
    this.sortId,
    this.mainId,
    this.childId,
    this.luid,
    this.ids,
  });

  final int subject;
  final String type;
  final int? sortId;
  final int? mainId;
  final int? childId;
  final int? luid;
  final List<dynamic>? ids;

  @override
  State<ExerciseView> createState() => _ExerciseViewState();
}

class _ExerciseViewState extends State<ExerciseView> {
  late PageController _pageController;
  String _tempAnswer = ''; // Temporary answer for multiple choice

  @override
  void initState() {
    super.initState();
    _pageController = PageController();

    // Load questions when widget initializes
    context.read<ExerciseBloc>().add(LoadQuestions(
          subject: widget.subject,
          type: widget.type,
          sortId: widget.sortId,
          mainId: widget.mainId,
          childId: widget.childId,
          luid: widget.luid,
          ids: widget.ids?.cast<int>(),
        ));
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ExerciseBloc, ExerciseState>(
      listener: (context, state) {
        if (state is ExerciseLoaded) {
          // Handle navigation to specific question
          if (state.currentIndex != _pageController.page?.round()) {
            _pageController.animateToPage(
              state.currentIndex,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          }

          // Reset temp answer when question changes
          final currentQuestion = state.questions[state.currentIndex];
          _tempAnswer = currentQuestion.userAnswer ?? '';
        }
      },
      builder: (context, state) {
        if (state is ExerciseLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (state is ExerciseError) {
          return _buildErrorWidget(state.message);
        }

        if (state is ExerciseLoaded) {
          return _buildExerciseContent(state);
        }

        return const Center(
          child: Text('No questions available'),
        );
      },
    );
  }

  Widget _buildErrorWidget(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: ExerciseStyles.dp(64),
            color: ExerciseStyles.red,
          ),
          SizedBox(height: ExerciseStyles.dp(16)),
          Text(
            'Error: $message',
            style: ExerciseStyles.styleText1H16W500,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: ExerciseStyles.dp(16)),
          ElevatedButton(
            onPressed: () {
              context.read<ExerciseBloc>().add(LoadQuestions(
                    subject: widget.subject,
                    type: widget.type,
                    sortId: widget.sortId,
                    mainId: widget.mainId,
                    childId: widget.childId,
                    luid: widget.luid,
                    ids: widget.ids?.cast<int>(),
                  ));
            },
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  Widget _buildExerciseContent(ExerciseLoaded state) {
    return PageView.builder(
      controller: _pageController,
      onPageChanged: (index) {
        context.read<ExerciseBloc>().add(JumpToQuestion(index));
      },
      itemCount: state.questions.length,
      itemBuilder: (context, index) {
        final question = state.questions[index];
        return _buildQuestionPage(question, state);
      },
    );
  }

  Widget _buildQuestionPage(QuestionEntity question, ExerciseLoaded state) {
    final userAnswer = question.userAnswer ?? '';

    return Container(
      key: ValueKey(question.id),
      color: Colors.white,
      child: SingleChildScrollView(
        child: Column(
          children: [
            // Question title with highlighting
            Container(
              padding: EdgeInsets.only(
                top: ExerciseStyles.dp(16),
                left: ExerciseStyles.dp(16),
                right: ExerciseStyles.dp(16),
              ),
              alignment: AlignmentDirectional.centerStart,
              child: RichText(
                text: TextSpan(
                  style: ExerciseStyles.styleText1H18W500,
                  children: _buildQuestionTitleSpans(question),
                ),
              ),
            ),

            // Question image
            QuestionImageWidget(
              question: question,
              onImageTap: () => _showImageDialog(question.imagePath),
            ),

            SizedBox(height: ExerciseStyles.dp(20)),

            // Options
            if (question.options.isNotEmpty) ...[
              for (final option in question.options)
                if (option.text.isNotEmpty)
                  OptionWidget(
                    option: option.key,
                    text: option.text,
                    question: question,
                    userAnswer: question.type == QuestionType.multiple
                        ? _tempAnswer
                        : userAnswer,
                    onTap: () => _handleOptionTap(option.key, question, state),
                    isEnabled: !question.isAnswered,
                  ),
            ],

            // Submit button for multiple choice
            if (question.type == QuestionType.multiple && !question.isAnswered)
              Container(
                margin: EdgeInsets.only(
                  top: ExerciseStyles.dp(40),
                  left: ExerciseStyles.dp(24),
                  right: ExerciseStyles.dp(24),
                  bottom: ExerciseStyles.dp(12),
                ),
                child: GestureDetector(
                  onTap: _tempAnswer.isNotEmpty
                      ? () => _submitMultipleChoice(question)
                      : null,
                  child: Container(
                    alignment: AlignmentDirectional.center,
                    height: ExerciseStyles.dp(46),
                    decoration: _tempAnswer.isNotEmpty
                        ? ExerciseStyles.styleBtnBlue
                        : ExerciseStyles.styleBtnBlue.copyWith(
                            gradient: const LinearGradient(
                              colors: [Colors.grey, Colors.grey],
                            ),
                          ),
                    child: Text(
                      '确认答案',
                      style: ExerciseStyles.styleWhite16W500,
                    ),
                  ),
                ),
              ),

            // Video section
            if (question.videoPath != null && question.videoPath!.isNotEmpty)
              QuestionVideoWidget(
                question: question,
                onVideoTap: () => _playVideo(question.videoPath!),
              ),

            // Explanation
            if (question.isAnswered)
              QuestionExplanationWidget(
                explanation: question.explanation,
                isVisible: true,
              ),

            SizedBox(height: ExerciseStyles.dp(100)), // Bottom padding
          ],
        ),
      ),
    );
  }

  List<TextSpan> _buildQuestionTitleSpans(QuestionEntity question) {
    // Build question title with type prefix and highlighting
    final typePrefix = _getQuestionTypePrefix(question.type);
    final title = question.title;

    // For now, return simple spans. In the original, this handles keyword highlighting
    return [
      TextSpan(text: typePrefix),
      TextSpan(text: title),
    ];
  }

  String _getQuestionTypePrefix(QuestionType type) {
    switch (type) {
      case QuestionType.single:
        return '[单选题] ';
      case QuestionType.multiple:
        return '[多选题] ';
      case QuestionType.judge:
        return '[判断题] ';
    }
  }

  void _handleOptionTap(
      String optionKey, QuestionEntity question, ExerciseLoaded state) {
    if (question.isAnswered) return;

    if (question.type == QuestionType.multiple) {
      // Handle multiple choice selection
      final currentAnswers = _tempAnswer
          .toLowerCase()
          .split('')
          .where((s) => s.isNotEmpty)
          .toSet();
      if (currentAnswers.contains(optionKey.toLowerCase())) {
        currentAnswers.remove(optionKey.toLowerCase());
      } else {
        currentAnswers.add(optionKey.toLowerCase());
      }

      setState(() {
        _tempAnswer = currentAnswers.join('');
      });
    } else {
      // Handle single choice - auto submit
      final questionIndex = state.questions.indexOf(question);
      context.read<ExerciseBloc>().add(AnswerQuestion(
            questionIndex: questionIndex,
            answer: optionKey,
          ));
    }
  }

  void _submitMultipleChoice(QuestionEntity question) {
    if (_tempAnswer.isNotEmpty) {
      // Find the question index in the current state
      final bloc = context.read<ExerciseBloc>();
      final state = bloc.state;
      if (state is ExerciseLoaded) {
        final questionIndex = state.questions.indexOf(question);
        bloc.add(AnswerQuestion(
          questionIndex: questionIndex,
          answer: _tempAnswer,
        ));
      }
    }
  }

  void _showImageDialog(String? imagePath) {
    if (imagePath == null || imagePath.isEmpty) return;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          constraints: BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width * 0.9,
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              AppBar(
                title: const Text('查看图片'),
                automaticallyImplyLeading: false,
                actions: [
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
              Expanded(
                child: InteractiveViewer(
                  child: Image.asset(
                    'assets/quiz/$imagePath',
                    fit: BoxFit.contain,
                    errorBuilder: (context, error, stackTrace) => const Center(
                      child: Text('图片加载失败'),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _playVideo(String videoPath) {
    // TODO: Implement video playback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('播放视频: $videoPath')),
    );
  }
}
