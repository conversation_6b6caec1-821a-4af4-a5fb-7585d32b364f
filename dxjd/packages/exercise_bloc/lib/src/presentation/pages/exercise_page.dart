import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tools/tools.dart';

import '../bloc/exercise/exercise_bloc.dart';
import '../bloc/exercise/exercise_event.dart';
import '../widgets/exercise_view.dart';
import '../widgets/listen_view.dart';
import '../widgets/browse_view.dart';
import '../widgets/video_view.dart';

/// Exercise page that completely replicates the original PageChapter implementation
class ExercisePage extends StatefulWidget {
  const ExercisePage({
    super.key,
    required this.subject,
    required this.type,
    this.sortId,
    this.title = '练习',
    this.isExamMode = false,
    this.examTimeLimit,
    this.examQuestionCount,
  });

  final int subject;
  final String type;
  final int? sortId;
  final String title;
  final bool isExamMode;
  final int? examTimeLimit;
  final int? examQuestionCount;

  @override
  State<ExercisePage> createState() => _ExercisePageState();
}

class _ExercisePageState extends State<ExercisePage> {
  final List<String> _tabs = ['做题', '听题', '背题', '视频'];
  int _currentTabIndex = 0;

  @override
  void initState() {
    super.initState();
    _initData();
  }

  void _initData() {
    context.read<ExerciseBloc>().add(LoadQuestions(
          subject: widget.subject,
          type: widget.type,
          sortId: widget.sortId,
        ));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            _buildCustomAppBar(),
            Expanded(child: _buildTabContent()),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomAppBar() {
    return Container(
      height: UIUtils.dp(48),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Color(0x1A000000),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          Positioned(
            left: 0,
            top: 0,
            bottom: 0,
            child: GestureDetector(
              onTap: () => Navigator.pop(context),
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: UIUtils.dp(16)),
                alignment: Alignment.center,
                child: Icon(
                  Icons.arrow_back_ios,
                  size: UIUtils.dp(20),
                  color: const Color(0xFF333333),
                ),
              ),
            ),
          ),
          Container(
            alignment: Alignment.center,
            child: Container(
              width: UIUtils.dp(204),
              height: UIUtils.dp(32),
              decoration: BoxDecoration(
                color: const Color(0x991992EF),
                borderRadius: BorderRadius.circular(UIUtils.dp(8)),
              ),
              child: Row(
                children: _tabs.asMap().entries.map((entry) {
                  final index = entry.key;
                  final title = entry.value;
                  final isSelected = index == _currentTabIndex;

                  return Expanded(
                    child: GestureDetector(
                      onTap: () => setState(() => _currentTabIndex = index),
                      child: Container(
                        height: UIUtils.dp(32),
                        decoration: isSelected
                            ? BoxDecoration(
                                color: const Color(0xFF1992EF),
                                borderRadius:
                                    BorderRadius.circular(UIUtils.dp(8)),
                              )
                            : null,
                        alignment: Alignment.center,
                        child: Text(
                          title,
                          style: isSelected
                              ? const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                )
                              : const TextStyle(
                                  color: Color(0xFF666666),
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
          Positioned(
            right: 0,
            top: 0,
            bottom: 0,
            child: GestureDetector(
              onTap: () {},
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: UIUtils.dp(16)),
                alignment: Alignment.center,
                child: const Text(
                  '字体',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF333333),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabContent() {
    switch (_currentTabIndex) {
      case 0:
        return ExerciseView(
          subject: widget.subject,
          type: widget.type,
          sortId: widget.sortId,
        );
      case 1:
        return ListenView(
          subject: widget.subject,
          type: widget.type,
          sortId: widget.sortId,
        );
      case 2:
        return BrowseView(
          subject: widget.subject,
          type: widget.type,
          sortId: widget.sortId,
        );
      case 3:
        return VideoView(
          subject: widget.subject,
          type: widget.type,
          sortId: widget.sortId,
        );
      default:
        return ExerciseView(
          subject: widget.subject,
          type: widget.type,
          sortId: widget.sortId,
        );
    }
  }
}
