import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/question_entity.dart';

part 'question_model.g.dart';

/// Data model for question (maps to/from JSON)
@JsonSerializable()
class QuestionModel {
  const QuestionModel({
    required this.id,
    required this.sortId,
    required this.title,
    required this.type,
    required this.options,
    required this.correctAnswer,
    this.imagePath,
    this.videoPath,
    this.audioUrl,
    this.explanation,
    this.skills,
    this.userAnswer,
    this.isAnswered = false,
    this.isCorrect = false,
    this.isCollected = false,
    this.answerCount = 0,
    this.correctCount = 0,
    this.errorCount = 0,
    this.playCount = 0,
    this.lastPlayTime,
    this.isPlaying = false,
  });

  @JsonKey(name: 'sub_Id')
  final int id;
  
  @<PERSON>son<PERSON><PERSON>(name: 'sort_id')
  final int sortId;
  
  @<PERSON>sonKey(name: 'sub_Titles')
  final String title;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'sub_type')
  final String type;
  
  final List<QuestionOptionModel> options;
  
  final String correctAnswer;
  
  @Json<PERSON>ey(name: 'image_path')
  final String? imagePath;
  
  @Json<PERSON>ey(name: 'video_path')
  final String? videoPath;
  
  @JsonKey(name: 'audio_url')
  final String? audioUrl;
  
  final String? explanation;
  
  final String? skills;
  
  @JsonKey(name: 'user_answer')
  final String? userAnswer;
  
  @JsonKey(name: 'is_answered')
  final bool isAnswered;
  
  @JsonKey(name: 'is_correct')
  final bool isCorrect;
  
  @JsonKey(name: 'is_collected')
  final bool isCollected;
  
  @JsonKey(name: 'answer_count')
  final int answerCount;
  
  @JsonKey(name: 'correct_count')
  final int correctCount;
  
  @JsonKey(name: 'error_count')
  final int errorCount;
  
  @JsonKey(name: 'play_count')
  final int playCount;
  
  @JsonKey(name: 'last_play_time')
  final DateTime? lastPlayTime;
  
  @JsonKey(name: 'is_playing')
  final bool isPlaying;

  /// Convert to domain entity
  QuestionEntity toEntity() {
    return QuestionEntity(
      id: id,
      sortId: sortId,
      title: title,
      type: _parseQuestionType(type),
      options: options.map((o) => o.toEntity()).toList(),
      correctAnswer: correctAnswer,
      imagePath: imagePath,
      videoPath: videoPath,
      audioUrl: audioUrl,
      explanation: explanation,
      skills: skills,
      userAnswer: userAnswer,
      isAnswered: isAnswered,
      isCorrect: isCorrect,
      isCollected: isCollected,
      answerCount: answerCount,
      correctCount: correctCount,
      errorCount: errorCount,
      playCount: playCount,
      lastPlayTime: lastPlayTime,
      isPlaying: isPlaying,
    );
  }

  /// Create from domain entity
  factory QuestionModel.fromEntity(QuestionEntity entity) {
    return QuestionModel(
      id: entity.id,
      sortId: entity.sortId,
      title: entity.title,
      type: _questionTypeToString(entity.type),
      options: entity.options.map((o) => QuestionOptionModel.fromEntity(o)).toList(),
      correctAnswer: entity.correctAnswer,
      imagePath: entity.imagePath,
      videoPath: entity.videoPath,
      audioUrl: entity.audioUrl,
      explanation: entity.explanation,
      skills: entity.skills,
      userAnswer: entity.userAnswer,
      isAnswered: entity.isAnswered,
      isCorrect: entity.isCorrect,
      isCollected: entity.isCollected,
      answerCount: entity.answerCount,
      correctCount: entity.correctCount,
      errorCount: entity.errorCount,
      playCount: entity.playCount,
      lastPlayTime: entity.lastPlayTime,
      isPlaying: entity.isPlaying,
    );
  }

  /// Parse question type from string
  static QuestionType _parseQuestionType(String type) {
    switch (type.toLowerCase()) {
      case 'single':
      case '1':
        return QuestionType.single;
      case 'multiple':
      case '2':
        return QuestionType.multiple;
      case 'judge':
      case '3':
        return QuestionType.judge;
      default:
        return QuestionType.single;
    }
  }

  /// Convert question type to string
  static String _questionTypeToString(QuestionType type) {
    switch (type) {
      case QuestionType.single:
        return 'single';
      case QuestionType.multiple:
        return 'multiple';
      case QuestionType.judge:
        return 'judge';
    }
  }

  /// JSON serialization
  factory QuestionModel.fromJson(Map<String, dynamic> json) =>
      _$QuestionModelFromJson(json);

  Map<String, dynamic> toJson() => _$QuestionModelToJson(this);
}

/// Data model for question option
@JsonSerializable()
class QuestionOptionModel {
  const QuestionOptionModel({
    required this.key,
    required this.text,
    this.imagePath,
  });

  final String key;
  final String text;
  
  @JsonKey(name: 'image_path')
  final String? imagePath;

  /// Convert to domain entity
  QuestionOption toEntity() {
    return QuestionOption(
      key: key,
      text: text,
      imagePath: imagePath,
    );
  }

  /// Create from domain entity
  factory QuestionOptionModel.fromEntity(QuestionOption entity) {
    return QuestionOptionModel(
      key: entity.key,
      text: entity.text,
      imagePath: entity.imagePath,
    );
  }

  /// JSON serialization
  factory QuestionOptionModel.fromJson(Map<String, dynamic> json) =>
      _$QuestionOptionModelFromJson(json);

  Map<String, dynamic> toJson() => _$QuestionOptionModelToJson(this);
}

/// Legacy compatibility - convert from original QuizMo format
extension QuestionModelLegacy on QuestionModel {
  /// Create from legacy QuizMo format
  factory QuestionModel.fromLegacyQuizMo(Map<String, dynamic> json) {
    // Extract options from legacy format
    final options = <QuestionOptionModel>[];
    
    if (json['optA'] != null && json['optA'].toString().isNotEmpty) {
      options.add(QuestionOptionModel(key: 'a', text: json['optA'].toString()));
    }
    if (json['optB'] != null && json['optB'].toString().isNotEmpty) {
      options.add(QuestionOptionModel(key: 'b', text: json['optB'].toString()));
    }
    if (json['optC'] != null && json['optC'].toString().isNotEmpty) {
      options.add(QuestionOptionModel(key: 'c', text: json['optC'].toString()));
    }
    if (json['optD'] != null && json['optD'].toString().isNotEmpty) {
      options.add(QuestionOptionModel(key: 'd', text: json['optD'].toString()));
    }

    return QuestionModel(
      id: json['sub_Id'] ?? 0,
      sortId: json['sort_id'] ?? 0,
      title: json['sub_Titles'] ?? '',
      type: json['sub_type'] ?? 'single',
      options: options,
      correctAnswer: json['answer'] ?? '',
      imagePath: json['image_path'],
      videoPath: json['video_path'],
      audioUrl: json['audio_url'],
      explanation: json['explanation'],
      skills: json['skills'],
      userAnswer: json['selection'],
      isAnswered: json['isDone'] ?? false,
      isCorrect: json['examstate'] == 1,
      isCollected: json['is_collected'] ?? false,
      answerCount: json['answer_count'] ?? 0,
      correctCount: json['correct_count'] ?? 0,
      errorCount: json['error_count'] ?? 0,
      playCount: json['play_count'] ?? 0,
      lastPlayTime: json['last_play_time'] != null 
          ? DateTime.tryParse(json['last_play_time'].toString())
          : null,
      isPlaying: json['is_playing'] ?? false,
    );
  }
}
