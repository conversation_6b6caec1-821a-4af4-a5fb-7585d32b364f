import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:exercise_bloc/exercise_bloc.dart';

/// 模拟考试页面 - 展示计时考试功能
class MockExamPage extends StatefulWidget {
  const MockExamPage({super.key});

  @override
  State<MockExamPage> createState() => _MockExamPageState();
}

class _MockExamPageState extends State<MockExamPage> {
  Timer? _timer;
  int _remainingSeconds = 45 * 60; // 45分钟
  bool _isExamStarted = false;
  bool _isExamCompleted = false;

  @override
  void initState() {
    super.initState();
    _startExam();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startExam() {
    setState(() {
      _isExamStarted = true;
    });

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_remainingSeconds > 0) {
        setState(() {
          _remainingSeconds--;
        });
      } else {
        _completeExam();
      }
    });
  }

  void _completeExam() {
    _timer?.cancel();
    setState(() {
      _isExamCompleted = true;
    });
    _showExamResultDialog();
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<ExerciseBloc>(
          create: (context) => getIt<ExerciseBloc>()
            ..add(const LoadQuestions(
              subject: 1,
              type: 'mock',
              sortId: 0,
            )),
        ),
      ],
      child: Scaffold(
        backgroundColor: Colors.white,
        body: SafeArea(
          child: Column(
            children: [
              // 考试头部信息
              _buildExamHeader(),

              // 考试内容
              Expanded(
                child: BlocBuilder<ExerciseBloc, ExerciseState>(
                  builder: (context, state) {
                    if (state is ExerciseLoading) {
                      return const Center(
                        child: CircularProgressIndicator(),
                      );
                    } else if (state is ExerciseLoaded) {
                      return Column(
                        children: [
                          // 题目进度条
                          _buildProgressBar(state),

                          // 题目内容
                          Expanded(
                            child: const ExerciseView(
                              subject: 1,
                              type: 'mock',
                            ),
                          ),

                          // 考试控制按钮
                          _buildExamControls(state),
                        ],
                      );
                    } else if (state is ExerciseError) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.error,
                                size: 64, color: Colors.red),
                            const SizedBox(height: 16),
                            Text(state.message),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: () => Navigator.pop(context),
                              child: const Text('返回'),
                            ),
                          ],
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildExamHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Color(0xFF1992EF),
        boxShadow: [
          BoxShadow(
            color: Color(0x1A000000),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              IconButton(
                onPressed: () => _showExitConfirmDialog(),
                icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
              ),
              const Expanded(
                child: Text(
                  '模拟考试',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
              IconButton(
                onPressed: () => _showExamInfoDialog(),
                icon: const Icon(Icons.info_outline, color: Colors.white),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 考试信息卡片
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildExamInfoItem(
                  icon: Icons.timer,
                  label: '剩余时间',
                  value: _formatTime(_remainingSeconds),
                  valueColor: _remainingSeconds < 300
                      ? Colors.red
                      : const Color(0xFF1992EF),
                ),
                _buildExamInfoItem(
                  icon: Icons.quiz,
                  label: '考试科目',
                  value: '科目一',
                  valueColor: const Color(0xFF1992EF),
                ),
                _buildExamInfoItem(
                  icon: Icons.assignment,
                  label: '题目总数',
                  value: '100题',
                  valueColor: const Color(0xFF1992EF),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExamInfoItem({
    required IconData icon,
    required String label,
    required String value,
    required Color valueColor,
  }) {
    return Column(
      children: [
        Icon(icon, color: const Color(0xFF666666), size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: valueColor,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Color(0xFF666666),
          ),
        ),
      ],
    );
  }

  Widget _buildProgressBar(ExerciseLoaded state) {
    final progress = (state.currentQuestionIndex + 1) / state.questions.length;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '第 ${state.currentQuestionIndex + 1} 题',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                '${state.currentQuestionIndex + 1}/${state.questions.length}',
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF666666),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: const Color(0xFFE0E0E0),
            valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFF1992EF)),
          ),
        ],
      ),
    );
  }

  Widget _buildExamControls(ExerciseLoaded state) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Color(0xFFF5F7FA),
        border: Border(
          top: BorderSide(color: Color(0xFFE0E0E0)),
        ),
      ),
      child: Row(
        children: [
          // 上一题按钮
          Expanded(
            child: ElevatedButton(
              onPressed: false // 暂时禁用
                  ? () =>
                      context.read<ExerciseBloc>().add(const PreviousQuestion())
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: const Color(0xFF666666),
                side: const BorderSide(color: Color(0xFFE0E0E0)),
              ),
              child: const Text('上一题'),
            ),
          ),

          const SizedBox(width: 16),

          // 下一题按钮
          Expanded(
            child: ElevatedButton(
              onPressed: false // 暂时禁用
                  ? () => context.read<ExerciseBloc>().add(const NextQuestion())
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF1992EF),
                foregroundColor: Colors.white,
              ),
              child: const Text('下一题'),
            ),
          ),

          const SizedBox(width: 16),

          // 交卷按钮
          ElevatedButton(
            onPressed: () => _showSubmitConfirmDialog(state),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFF6B35),
              foregroundColor: Colors.white,
            ),
            child: const Text('交卷'),
          ),
        ],
      ),
    );
  }

  void _checkExamCompletion(ExerciseLoaded state) {
    // 检查是否所有题目都已回答
    final answeredCount =
        state.questions.where((q) => q.userAnswer?.isNotEmpty == true).length;
    if (answeredCount == state.questions.length) {
      _completeExam();
    }
  }

  void _showExitConfirmDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认退出'),
        content: const Text('考试正在进行中，确定要退出吗？退出后考试记录将不会保存。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('继续考试'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            child: const Text('确认退出'),
          ),
        ],
      ),
    );
  }

  void _showSubmitConfirmDialog(ExerciseLoaded state) {
    final answeredCount =
        state.questions.where((q) => q.userAnswer?.isNotEmpty == true).length;
    final unansweredCount = state.questions.length - answeredCount;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认交卷'),
        content: Text(
          unansweredCount > 0
              ? '还有 $unansweredCount 道题未作答，确定要交卷吗？'
              : '所有题目已完成，确定要交卷吗？',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('继续答题'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _completeExam();
            },
            child: const Text('确认交卷'),
          ),
        ],
      ),
    );
  }

  void _showExamInfoDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('考试说明'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('考试规则:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('• 考试时间：45分钟'),
              Text('• 题目数量：100题'),
              Text('• 及格分数：90分'),
              Text('• 每题1分'),
              SizedBox(height: 16),
              Text('注意事项:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('• 请在规定时间内完成所有题目'),
              Text('• 可以随时查看已答题目'),
              Text('• 交卷后无法修改答案'),
              Text('• 时间到自动交卷'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('知道了'),
          ),
        ],
      ),
    );
  }

  void _showExamResultDialog() {
    // 这里应该计算实际的考试结果
    final score = 85; // 示例分数
    final isPassed = score >= 90;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text(
          isPassed ? '考试通过' : '考试未通过',
          style: TextStyle(
            color: isPassed ? Colors.green : Colors.red,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isPassed ? Icons.check_circle : Icons.cancel,
              size: 64,
              color: isPassed ? Colors.green : Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              '考试分数: $score 分',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            Text(
              isPassed ? '恭喜通过考试！' : '继续努力，下次一定能通过！',
              style: const TextStyle(fontSize: 14),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            child: const Text('返回'),
          ),
        ],
      ),
    );
  }
}
