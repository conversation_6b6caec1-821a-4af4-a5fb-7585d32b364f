import 'dart:async';

import 'package:audioplayers/audioplayers.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:common_utils/common_utils.dart';
import 'package:component_library/component_library.dart';
import 'package:dxjd/main.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'package:tools/tools.dart';

import '../../../quiz.dart';
import '../../bean/quiz.dart';
import '../../data/i_quiz.dart';
import '../../data/quiz_utils.dart';
import '../../utils/jump.dart';
import 'option.dart';

StreamSubscription? _ibus;

class Listen extends StatefulWidget {
  const Listen({Key? key, this.onPlaying, this.onPaly}) : super(key: key);
  final Function(int index)? onPlaying;
  final Function(int index)? onPaly;
  @override
  _ListenState createState() => _ListenState();

  static Future<void> checkRemove(int index) async {
    try {
      _ListenState.state?.checkRemove(index);
    } catch (e) {}
  }

  static Future<void> pause() async {
    try {
      _ListenState.state?.pause();
    } catch (e) {}
  }

  static Future<void> resume() async {
    try {
      _ListenState.state?.resume();
    } catch (e) {}
  }

  static Future<void> stop() async {
    try {
      _ListenState.state?.stop();
    } catch (e) {}
  }

  static Future<void> release() async {
    try {
      _ListenState.state?.release();
    } catch (e) {}
  }
}

class _ListenState extends State<Listen>
    with WidgetsBindingObserver, RouteAware {
  final ItemScrollController itemScrollController = ItemScrollController();
  final ItemPositionsListener itemPositionsListener =
      ItemPositionsListener.create();

  AudioPlayer audioPlayer = AudioPlayer();

  int current = -1;
  Duration? seekTime;
  QuizMo? currentMo;
  static _ListenState? state;

  _ListenState() {
    state = this;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 订阅路由观察者
    // 获取当前路由
    final route = ModalRoute.of(context);
    // 检查路由是否为 PageRoute 类型
    if (route is PageRoute) {
      routeObserver.subscribe(this, route);
    }
  }

  // 当跳转到其他页面时暂停播放
  @override
  void didPushNext() async {
    audioPlayer.pause();
    seekTime = await audioPlayer.getCurrentPosition();
    currentMo?.isPlay = false;
    super.didPushNext();
  }

  // 当返回到当前页面时恢复播放
  @override
  void didPopNext() {
    super.didPopNext();
  }

  @override
  void initState() {
    super.initState();
    // 订阅事件
    _ibus = IEventBus.get().register<IEvent>((event) {
      if (event.type == JkKey.EVENT_LISTEN_SHOW) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          onPagerPosition(IQuiz.get().currentpage);
        });
        // refreshUI();
      } else if (JkKey.EVENT_QUIZ_INDEX == event.type) {
        int index = event.object;
        onPagerPosition(index);
      }
    });
    WidgetsBinding.instance.addPostFrameCallback((_) {
      current = -1;
      autoPlay(IQuiz.get().currentpage);
      if (widget.onPlaying != null) {
        widget.onPlaying!(IQuiz.get().currentpage);
      }
    });
    AudioPlayer.global.setGlobalAudioContext(
      AudioContext(
        iOS: AudioContextIOS(
          category: AVAudioSessionCategory.playback,
          options: [
            AVAudioSessionOptions.mixWithOthers,
            AVAudioSessionOptions.defaultToSpeaker,
          ],
          defaultToSpeaker: true,
        ),
        android: AudioContextAndroid(
          isSpeakerphoneOn: true,
          stayAwake: true,
          contentType: AndroidContentType.speech,
          usageType: AndroidUsageType.media,
          audioFocus: AndroidAudioFocus.gainTransientExclusive,
        ),
      ),
    );
    audioPlayer.onPlayerStateChanged.listen((s) async {
      // p参数可以获取当前进度，也是可以调整的，比如p.inMilliseconds
      if (s == PlayerState.completed) {
        if (current != -1 && current < IQuiz.get().quizList.length - 2) {
          //音频播放
          autoPlay(current + 1);
        }
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    release();
    IEventBus.get().unregister(_ibus);
  }

  void refreshUI() {
    try {
      if (mounted) {
        setState(() {});
      }
    } catch (e) {}
  }

  void onPagerPosition(int index) {
    IQuiz.get().currentpage = index;
    itemScrollController.jumpTo(index: index);
  }

  void onPaly() {
    if (widget.onPaly != null) {
      widget.onPaly!(current);
    }
    if (current != IQuiz.get().currentpage) {
      stop();
    }
  }

  void stop() {
    try {
      current = -1;
      audioPlayer.stop();
    } catch (e) {}
  }

  Future<void> pause() async {
    if (audioPlayer.state == PlayerState.playing) {
      audioPlayer.pause();
      seekTime = await audioPlayer.getCurrentPosition();
    }
  }

  Future<void> resume() async {
    if (current == IQuiz.get().currentpage) {
      audioPlayer.resume();
      audioPlayer.seek(seekTime ?? Duration());
    } else {
      current = -1;
      autoPlay(IQuiz.get().currentpage);
      if (widget.onPlaying != null) {
        widget.onPlaying!(IQuiz.get().currentpage);
      }
    }
  }

  Future<void> release() async {
    try {
      current = -1;
      await audioPlayer.stop();
      await audioPlayer.release();
      print('############ 释放播放音频 ############');
    } catch (e) {}
  }

  void autoPlay(int index) {
    onPagerPosition(index);
    print("autoPlay playing = ${index}");
    QuizMo? mo = IQuiz.get().getQuiz(index);
    if (mo != null) {
      playing(index, mo);
    }
  }

  void checkRemove(int index) {
    if (current < index) {
      current = index;
    } else if (current > index) {
      current = index - 1;
    } else {
      current = -1;
    }
    refreshUI();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: false,
      body: ScrollablePositionedList.builder(
        itemCount: IQuiz.get().quizList.length,
        itemBuilder: (context, index) {
          return ItemView(
            index: index,
            mo: IQuiz.get().quizList[index],
            current: current,
            onPlaying: (int index, QuizMo mo) {
              onPagerPosition(index);
              if (widget.onPlaying != null) {
                widget.onPlaying!(index);
              }
              //音频播放
              QuizMo? mo = IQuiz.get().getQuiz(index);
              if (mo != null) {
                playing(index, mo);
              }
              refreshUI();
            },
          );
        },
        itemScrollController: itemScrollController,
        itemPositionsListener: itemPositionsListener,
      ),
    );
  }

  void playing(int index, QuizMo mo) async {
    currentMo = mo;
    if (IQuiz.get().isSkill(index)) {
      print('current = ${current}, index = ${index}');
      if (current == index) {
        try {
          print(
              'state = ${audioPlayer.state}, playing = ${PlayerState.playing}');
          if (audioPlayer.state == PlayerState.playing) {
            audioPlayer.pause();
            seekTime = await audioPlayer.getCurrentPosition();
            mo.isPlay = false;
          } else {
            audioPlayer.resume();
            audioPlayer.seek(seekTime ?? Duration());
            mo.isPlay = true;
          }
        } catch (e) {}
        current = index;
      } else {
        // await audioPlayer.stop();
        // await audioPlayer.release();
        audioPlayer.play(UrlSource('${ApiUrl.MUSIC_URL}${mo.sub_Id}.mp3'));
        mo.isPlay = true;
        int count = mo.playCount;
        mo.playCount = count + 1;
        mo.playTime =
            DateUtil.formatDate(DateTime.now(), format: 'MM/dd HH:mm');
        mo.uid = ITools.get().getUid('0');
        current = index;
        QuizUtils.get().saveListen(mo);
      }
    } else {
      current = -1;
      DialogHelper.showDialogTips(
          context, 0, '试听结束', '完整听题包需要购买VIP才能使用', '取消', '去购买',
          (String? type) async {
        if (type == 'confirm') {
          BuryingPointUtils.instance.addPoint(
              buryingPointList: BuryingPointList(
                  eventType: 1, entranceType: 2, action: 1, browseDuration: 0));
          JumpUtils.get().jumpBuyVip(type: 'lianxutingti', vipType: 66);
        }
      });
    }
    onPaly();
    refreshUI();
  }

  void finish() {
    Navigator.of(context).pop();
  }
}

class ItemView extends StatefulWidget {
  const ItemView(
      {Key? key, this.index, this.mo, this.current = -1, this.onPlaying})
      : super(key: key);

  final int? index;
  final QuizMo? mo;
  final int? current;
  final Function(int index, QuizMo mo)? onPlaying;

  @override
  _ItemViewState createState() => _ItemViewState();
}

class _ItemViewState extends State<ItemView> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(
          top: widget.index! == 0 ? UIUtils.dp(14) : UIUtils.dp(8),
          bottom: widget.index! == IQuiz.get().quizList.length - 1
              ? UIUtils.dp(14)
              : UIUtils.dp(8),
          left: UIUtils.dp(16),
          right: UIUtils.dp(16)),
      decoration: widget.mo!.playCount > 0 && widget.current != widget.index!
          ? Style.style_box_bg_r8
          : Style.style_box_line_r8,
      child: JkInkWell(
        child: Container(
          padding: EdgeInsets.only(
              top: UIUtils.dp(12),
              bottom: UIUtils.dp(16),
              left: UIUtils.dp(16),
              right: UIUtils.dp(16)),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  TitlePlay(widget.index!, widget.mo!),
                  Expanded(
                    child: TitleText(widget.index!, widget.mo!),
                  ),
                ],
              ),
              if (IQuiz.get().isShow)
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    getSub_pic(widget.mo!),
                    Container(
                      margin: EdgeInsets.only(top: UIUtils.dp(12)),
                      padding: EdgeInsets.only(
                          top: UIUtils.dp(12),
                          bottom: UIUtils.dp(12),
                          left: UIUtils.dp(12),
                          right: UIUtils.dp(12)),
                      decoration: Style.style_box_white_r4,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          if (widget.mo!.optA.isNotEmpty)
                            OptionView('A', widget.mo!.optA),
                          if (widget.mo!.optB.isNotEmpty)
                            Container(
                              margin: EdgeInsets.only(top: UIUtils.dp(8)),
                              child: OptionView('B', widget.mo!.optB),
                            ),
                          if (widget.mo!.sub_type != JkKey.JUDGE &&
                              widget.mo!.optC.isNotEmpty)
                            Container(
                              margin: EdgeInsets.only(top: UIUtils.dp(8)),
                              child: OptionView('C', widget.mo!.optC),
                            ),
                          if (widget.mo!.sub_type != JkKey.JUDGE &&
                              widget.mo!.optD.isNotEmpty)
                            Container(
                              margin: EdgeInsets.only(top: UIUtils.dp(8)),
                              child: OptionView('D', widget.mo!.optD),
                            ),
                        ],
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(top: UIUtils.dp(12)),
                      width: double.infinity,
                      child: Text(
                        '答案：${widget.mo!.answer.toUpperCase()}',
                        style: Option.style_text1_14_w500(),
                      ),
                    ),
                    SkillsText(widget.index!, widget.mo!),
                    Container(
                      margin: EdgeInsets.only(top: UIUtils.dp(4)),
                      child: DashLine(
                          size: Size(double.infinity, 1),
                          color: Style.dark,
                          dashLineSize: 2,
                          spacingSize: 1.5,
                          direction: DashLine.DIRECTION_LINEAR),
                    ),
                  ],
                ),
              Container(
                margin: EdgeInsets.only(top: UIUtils.dp(10)),
                // color: Color(0x88FF0000),
                child: Row(
                  children: <Widget>[
                    TimePlay(widget.index!, widget.mo!),
                    Text(
                      widget.mo!.playCount > 0
                          ? '次数:${widget.mo!.playCount}∣时间：${widget.mo!.playTime}'
                          : '未学',
                      style: widget.current == widget.index!
                          ? Option.style_blue_14_w500()
                          : Option.style_text3_14_w500(),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        onTap: () {
          if (widget.onPlaying != null) {
            widget.onPlaying!(widget.index!, widget.mo!);
          }
        },
      ),
    );
  }

  Widget TitlePlay(int index, QuizMo mo) {
    if (widget.current == index) {
      return IQuiz.get().isShow
          ? Container(
              margin:
                  EdgeInsets.only(top: UIUtils.dp(5), right: UIUtils.dp(12)),
              child: Image.asset(
                mo.isPlay
                    ? 'assets/exercise/ic_ques_listen_play.png'
                    : 'assets/exercise/ic_ques_listen_pause.png',
                width: UIUtils.dp(18),
                height: UIUtils.dp(18),
                fit: BoxFit.contain,
              ),
            )
          : Container();
    }
    return Container();
  }

  Widget TitleText(int index, QuizMo mo) {
    if (IQuiz.get().isShow) {
      return Text(
        index < 10
            ? '0${index + 1}∣${mo.sub_Titles}'
            : '${index + 1}∣${mo.sub_Titles}',
        style: widget.current == index
            ? Option.style_blue_h16_w500()
            : Option.style_text1_h16_w500(),
      );
    }
    return Text(
      index < 10
          ? '0${index + 1}∣${mo.sub_Titles}'
          : '${index + 1}∣${mo.sub_Titles}',
      style: widget.current == index
          ? Option.style_blue_h16_w500()
          : Option.style_text1_h16_w500(),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget TimePlay(int index, QuizMo mo) {
    if (widget.current == index) {
      return !IQuiz.get().isShow
          ? Container(
              margin: EdgeInsets.only(right: UIUtils.dp(12)),
              child: Image.asset(
                mo.isPlay
                    ? 'assets/exercise/ic_ques_listen_play.png'
                    : 'assets/exercise/ic_ques_listen_pause.png',
                width: UIUtils.dp(18),
                height: UIUtils.dp(18),
                fit: BoxFit.contain,
              ),
            )
          : Container();
    }
    return IQuiz.get().isShow ? Container() : Container(width: UIUtils.dp(30));
  }

  Widget OptionView(String option, String text) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          '${option}、',
          style: Option.style_text2_h14_w500(),
        ),
        Expanded(
          child: Text(
            text,
            style: Option.style_text2_h14_w500(),
          ),
        ),
      ],
    );
  }

  Widget getSub_pic(QuizMo mo) {
    String sub_pic = mo.sub_pic.trim();
    String dhlj = mo.dhlj.trim();
    if (sub_pic.isNotEmpty) {
      return FutureBuilder<bool?>(
        future: checkImage('assets/quiz/${sub_pic}'),
        builder: (context, AsyncSnapshot<bool?> res) {
          bool exists = res.data ?? false;
          // print('sub_pic = ${sub_pic}, exists = ${exists}');
          if (exists) {
            return Container(
              padding: EdgeInsets.only(
                  top: UIUtils.dp(12),
                  left: UIUtils.dp(16),
                  right: UIUtils.dp(16)),
              alignment: AlignmentDirectional.center,
              child: JkInkWell(
                child: Image.asset(
                  'assets/quiz/${sub_pic}',
                  width: UIUtils.dp(345),
                  height: UIUtils.dp(128),
                  fit: BoxFit.contain,
                ),
                onTap: () {
                  //点击看大图
                  List<String> photos = <String>[];
                  photos.add('assets/quiz/${sub_pic}');
                  DialogHelper.showBigPicture(
                      context, 'assets', photos, 0, false);
                },
              ),
            );
          } else {
            return Container(
              margin: EdgeInsets.only(
                  top: UIUtils.dp(12),
                  left: UIUtils.dp(16),
                  right: UIUtils.dp(16)),
              alignment: AlignmentDirectional.center,
              child: errorView(sub_pic, UIUtils.dp(345), UIUtils.dp(128), () {
                //点击看大图
                List<String> photos = <String>[];
                photos.add(ApiUrl.IMAGE_URL + sub_pic);
                DialogHelper.showBigPicture(
                    context, 'network', photos, 0, false);
              }),
            );
          }
        },
      );
    }
    if (dhlj.isNotEmpty && dhlj != '1') {
      if (dhlj.endsWith(".gif")) {
        return FutureBuilder<bool?>(
          future: checkImage('assets/quiz/${dhlj}'),
          builder: (context, AsyncSnapshot<bool?> res) {
            bool exists = res.data ?? false;
            // print('dhlj = ${dhlj}, exists = ${exists}');
            if (exists) {
              return Container(
                padding: EdgeInsets.only(
                    top: UIUtils.dp(12),
                    left: UIUtils.dp(16),
                    right: UIUtils.dp(16)),
                alignment: AlignmentDirectional.center,
                child: JkInkWell(
                  child: Image.asset(
                    'assets/quiz/${dhlj}',
                    width: UIUtils.dp(345),
                    height: UIUtils.dp(128),
                    fit: BoxFit.contain,
                  ),
                  onTap: () {
                    //点击看大图
                    List<String> photos = <String>[];
                    photos.add('assets/quiz/${dhlj}');
                    DialogHelper.showBigPicture(
                        context, 'assets', photos, 0, false);
                  },
                ),
              );
            } else {
              return Container(
                margin: EdgeInsets.only(
                    top: UIUtils.dp(12),
                    left: UIUtils.dp(16),
                    right: UIUtils.dp(16)),
                alignment: AlignmentDirectional.center,
                child: errorView(dhlj, UIUtils.dp(345), UIUtils.dp(128), () {
                  //点击看大图
                  List<String> photos = <String>[];
                  photos.add(ApiUrl.IMAGE_URL + dhlj);
                  DialogHelper.showBigPicture(
                      context, 'network', photos, 0, false);
                }),
              );
            }
          },
        );
      } else {
        return FutureBuilder<bool?>(
          future: checkImage('assets/quiz/${dhlj}.gif'),
          builder: (context, AsyncSnapshot<bool?> res) {
            bool exists = res.data ?? false;
            // print('dhlj = ${dhlj}, exists = ${exists}');
            if (exists) {
              return Container(
                padding: EdgeInsets.only(
                    top: UIUtils.dp(12),
                    left: UIUtils.dp(16),
                    right: UIUtils.dp(16)),
                alignment: AlignmentDirectional.center,
                child: JkInkWell(
                  child: Image.asset(
                    'assets/quiz/${dhlj}.gif',
                    width: UIUtils.dp(345),
                    height: UIUtils.dp(128),
                    fit: BoxFit.contain,
                  ),
                  onTap: () {
                    //点击看大图
                    List<String> photos = <String>[];
                    photos.add('assets/quiz/${dhlj}.gif');
                    DialogHelper.showBigPicture(
                        context, 'assets', photos, 0, false);
                  },
                ),
              );
            } else {
              return Container(
                margin: EdgeInsets.only(
                    top: UIUtils.dp(12),
                    left: UIUtils.dp(16),
                    right: UIUtils.dp(16)),
                alignment: AlignmentDirectional.center,
                child: errorView(
                    '${dhlj}.gif', UIUtils.dp(345), UIUtils.dp(128), () {
                  //点击看大图
                  List<String> photos = <String>[];
                  photos.add('${ApiUrl.IMAGE_URL + dhlj}.gif');
                  DialogHelper.showBigPicture(
                      context, 'network', photos, 0, false);
                }),
              );
            }
          },
        );
      }
    }
    return Container();
  }

  Future<bool> checkImage(String path) async {
    try {
      await rootBundle.load(path);
      return Future<bool>.value(true);
    } catch (e) {
      return Future<bool>.value(false);
    }
  }

  Widget errorView(
      String fileName, double? width, double? height, Function()? onTap) {
    return JkInkWell(
      child: CachedNetworkImage(
        fit: BoxFit.contain,
        width: width,
        height: height,
        imageUrl: ApiUrl.IMAGE_URL + fileName,
        errorWidget: (context, url, error) => Container(),
      ),
      onTap: () {
        if (onTap != null) {
          onTap();
        }
      },
    );
  }

  Widget SkillsText(int index, QuizMo mo) {
    if (mo.dtjq.isNotEmpty) {
      return Container(
        constraints: BoxConstraints(
          minHeight: IQuiz.get().isSkill(widget.index!) ? 0 : UIUtils.dp(52),
          maxHeight: IQuiz.get().isSkill(widget.index!)
              ? double.infinity
              : UIUtils.dp(52),
        ),
        alignment: AlignmentDirectional.centerStart,
        child: Stack(
          children: <Widget>[
            Container(
              width: double.infinity,
              constraints: BoxConstraints(
                minHeight:
                    IQuiz.get().isSkill(widget.index!) ? 0 : UIUtils.dp(52),
              ),
              margin:
                  EdgeInsets.only(top: UIUtils.dp(6), bottom: UIUtils.dp(6)),
              child: Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: '答题技巧：',
                      style: Option.style_text1_h14_w500(),
                    ),
                    TextSpan(
                      text: mo.dtjq,
                      style: Option.style_text3_h14_w500(),
                    ),
                  ],
                ),
              ),
            ),
            if (!IQuiz.get().isSkill(index))
              Positioned(
                left: 0,
                right: 0,
                top: 0,
                bottom: 0,
                child: Row(
                  children: <Widget>[
                    Expanded(
                      child: Container(
                        decoration: Style.style_box_white_trans,
                      ),
                    ),
                    Container(
                      height: double.infinity,
                      decoration: Style.style_box_white_trans1,
                      child: Container(
                        margin: EdgeInsets.only(right: UIUtils.dp(10)),
                        alignment: AlignmentDirectional.center,
                        child: JkInkWell(
                          child: Container(
                            padding: EdgeInsets.only(
                                top: UIUtils.dp(4),
                                bottom: UIUtils.dp(4),
                                left: UIUtils.dp(16),
                                right: UIUtils.dp(16)),
                            height: UIUtils.dp(28),
                            decoration: Style.style_box_brown_gradient_r50,
                            alignment: AlignmentDirectional.center,
                            child: Text(
                              '查看完整技巧',
                              style: Option.style_brown1_14_w500(),
                            ),
                          ),
                          onTap: () {
                            JumpUtils.get().jumpBuyVip();
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      );
    }
    return Container();
  }
}
