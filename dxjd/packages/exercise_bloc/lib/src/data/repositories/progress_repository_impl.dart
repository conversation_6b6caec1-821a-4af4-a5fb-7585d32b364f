import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import '../../domain/entities/exercise_progress.dart';
import '../../domain/repositories/progress_repository.dart';

/// Implementation of ProgressRepository using SQLite
class ProgressRepositoryImpl implements ProgressRepository {
  ProgressRepositoryImpl({
    required String databasePath,
  }) : _databasePath = databasePath;

  final String _databasePath;
  Database? _database;

  /// Get database instance
  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  /// Initialize database
  Future<Database> _initDatabase() async {
    return await openDatabase(
      _databasePath,
      version: 1,
      onCreate: _onCreate,
    );
  }

  /// Create database tables
  Future<void> _onCreate(Database db, int version) async {
    // Exercise progress table
    await db.execute('''
      CREATE TABLE exercise_progress (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        subject INTEGER NOT NULL,
        type TEXT NOT NULL,
        user_id TEXT,
        current_index INTEGER NOT NULL,
        total_questions INTEGER NOT NULL,
        correct_count INTEGER NOT NULL,
        wrong_count INTEGER NOT NULL,
        answered_questions TEXT NOT NULL,
        start_time TEXT,
        end_time TEXT,
        time_spent INTEGER NOT NULL,
        score INTEGER NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Listen progress table
    await db.execute('''
      CREATE TABLE listen_progress (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        subject INTEGER NOT NULL,
        user_id TEXT,
        current_index INTEGER NOT NULL,
        total_questions INTEGER NOT NULL,
        played_questions TEXT NOT NULL,
        is_playing INTEGER NOT NULL DEFAULT 0,
        current_position INTEGER NOT NULL DEFAULT 0,
        total_duration INTEGER NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Create indexes
    await db.execute('CREATE INDEX idx_exercise_progress_subject_type ON exercise_progress (subject, type)');
    await db.execute('CREATE INDEX idx_listen_progress_subject ON listen_progress (subject)');
  }

  @override
  Future<void> saveExerciseProgress(ExerciseProgress progress) async {
    final db = await database;
    
    final data = {
      'subject': progress.subject,
      'type': progress.type,
      'current_index': progress.currentIndex,
      'total_questions': progress.totalQuestions,
      'correct_count': progress.correctCount,
      'wrong_count': progress.wrongCount,
      'answered_questions': jsonEncode(progress.answeredQuestions),
      'start_time': progress.startTime?.toIso8601String(),
      'end_time': progress.endTime?.toIso8601String(),
      'time_spent': progress.timeSpent.inMilliseconds,
      'score': progress.score,
      'updated_at': DateTime.now().toIso8601String(),
    };

    // Check if progress already exists
    final existing = await db.query(
      'exercise_progress',
      where: 'subject = ? AND type = ?',
      whereArgs: [progress.subject, progress.type],
      limit: 1,
    );

    if (existing.isNotEmpty) {
      // Update existing progress
      await db.update(
        'exercise_progress',
        data,
        where: 'subject = ? AND type = ?',
        whereArgs: [progress.subject, progress.type],
      );
    } else {
      // Insert new progress
      data['created_at'] = DateTime.now().toIso8601String();
      await db.insert('exercise_progress', data);
    }
  }

  @override
  Future<ExerciseProgress?> getExerciseProgress({
    required int subject,
    required String type,
    String? userId,
  }) async {
    final db = await database;
    
    String whereClause = 'subject = ? AND type = ?';
    List<dynamic> whereArgs = [subject, type];
    
    if (userId != null) {
      whereClause += ' AND user_id = ?';
      whereArgs.add(userId);
    }

    final result = await db.query(
      'exercise_progress',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'updated_at DESC',
      limit: 1,
    );

    if (result.isNotEmpty) {
      return _mapToExerciseProgress(result.first);
    }

    return null;
  }

  @override
  Future<void> saveListenProgress(ListenProgress progress) async {
    final db = await database;
    
    final data = {
      'subject': progress.subject,
      'current_index': progress.currentIndex,
      'total_questions': progress.totalQuestions,
      'played_questions': jsonEncode(progress.playedQuestions),
      'is_playing': progress.isPlaying ? 1 : 0,
      'current_position': progress.currentPosition.inMilliseconds,
      'total_duration': progress.totalDuration.inMilliseconds,
      'updated_at': DateTime.now().toIso8601String(),
    };

    // Check if progress already exists
    final existing = await db.query(
      'listen_progress',
      where: 'subject = ?',
      whereArgs: [progress.subject],
      limit: 1,
    );

    if (existing.isNotEmpty) {
      // Update existing progress
      await db.update(
        'listen_progress',
        data,
        where: 'subject = ?',
        whereArgs: [progress.subject],
      );
    } else {
      // Insert new progress
      data['created_at'] = DateTime.now().toIso8601String();
      await db.insert('listen_progress', data);
    }
  }

  @override
  Future<ListenProgress?> getListenProgress({
    required int subject,
    String? userId,
  }) async {
    final db = await database;
    
    String whereClause = 'subject = ?';
    List<dynamic> whereArgs = [subject];
    
    if (userId != null) {
      whereClause += ' AND user_id = ?';
      whereArgs.add(userId);
    }

    final result = await db.query(
      'listen_progress',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'updated_at DESC',
      limit: 1,
    );

    if (result.isNotEmpty) {
      return _mapToListenProgress(result.first);
    }

    return null;
  }

  @override
  Future<void> deleteProgress({
    required int subject,
    required String type,
    String? userId,
  }) async {
    final db = await database;
    
    if (type == 'exercise') {
      String whereClause = 'subject = ?';
      List<dynamic> whereArgs = [subject];
      
      if (userId != null) {
        whereClause += ' AND user_id = ?';
        whereArgs.add(userId);
      }

      await db.delete(
        'exercise_progress',
        where: whereClause,
        whereArgs: whereArgs,
      );
    } else if (type == 'listen') {
      String whereClause = 'subject = ?';
      List<dynamic> whereArgs = [subject];
      
      if (userId != null) {
        whereClause += ' AND user_id = ?';
        whereArgs.add(userId);
      }

      await db.delete(
        'listen_progress',
        where: whereClause,
        whereArgs: whereArgs,
      );
    }
  }

  @override
  Future<List<ExerciseProgress>> getAllExerciseProgress({
    String? userId,
  }) async {
    final db = await database;
    
    String whereClause = '';
    List<dynamic> whereArgs = [];
    
    if (userId != null) {
      whereClause = 'user_id = ?';
      whereArgs.add(userId);
    }

    final result = await db.query(
      'exercise_progress',
      where: whereClause.isNotEmpty ? whereClause : null,
      whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
      orderBy: 'updated_at DESC',
    );

    return result.map((map) => _mapToExerciseProgress(map)).toList();
  }

  @override
  Future<Map<String, dynamic>> getProgressStats({
    required int subject,
    String? userId,
  }) async {
    final db = await database;
    
    // Exercise stats
    String exerciseWhereClause = 'subject = ?';
    List<dynamic> exerciseWhereArgs = [subject];
    
    if (userId != null) {
      exerciseWhereClause += ' AND user_id = ?';
      exerciseWhereArgs.add(userId);
    }

    final exerciseResult = await db.query(
      'exercise_progress',
      where: exerciseWhereClause,
      whereArgs: exerciseWhereArgs,
      orderBy: 'updated_at DESC',
      limit: 1,
    );

    // Listen stats
    String listenWhereClause = 'subject = ?';
    List<dynamic> listenWhereArgs = [subject];
    
    if (userId != null) {
      listenWhereClause += ' AND user_id = ?';
      listenWhereArgs.add(userId);
    }

    final listenResult = await db.query(
      'listen_progress',
      where: listenWhereClause,
      whereArgs: listenWhereArgs,
      orderBy: 'updated_at DESC',
      limit: 1,
    );

    final stats = <String, dynamic>{
      'subject': subject,
      'has_exercise_progress': exerciseResult.isNotEmpty,
      'has_listen_progress': listenResult.isNotEmpty,
    };

    if (exerciseResult.isNotEmpty) {
      final exerciseProgress = _mapToExerciseProgress(exerciseResult.first);
      stats.addAll({
        'exercise_completion': exerciseProgress.completionPercentage,
        'exercise_accuracy': exerciseProgress.accuracyPercentage,
        'exercise_correct_count': exerciseProgress.correctCount,
        'exercise_wrong_count': exerciseProgress.wrongCount,
        'exercise_total_questions': exerciseProgress.totalQuestions,
      });
    }

    if (listenResult.isNotEmpty) {
      final listenProgress = _mapToListenProgress(listenResult.first);
      stats.addAll({
        'listen_progress': listenProgress.progressPercentage,
        'listen_played_count': listenProgress.playedQuestions.length,
        'listen_total_questions': listenProgress.totalQuestions,
      });
    }

    return stats;
  }

  @override
  Future<void> syncProgress({
    String? userId,
  }) async {
    // This would sync progress with a remote server
    // For now, we'll just log the sync operation
    print('Syncing progress for user: $userId');
  }

  @override
  Future<void> clearAllProgress() async {
    final db = await database;
    
    await db.delete('exercise_progress');
    await db.delete('listen_progress');
  }

  /// Helper methods

  /// Convert database map to ExerciseProgress
  ExerciseProgress _mapToExerciseProgress(Map<String, dynamic> map) {
    final answeredQuestionsJson = map['answered_questions'] as String;
    final answeredQuestions = Map<int, String>.from(
      jsonDecode(answeredQuestionsJson) as Map<String, dynamic>,
    );

    return ExerciseProgress(
      subject: map['subject'] as int,
      type: map['type'] as String,
      currentIndex: map['current_index'] as int,
      totalQuestions: map['total_questions'] as int,
      correctCount: map['correct_count'] as int,
      wrongCount: map['wrong_count'] as int,
      answeredQuestions: answeredQuestions,
      startTime: map['start_time'] != null 
          ? DateTime.parse(map['start_time'] as String)
          : null,
      endTime: map['end_time'] != null 
          ? DateTime.parse(map['end_time'] as String)
          : null,
      timeSpent: Duration(milliseconds: map['time_spent'] as int),
      score: map['score'] as int,
    );
  }

  /// Convert database map to ListenProgress
  ListenProgress _mapToListenProgress(Map<String, dynamic> map) {
    final playedQuestionsJson = map['played_questions'] as String;
    final playedQuestions = Map<int, int>.from(
      jsonDecode(playedQuestionsJson) as Map<String, dynamic>,
    );

    return ListenProgress(
      subject: map['subject'] as int,
      currentIndex: map['current_index'] as int,
      totalQuestions: map['total_questions'] as int,
      playedQuestions: playedQuestions,
      isPlaying: (map['is_playing'] as int) == 1,
      currentPosition: Duration(milliseconds: map['current_position'] as int),
      totalDuration: Duration(milliseconds: map['total_duration'] as int),
    );
  }
}
