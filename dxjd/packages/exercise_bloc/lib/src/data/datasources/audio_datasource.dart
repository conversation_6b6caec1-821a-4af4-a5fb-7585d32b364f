import 'dart:async';
import 'package:audioplayers/audioplayers.dart';
import '../../domain/repositories/audio_repository.dart';

/// Implementation of audio data source using audioplayers package
class AudioDataSourceImpl {
  AudioDataSourceImpl() {
    _initializePlayer();
  }

  final AudioPlayer _audioPlayer = AudioPlayer();
  
  // Stream controllers for audio events
  final StreamController<Duration> _positionController = StreamController<Duration>.broadcast();
  final StreamController<Duration> _durationController = StreamController<Duration>.broadcast();
  final StreamController<AudioPlayerState> _playerStateController = StreamController<AudioPlayerState>.broadcast();
  final StreamController<void> _completionController = StreamController<void>.broadcast();

  // Current state
  String? _currentUrl;
  AudioPlayerState _currentState = AudioPlayerState.stopped;
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;

  /// Initialize audio player with event listeners
  void _initializePlayer() {
    // Listen to position changes
    _audioPlayer.onPositionChanged.listen((position) {
      _currentPosition = position;
      _positionController.add(position);
    });

    // Listen to duration changes
    _audioPlayer.onDurationChanged.listen((duration) {
      _totalDuration = duration;
      _durationController.add(duration);
    });

    // Listen to player state changes
    _audioPlayer.onPlayerStateChanged.listen((state) {
      final audioState = _mapPlayerState(state);
      _currentState = audioState;
      _playerStateController.add(audioState);
    });

    // Listen to completion events
    _audioPlayer.onPlayerComplete.listen((_) {
      _currentState = AudioPlayerState.completed;
      _playerStateController.add(AudioPlayerState.completed);
      _completionController.add(null);
    });
  }

  /// Play audio from URL
  Future<void> play(String url) async {
    try {
      if (_currentUrl != url) {
        await _audioPlayer.stop();
        await _audioPlayer.setSource(UrlSource(url));
        _currentUrl = url;
      }
      
      await _audioPlayer.resume();
    } catch (e) {
      _currentState = AudioPlayerState.error;
      _playerStateController.add(AudioPlayerState.error);
      throw Exception('Failed to play audio: $e');
    }
  }

  /// Pause current audio
  Future<void> pause() async {
    try {
      await _audioPlayer.pause();
    } catch (e) {
      throw Exception('Failed to pause audio: $e');
    }
  }

  /// Resume paused audio
  Future<void> resume() async {
    try {
      await _audioPlayer.resume();
    } catch (e) {
      throw Exception('Failed to resume audio: $e');
    }
  }

  /// Stop current audio
  Future<void> stop() async {
    try {
      await _audioPlayer.stop();
      _currentUrl = null;
      _currentPosition = Duration.zero;
      _positionController.add(Duration.zero);
    } catch (e) {
      throw Exception('Failed to stop audio: $e');
    }
  }

  /// Seek to specific position
  Future<void> seekTo(Duration position) async {
    try {
      await _audioPlayer.seek(position);
    } catch (e) {
      throw Exception('Failed to seek audio: $e');
    }
  }

  /// Set playback speed
  Future<void> setPlaybackRate(double rate) async {
    try {
      await _audioPlayer.setPlaybackRate(rate);
    } catch (e) {
      throw Exception('Failed to set playback rate: $e');
    }
  }

  /// Set volume (0.0 to 1.0)
  Future<void> setVolume(double volume) async {
    try {
      await _audioPlayer.setVolume(volume);
    } catch (e) {
      throw Exception('Failed to set volume: $e');
    }
  }

  /// Get current playback position
  Future<Duration> getCurrentPosition() async {
    return _currentPosition;
  }

  /// Get total duration of current audio
  Future<Duration> getDuration() async {
    return _totalDuration;
  }

  /// Stream of playback position updates
  Stream<Duration> get positionStream => _positionController.stream;

  /// Stream of duration updates
  Stream<Duration> get durationStream => _durationController.stream;

  /// Stream of player state changes
  Stream<AudioPlayerState> get playerStateStream => _playerStateController.stream;

  /// Stream of playback completion events
  Stream<void> get onPlayerComplete => _completionController.stream;

  /// Check if audio is currently playing
  bool get isPlaying => _currentState == AudioPlayerState.playing;

  /// Check if audio is paused
  bool get isPaused => _currentState == AudioPlayerState.paused;

  /// Check if audio is stopped
  bool get isStopped => _currentState == AudioPlayerState.stopped;

  /// Configure audio session for background playback
  Future<void> configureAudioSession({
    bool enableBackgroundPlayback = true,
    bool mixWithOthers = false,
  }) async {
    try {
      // Configure audio context for different platforms
      await _audioPlayer.setAudioContext(
        AudioContext(
          iOS: AudioContextIOS(
            defaultToSpeaker: true,
            category: enableBackgroundPlayback 
                ? AVAudioSessionCategory.playback 
                : AVAudioSessionCategory.ambient,
            options: mixWithOthers 
                ? [AVAudioSessionOptions.mixWithOthers]
                : [],
          ),
          android: AudioContextAndroid(
            isSpeakerphoneOn: true,
            stayAwake: enableBackgroundPlayback,
            contentType: AndroidContentType.music,
            usageType: AndroidUsageType.media,
            audioFocus: mixWithOthers 
                ? AndroidAudioFocus.gainTransientMayDuck
                : AndroidAudioFocus.gain,
          ),
        ),
      );
    } catch (e) {
      throw Exception('Failed to configure audio session: $e');
    }
  }

  /// Handle audio interruptions (phone calls, etc.)
  Stream<AudioInterruption> get onAudioInterruption {
    // This would need platform-specific implementation
    // For now, return an empty stream
    return const Stream.empty();
  }

  /// Release audio player resources
  Future<void> dispose() async {
    await _audioPlayer.dispose();
    await _positionController.close();
    await _durationController.close();
    await _playerStateController.close();
    await _completionController.close();
  }

  /// Map audioplayers PlayerState to our AudioPlayerState
  AudioPlayerState _mapPlayerState(PlayerState state) {
    switch (state) {
      case PlayerState.stopped:
        return AudioPlayerState.stopped;
      case PlayerState.playing:
        return AudioPlayerState.playing;
      case PlayerState.paused:
        return AudioPlayerState.paused;
      case PlayerState.completed:
        return AudioPlayerState.completed;
      case PlayerState.disposed:
        return AudioPlayerState.stopped;
    }
  }
}

/// Audio repository implementation using AudioDataSourceImpl
class AudioRepositoryImpl implements AudioRepository {
  AudioRepositoryImpl() : _dataSource = AudioDataSourceImpl();

  final AudioDataSourceImpl _dataSource;

  @override
  Future<void> play(String url) => _dataSource.play(url);

  @override
  Future<void> pause() => _dataSource.pause();

  @override
  Future<void> resume() => _dataSource.resume();

  @override
  Future<void> stop() => _dataSource.stop();

  @override
  Future<void> seekTo(Duration position) => _dataSource.seekTo(position);

  @override
  Future<void> setPlaybackRate(double rate) => _dataSource.setPlaybackRate(rate);

  @override
  Future<void> setVolume(double volume) => _dataSource.setVolume(volume);

  @override
  Future<Duration> getCurrentPosition() => _dataSource.getCurrentPosition();

  @override
  Future<Duration> getDuration() => _dataSource.getDuration();

  @override
  Stream<Duration> get positionStream => _dataSource.positionStream;

  @override
  Stream<Duration> get durationStream => _dataSource.durationStream;

  @override
  Stream<AudioPlayerState> get playerStateStream => _dataSource.playerStateStream;

  @override
  Stream<void> get onPlayerComplete => _dataSource.onPlayerComplete;

  @override
  bool get isPlaying => _dataSource.isPlaying;

  @override
  bool get isPaused => _dataSource.isPaused;

  @override
  bool get isStopped => _dataSource.isStopped;

  @override
  Future<void> dispose() => _dataSource.dispose();

  @override
  Future<void> configureAudioSession({
    bool enableBackgroundPlayback = true,
    bool mixWithOthers = false,
  }) => _dataSource.configureAudioSession(
        enableBackgroundPlayback: enableBackgroundPlayback,
        mixWithOthers: mixWithOthers,
      );

  @override
  Stream<AudioInterruption> get onAudioInterruption => _dataSource.onAudioInterruption;
}
