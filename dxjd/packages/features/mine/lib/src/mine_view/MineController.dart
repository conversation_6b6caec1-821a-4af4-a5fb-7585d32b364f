import 'dart:async';

import 'package:component_library/component_library.dart';
import 'package:dxjd/bottom_tabbar_controller.dart';
import 'package:dxjd/mainController.dart';
import 'package:dxjd/tab_container_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:home/home.dart';
import 'package:home_repository/home_repository.dart';
import 'package:key_value_storage/key_value_storage.dart';
import 'package:quiz/quiz.dart';
import 'package:timing/timing.dart';
import 'package:timing_repository/timing_repository.dart';
import 'package:tools/tools.dart';
import 'package:user_repository/user_repository.dart';

StreamSubscription? _ibus;

class MineController extends GetxController {
  bool isTiming = false;
  bool isSubOne = true;
  List iconList = [
    'nav_icon_xiaoxi',
    'nav_icon_miji',
    'nav_icon_dyguanzhu',
    'nav_icon_fenxiang',
    // 'nav_icon_xuechegushi',
  ];
  List vip_sub_List = [
    '科目一',
    '科目二',
    '科目三',
    '科目四',
  ];
  late List<String> extended_vip_sub_List;
  late PageController pageController ;
  int currentPage = 0;
  List vip_sub_List_tips = [
    '3天轻松过科一',
    '3步快速学科二',
    '3步快速学科三',
    '3天轻松过科四',
  ];
  List titleList = [
    '消息通知',
    '全科目秘籍资料',
    '抖音学习：关注大象驾到抖音',
    '分享',
    // '学车故事',
  ];
  List<bool> isVipSubList = [];
  List<int?> vipTimeList = [];
  final ScrollController scrollController = ScrollController();
  Color appBarColor = Colors.transparent;
  late UserRepository userRepository;
  late TimingRepository timingRepository;
  late HomeRepository homeRepository;
  late StreamSubscription<UserAccount?> _userSubject;
  UserAccount? userAccount;
  MethodChannel channel = MethodChannel('xw.dxjk.share');
  bool isShowMijiRp = true;
  bool isShowDyguanzhuRp = true;
  bool isShowRedPointInCoupon = false;
  int realIndex = 1000; // 初始索引，用于无限循环


  //获取已结业用户是否切换车型题库
  String? isChangeCarType;

  @override
  void onInit() async {
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   // isLogin.call();
    // });
    userRepository = Get.find<MainController>().userRepository;
    timingRepository = Get.find<MainController>().timingRepository;
    homeRepository = Get.find<MainController>().homeRepository;
    var currentIndex = Get.find<ExaminationController>(tag: ExaminationPageState.key.toString()).currentIndex;
    pageController = PageController(
      initialPage: currentIndex+1,
      viewportFraction: 0.96, // 控制卡片可见区域（留出两侧部分空间）
    );
    extended_vip_sub_List = [
      vip_sub_List.last,
      ...vip_sub_List,
      vip_sub_List.first
    ];
    isVipSubList = [
      userRepository.vipProductMap.vip1,
      userRepository.vipProductMap.vip2,
      userRepository.vipProductMap.vip3,
      userRepository.vipProductMap.vip4
    ];
    // 订阅事件
    _ibus = IEventBus.get().register<IEvent>((event) async {
      if (JkKey.EVENT_REFRESH_MY_RECORD == event.type) {
        await IHome.get().updatePassRate(isSubOne ? 1 : 4);
        refreshUI();
      }
    });
    isTiming = !ITiming.get().closeTiming;

    scrollController.addListener(() {
      if (scrollController.position.pixels >= 100.h) {
        double offset = (scrollController.position.pixels - 100.h) / 100;
        if (offset <= 1) {
          appBarColor = AppColors.white.withOpacity(offset);
          update();
        }
      } else {
        appBarColor = Colors.transparent;
        update();
      }
    });
    _getAccountInfo();
    _userSubject = userRepository.getUserAccount().listen((userAccountNew) {
      if (userAccountNew != null) {
        userAccount = userAccountNew;
        vipTimeList = [
          userAccount?.vip1ExpireValue,
          userAccount?.vip2ExpireValue,
          userAccount?.vip3ExpireValue,
          userAccount?.vip4ExpireValue
        ];
        // debugPrint('userAccountNew:${userAccountNew.name}');
        update();
      }
    });
    vipTimeList = [
      userAccount?.vip1ExpireValue,
      userAccount?.vip2ExpireValue,
      userAccount?.vip3ExpireValue,
      userAccount?.vip4ExpireValue
    ];
    isShowRedPointInCoupon =
        await PreferencesService().getBool('red_point_in_mine') ?? false;
    PreferencesService().getBool('isShowMijiRp').then((value) {
      isShowMijiRp = value ?? true;
      update();
    });
    PreferencesService().getBool('isShowDyguanzhuRp').then((value) {
      isShowDyguanzhuRp = value ?? true;
      update();
    });

    PreferencesService().getString('useTopicType').then((value) {
      isChangeCarType = value;
      update();
    });
    super.onInit();
  }

  ///改变车型题库类型
  changeTopicType() async {
    isChangeCarType = await PreferencesService().getString('useTopicType');
    update();
  }

  void refreshVipStatus() {
    isVipSubList = [
      userRepository.vipProductMap.vip1,
      userRepository.vipProductMap.vip2,
      userRepository.vipProductMap.vip3,
      userRepository.vipProductMap.vip4
    ];
    update();
  }

  void changeShowRedPointInCoupon() {
    if(!isShowRedPointInCoupon){
      return ;
    }
    isShowRedPointInCoupon = false;
    update();
    if (Get.isRegistered<BottomTabBarController>(
      tag: TabContainerScreenState.key.toString(),
    )) {
      Get.find<BottomTabBarController>(
              tag: TabContainerScreenState.key.toString())
          .changeMineRedPoint();
    }
  }

  void onClickMijiRp() {
    isShowMijiRp = false;
    update();
    PreferencesService().setBool('isShowMijiRp', isShowMijiRp);
  }

  void onClickDyguanzhuRp() {
    isShowDyguanzhuRp = false;
    update();
    PreferencesService().setBool('isShowDyguanzhuRp', isShowDyguanzhuRp);
  }

  @override
  void onReady() async {
    // TODO: implement onReady
    super.onReady();
  }

  void refreshUI() async {
    try {
      update();
    } catch (e) {}
  }

  _getAccountInfo() async {
    userAccount = await userRepository.userAccountDM;
    update();
  }

  @override
  void onClose() {
    IEventBus.get().unregister(_ibus);
    scrollController.dispose();
    pageController.dispose();
    _userSubject.cancel();
    super.onClose();
  }
}
