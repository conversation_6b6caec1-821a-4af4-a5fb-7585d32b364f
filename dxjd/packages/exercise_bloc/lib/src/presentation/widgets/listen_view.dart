import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/listen/listen_bloc.dart';
import '../bloc/listen/listen_event.dart';
import '../bloc/listen/listen_state.dart';

/// Listen view for audio-based learning
class ListenView extends StatefulWidget {
  const ListenView({
    super.key,
    required this.subject,
    required this.type,
    this.sortId,
    this.mainId,
    this.childId,
    this.luid,
    this.ids,
  });

  final int subject;
  final String type;
  final int? sortId;
  final int? mainId;
  final int? childId;
  final int? luid;
  final List<int>? ids;

  @override
  State<ListenView> createState() => _ListenViewState();
}

class _ListenViewState extends State<ListenView> {
  @override
  void initState() {
    super.initState();
    // Load questions when view is initialized
    context.read<ListenBloc>().add(LoadListenQuestions(
      subject: widget.subject,
      type: widget.type,
      sortId: widget.sortId,
      mainId: widget.mainId,
      childId: widget.childId,
      luid: widget.luid,
      ids: widget.ids,
    ));
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ListenBloc, ListenState>(
      builder: (context, state) {
        if (state is ListenInitial) {
          return const Center(
            child: Text('准备加载听题...'),
          );
        }

        if (state is ListenLoading) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('正在加载听题...'),
              ],
            ),
          );
        }

        if (state is ListenError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 16),
                Text(
                  '加载失败',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  state.message,
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    context.read<ListenBloc>().add(LoadListenQuestions(
                      subject: widget.subject,
                      type: widget.type,
                      sortId: widget.sortId,
                      mainId: widget.mainId,
                      childId: widget.childId,
                      luid: widget.luid,
                      ids: widget.ids,
                    ));
                  },
                  child: const Text('重试'),
                ),
              ],
            ),
          );
        }

        if (state is ListenLoaded) {
          return Column(
            children: [
              // Progress info
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '第 ${state.currentIndex + 1} 题 / 共 ${state.questions.length} 题',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    Text(
                      '播放速度: ${state.playbackSpeed}x',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
              
              // Audio player
              Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    // Current question info
                    if (state.currentQuestion != null)
                      Text(
                        state.currentQuestion!.title,
                        style: Theme.of(context).textTheme.titleMedium,
                        textAlign: TextAlign.center,
                      ),
                    
                    const SizedBox(height: 16),
                    
                    // Audio progress
                    Row(
                      children: [
                        Text(_formatDuration(state.currentPosition)),
                        Expanded(
                          child: Slider(
                            value: state.progressPercentage,
                            onChanged: (value) {
                              final position = Duration(
                                milliseconds: (value * state.totalDuration.inMilliseconds).round(),
                              );
                              context.read<ListenBloc>().add(SeekAudio(position));
                            },
                          ),
                        ),
                        Text(_formatDuration(state.totalDuration)),
                      ],
                    ),
                    
                    // Audio controls
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        IconButton(
                          onPressed: state.currentIndex > 0
                              ? () => context.read<ListenBloc>().add(
                                    JumpToListenQuestion(state.currentIndex - 1),
                                  )
                              : null,
                          icon: const Icon(Icons.skip_previous),
                        ),
                        IconButton(
                          onPressed: () {
                            if (state.isPlaying) {
                              context.read<ListenBloc>().add(const PauseAudio());
                            } else if (state.isPaused) {
                              context.read<ListenBloc>().add(const ResumeAudio());
                            } else {
                              context.read<ListenBloc>().add(
                                PlayQuestionAudio(questionIndex: state.currentIndex),
                              );
                            }
                          },
                          icon: Icon(
                            state.isPlaying 
                                ? Icons.pause_circle_filled
                                : Icons.play_circle_filled,
                            size: 48,
                          ),
                        ),
                        IconButton(
                          onPressed: () => context.read<ListenBloc>().add(const StopAudio()),
                          icon: const Icon(Icons.stop_circle),
                        ),
                        IconButton(
                          onPressed: state.currentIndex < state.questions.length - 1
                              ? () => context.read<ListenBloc>().add(
                                    JumpToListenQuestion(state.currentIndex + 1),
                                  )
                              : null,
                          icon: const Icon(Icons.skip_next),
                        ),
                      ],
                    ),
                    
                    // Playback speed control
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Text('播放速度: '),
                        DropdownButton<double>(
                          value: state.playbackSpeed,
                          items: const [
                            DropdownMenuItem(value: 0.5, child: Text('0.5x')),
                            DropdownMenuItem(value: 0.75, child: Text('0.75x')),
                            DropdownMenuItem(value: 1.0, child: Text('1.0x')),
                            DropdownMenuItem(value: 1.25, child: Text('1.25x')),
                            DropdownMenuItem(value: 1.5, child: Text('1.5x')),
                            DropdownMenuItem(value: 2.0, child: Text('2.0x')),
                          ],
                          onChanged: (speed) {
                            if (speed != null) {
                              context.read<ListenBloc>().add(SetPlaybackSpeed(speed));
                            }
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // Question list
              Expanded(
                child: ListView.builder(
                  itemCount: state.questions.length,
                  itemBuilder: (context, index) {
                    final question = state.questions[index];
                    final isCurrentQuestion = index == state.currentIndex;
                    final isPlaying = index == state.currentPlayingIndex && state.isPlaying;
                    
                    return Card(
                      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                      color: isCurrentQuestion ? Colors.blue[50] : null,
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: isPlaying ? Colors.green : Colors.grey,
                          child: isPlaying 
                              ? const Icon(Icons.volume_up, color: Colors.white)
                              : Text('${index + 1}'),
                        ),
                        title: Text(
                          question.title,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        subtitle: Text('播放次数: ${question.playCount}'),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              onPressed: () => context.read<ListenBloc>().add(
                                PlayQuestionAudio(questionIndex: index),
                              ),
                              icon: Icon(
                                isPlaying ? Icons.pause : Icons.play_arrow,
                              ),
                            ),
                            if (state.showQuestionDetails)
                              IconButton(
                                onPressed: () => context.read<ListenBloc>().add(
                                  RemoveListenQuestion(index),
                                ),
                                icon: const Icon(Icons.delete),
                              ),
                          ],
                        ),
                        onTap: () => context.read<ListenBloc>().add(
                          JumpToListenQuestion(index),
                        ),
                      ),
                    );
                  },
                ),
              ),
              
              // Toggle details button
              Container(
                padding: const EdgeInsets.all(16),
                child: ElevatedButton(
                  onPressed: () => context.read<ListenBloc>().add(
                    const ToggleQuestionDetails(),
                  ),
                  child: Text(
                    state.showQuestionDetails ? '隐藏详情' : '显示详情',
                  ),
                ),
              ),
            ],
          );
        }

        return const Center(
          child: Text('未知状态'),
        );
      },
    );
  }

  /// Format duration for display
  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}
