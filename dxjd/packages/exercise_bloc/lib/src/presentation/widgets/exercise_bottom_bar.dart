import 'package:flutter/material.dart';
import '../bloc/mode_switch/mode_switch_event.dart';

/// Bottom navigation bar for exercise modes
class ExerciseBottomBar extends StatelessWidget {
  const ExerciseBottomBar({
    super.key,
    required this.currentMode,
    required this.canSwitchModes,
    required this.onModeChanged,
  });

  final ExerciseMode currentMode;
  final bool canSwitchModes;
  final ValueChanged<ExerciseMode> onModeChanged;

  @override
  Widget build(BuildContext context) {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: currentMode.index,
      onTap: canSwitchModes 
          ? (index) => onModeChanged(ExerciseModeExtension.fromIndex(index))
          : null,
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.quiz),
          label: '做题',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.headphones),
          label: '听题',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.book),
          label: '背题',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.video_library),
          label: '视频',
        ),
      ],
      selectedItemColor: _getModeColor(currentMode),
      unselectedItemColor: canSwitchModes ? Colors.grey : Colors.grey[400],
    );
  }

  /// Get color for current mode
  Color _getModeColor(ExerciseMode mode) {
    switch (mode) {
      case ExerciseMode.exercise:
        return Colors.blue;
      case ExerciseMode.listen:
        return Colors.green;
      case ExerciseMode.browse:
        return Colors.orange;
      case ExerciseMode.video:
        return Colors.purple;
    }
  }
}
