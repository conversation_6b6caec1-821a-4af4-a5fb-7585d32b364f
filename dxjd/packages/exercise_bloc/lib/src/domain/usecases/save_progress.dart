import '../entities/exercise_progress.dart';
import '../repositories/progress_repository.dart';

/// Use case for saving exercise progress
class SaveProgress {
  const SaveProgress(this._repository);

  final ProgressRepository _repository;

  /// Execute the use case
  Future<void> call(SaveProgressParams params) async {
    switch (params.type) {
      case ProgressType.exercise:
        if (params.exerciseProgress != null) {
          await _repository.saveExerciseProgress(params.exerciseProgress!);
        }
        break;
      case ProgressType.listen:
        if (params.listenProgress != null) {
          await _repository.saveListenProgress(params.listenProgress!);
        }
        break;
    }
  }
}

/// Parameters for saving progress
class SaveProgressParams {
  const SaveProgressParams({
    required this.type,
    this.exerciseProgress,
    this.listenProgress,
  });

  /// Type of progress to save
  final ProgressType type;
  
  /// Exercise progress data
  final ExerciseProgress? exerciseProgress;
  
  /// Listen progress data
  final ListenProgress? listenProgress;
}

/// Types of progress
enum ProgressType {
  exercise,
  listen,
}
