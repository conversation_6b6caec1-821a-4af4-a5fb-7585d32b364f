import 'package:flutter/material.dart';
import '../bloc/mode_switch/mode_switch_event.dart';

/// Top tab bar for exercise modes (following original design)
class ExerciseTopBar extends StatelessWidget {
  const ExerciseTopBar({
    super.key,
    required this.currentMode,
    required this.canSwitchModes,
    required this.onModeChanged,
    this.width = 204,
    this.height = 32,
  });

  final ExerciseMode currentMode;
  final bool canSwitchModes;
  final ValueChanged<ExerciseMode> onModeChanged;
  final double width;
  final double height;

  static const List<String> _tabs = ['做题', '听题', '背题', '视频'];

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: AlignmentDirectional.center,
      child: Container(
        width: width,
        alignment: AlignmentDirectional.center,
        child: _buildTabBar(),
      ),
    );
  }

  /// Build custom tab bar following original TTabBar design
  Widget _buildTabBar() {
    return Stack(
      children: [
        // Background container
        Container(
          height: height,
          decoration: _getBackgroundDecoration(),
        ),
        // Tab items
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: _buildTabItems(),
        ),
      ],
    );
  }

  /// Build tab items
  List<Widget> _buildTabItems() {
    return _tabs.asMap().entries.map((entry) {
      final index = entry.key;
      final title = entry.value;
      final isSelected = index == currentMode.index;

      return Expanded(
        child: GestureDetector(
          onTap: canSwitchModes
              ? () => onModeChanged(ExerciseModeExtension.fromIndex(index))
              : null,
          child: Container(
            height: height,
            decoration: isSelected ? _getSelectedDecoration() : null,
            alignment: Alignment.center,
            child: Text(
              title,
              style: isSelected
                  ? _getSelectedTextStyle()
                  : _getUnselectedTextStyle(),
            ),
          ),
        ),
      );
    }).toList();
  }

  /// Get background decoration (following Style.style_teb_line)
  BoxDecoration _getBackgroundDecoration() {
    return const BoxDecoration(
      color: Color(0x991992EF), // Style.blue3_60 equivalent
      borderRadius: BorderRadius.all(Radius.circular(8)),
    );
  }

  /// Get selected tab decoration (following Style.style_teb_indicator_line)
  BoxDecoration _getSelectedDecoration() {
    return const BoxDecoration(
      color: Color(0xFF1992EF), // Style.blue1 equivalent
      borderRadius: BorderRadius.all(Radius.circular(8)),
    );
  }

  /// Get selected text style (following Style.style_white_16_w500)
  TextStyle _getSelectedTextStyle() {
    return const TextStyle(
      color: Colors.white,
      fontSize: 16,
      fontWeight: FontWeight.w500,
    );
  }

  /// Get unselected text style (following Style.style_text2_14_w500)
  TextStyle _getUnselectedTextStyle() {
    return TextStyle(
      color: canSwitchModes ? const Color(0xFF666666) : const Color(0xFFCCCCCC),
      fontSize: 14,
      fontWeight: FontWeight.w500,
    );
  }
}
