import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../injection/injection.dart';
import '../bloc/exercise/exercise_bloc.dart';
import '../bloc/listen/listen_bloc.dart';
import '../bloc/browse/browse_bloc.dart';
import '../bloc/mode_switch/mode_switch_bloc.dart';
import '../bloc/mode_switch/mode_switch_event.dart';
import '../bloc/mode_switch/mode_switch_state.dart';
import '../widgets/exercise_view.dart';
import '../widgets/listen_view.dart';
import '../widgets/browse_view.dart';
import '../widgets/video_view.dart';
import '../widgets/exercise_top_bar.dart';

/// Main exercise page with mode switching
class ExercisePage extends StatelessWidget {
  const ExercisePage({
    super.key,
    required this.subject,
    required this.type,
    this.sortId,
    this.mainId,
    this.childId,
    this.luid,
    this.ids,
    this.initialMode = ExerciseMode.exercise,
  });

  final int subject;
  final String type;
  final int? sortId;
  final int? mainId;
  final int? childId;
  final int? luid;
  final List<int>? ids;
  final ExerciseMode initialMode;

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<ExerciseBloc>(
          create: (context) => getIt<ExerciseBloc>(),
        ),
        BlocProvider<ListenBloc>(
          create: (context) => getIt<ListenBloc>(),
        ),
        BlocProvider<BrowseBloc>(
          create: (context) => getIt<BrowseBloc>(),
        ),
        BlocProvider<ModeSwitchBloc>(
          create: (context) => getIt<ModeSwitchBloc>()
            ..add(InitializeMode(
              initialMode: initialMode,
              exerciseType: type,
            )),
        ),
      ],
      child: ExercisePageView(
        subject: subject,
        type: type,
        sortId: sortId,
        mainId: mainId,
        childId: childId,
        luid: luid,
        ids: ids,
      ),
    );
  }
}

/// Exercise page view with mode switching logic
class ExercisePageView extends StatefulWidget {
  const ExercisePageView({
    super.key,
    required this.subject,
    required this.type,
    this.sortId,
    this.mainId,
    this.childId,
    this.luid,
    this.ids,
  });

  final int subject;
  final String type;
  final int? sortId;
  final int? mainId;
  final int? childId;
  final int? luid;
  final List<int>? ids;

  @override
  State<ExercisePageView> createState() => _ExercisePageViewState();
}

class _ExercisePageViewState extends State<ExercisePageView> {
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<ModeSwitchBloc, ModeSwitchState>(
        listener: (context, state) {
          // Handle mode switch animations
          if (state is ModeSwitching) {
            _animateToPage(state.toMode.index);
          } else if (state.isActive) {
            _animateToPage(state.currentMode.index);
          }
        },
        builder: (context, state) {
          return Column(
            children: [
              // App bar
              _buildAppBar(context, state),

              // Top tab bar (following original design)
              Container(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: ExerciseTopBar(
                  currentMode: state.currentMode,
                  canSwitchModes: state.canSwitchModes,
                  onModeChanged: (ExerciseMode mode) =>
                      _switchMode(context, mode),
                ),
              ),

              // Main content with IndexedStack for better performance
              Expanded(
                child: IndexedStack(
                  index: state.currentMode.index,
                  children: [
                    ExerciseView(
                      subject: widget.subject,
                      type: widget.type,
                      sortId: widget.sortId,
                      mainId: widget.mainId,
                      childId: widget.childId,
                      luid: widget.luid,
                      ids: widget.ids,
                    ),
                    ListenView(
                      subject: widget.subject,
                      type: widget.type,
                      sortId: widget.sortId,
                      mainId: widget.mainId,
                      childId: widget.childId,
                      luid: widget.luid,
                      ids: widget.ids,
                    ),
                    BrowseView(
                      subject: widget.subject,
                      type: widget.type,
                      sortId: widget.sortId,
                      mainId: widget.mainId,
                      childId: widget.childId,
                      luid: widget.luid,
                      ids: widget.ids,
                    ),
                    VideoView(
                      subject: widget.subject,
                      type: widget.type,
                      sortId: widget.sortId,
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  /// Build app bar
  Widget _buildAppBar(BuildContext context, ModeSwitchState state) {
    return AppBar(
      title: Text(_getModeTitle(state.currentMode)),
      backgroundColor: _getModeColor(state.currentMode),
      elevation: 0,
      actions: [
        if (state.isConfirmationRequired)
          IconButton(
            icon: const Icon(Icons.warning),
            onPressed: () => _showConfirmationDialog(context, state),
          ),
        if (state.isSwitching)
          const Padding(
            padding: EdgeInsets.all(16.0),
            child: SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          ),
      ],
    );
  }

  /// Get mode title
  String _getModeTitle(ExerciseMode mode) {
    switch (mode) {
      case ExerciseMode.exercise:
        return '做题练习';
      case ExerciseMode.listen:
        return '听题练习';
      case ExerciseMode.browse:
        return '背题练习';
      case ExerciseMode.video:
        return '视频学习';
    }
  }

  /// Get mode color
  Color _getModeColor(ExerciseMode mode) {
    switch (mode) {
      case ExerciseMode.exercise:
        return Colors.blue;
      case ExerciseMode.listen:
        return Colors.green;
      case ExerciseMode.browse:
        return Colors.orange;
      case ExerciseMode.video:
        return Colors.purple;
    }
  }

  /// Switch mode
  void _switchMode(BuildContext context, ExerciseMode mode) {
    final modeSwitchBloc = context.read<ModeSwitchBloc>();

    switch (mode) {
      case ExerciseMode.exercise:
        modeSwitchBloc.add(const SwitchToExercise());
        break;
      case ExerciseMode.listen:
        modeSwitchBloc.add(const SwitchToListen());
        break;
      case ExerciseMode.browse:
        modeSwitchBloc.add(const SwitchToBrowse());
        break;
      case ExerciseMode.video:
        modeSwitchBloc.add(const SwitchToVideo());
        break;
    }
  }

  /// Animate to page
  void _animateToPage(int index) {
    if (_pageController.hasClients) {
      _pageController.animateToPage(
        index,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// Show confirmation dialog
  void _showConfirmationDialog(BuildContext context, ModeSwitchState state) {
    if (state is! ModeSwitchConfirmationRequired) return;

    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认切换模式'),
        content: Text(state.reason),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<ModeSwitchBloc>().add(const CancelModeSwitch());
            },
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<ModeSwitchBloc>().add(
                    ConfirmModeSwitch(targetMode: state.targetMode),
                  );
            },
            child: const Text('确认'),
          ),
        ],
      ),
    );
  }
}
