import 'package:get_it/get_it.dart';
import 'package:dio/dio.dart';
import 'package:exercise_bloc/exercise_bloc.dart';

import '../data/mock_data_source.dart';

final getIt = GetIt.instance;

/// 配置模拟依赖注入
void configureMockDependencies() {
  // 清除现有注册
  getIt.reset();

  // 注册Dio
  getIt.registerLazySingleton<Dio>(() => Dio());

  // 注册模拟数据源
  getIt.registerLazySingleton<MockQuestionDataSource>(
    () => MockQuestionDataSource(),
  );

  getIt.registerLazySingleton<MockAudioDataSource>(
    () => MockAudioDataSource(),
  );

  getIt.registerLazySingleton<MockVideoDataSource>(
    () => MockVideoDataSource(),
  );

  getIt.registerLazySingleton<MockProgressDataSource>(
    () => MockProgressDataSource(),
  );

  // 注册Repository
  getIt.registerLazySingleton<QuestionRepository>(
    () => MockQuestionRepository(),
  );

  getIt.registerLazySingleton<AudioRepository>(
    () => MockAudioRepository(),
  );

  getIt.registerLazySingleton<VideoRepository>(
    () => MockVideoRepository(),
  );

  getIt.registerLazySingleton<ProgressRepository>(
    () => MockProgressRepository(),
  );

  // 注册UseCase
  getIt.registerLazySingleton<LoadQuestions>(
    () => LoadQuestions(getIt<QuestionRepository>()),
  );

  getIt.registerLazySingleton<AnswerQuestion>(
    () => AnswerQuestion(getIt<QuestionRepository>()),
  );

  getIt.registerLazySingleton<PlayAudio>(
    () => PlayAudio(getIt<AudioRepository>()),
  );

  getIt.registerLazySingleton<PlayVideo>(
    () => PlayVideo(getIt<VideoRepository>()),
  );

  getIt.registerLazySingleton<SaveProgress>(
    () => SaveProgress(getIt<ProgressRepository>()),
  );

  // 注册BLoC
  getIt.registerFactory<ExerciseBloc>(
    () => ExerciseBloc(
      loadQuestions: getIt<LoadQuestions>(),
      answerQuestion: getIt<AnswerQuestion>(),
    ),
  );

  getIt.registerFactory<ListenBloc>(
    () => ListenBloc(
      loadQuestions: getIt<LoadQuestions>(),
      playAudio: getIt<PlayAudio>(),
    ),
  );

  getIt.registerFactory<BrowseBloc>(
    () => BrowseBloc(
      loadQuestions: getIt<LoadQuestions>(),
    ),
  );

  getIt.registerFactory<VideoBloc>(
    () => VideoBloc(
      videoRepository: getIt<VideoRepository>(),
      playVideo: getIt<PlayVideo>(),
    ),
  );

  getIt.registerLazySingleton<ModeSwitchBloc>(
    () => ModeSwitchBloc(),
  );
}

/// 模拟题目Repository
class MockQuestionRepository implements QuestionRepository {
  @override
  Future<List<QuestionEntity>> getQuestions({
    required int subject,
    required String type,
    int? sortId,
    int? limit,
    int? offset,
  }) async {
    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 500));
    return MockDataSource.getMockQuestions();
  }

  @override
  Future<void> saveUserAnswer({
    required int questionId,
    required String answer,
    required bool isCorrect,
    String? userId,
  }) async {
    // 模拟保存操作
    await Future.delayed(const Duration(milliseconds: 100));
  }

  @override
  Future<List<QuestionEntity>> getErrorQuestions({
    String? userId,
    int? limit,
  }) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return MockDataSource.getMockQuestions().take(3).toList();
  }

  @override
  Future<List<QuestionEntity>> getFavoriteQuestions({
    String? userId,
    int? limit,
  }) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return MockDataSource.getMockQuestions().take(2).toList();
  }

  @override
  Future<void> syncWithServer() async {
    await Future.delayed(const Duration(milliseconds: 200));
  }
}

/// 模拟音频Repository
class MockAudioRepository implements AudioRepository {
  bool _isPlaying = false;
  bool _isPaused = false;
  Duration _position = Duration.zero;
  Duration _duration = const Duration(minutes: 2);

  @override
  Future<void> play(String url) async {
    await Future.delayed(const Duration(milliseconds: 100));
    _isPlaying = true;
    _isPaused = false;
  }

  @override
  Future<void> pause() async {
    _isPlaying = false;
    _isPaused = true;
  }

  @override
  Future<void> resume() async {
    _isPlaying = true;
    _isPaused = false;
  }

  @override
  Future<void> stop() async {
    _isPlaying = false;
    _isPaused = false;
    _position = Duration.zero;
  }

  @override
  Future<void> seekTo(Duration position) async {
    _position = position;
  }

  @override
  Future<void> setVolume(double volume) async {}

  @override
  Future<void> setPlaybackRate(double rate) async {}

  @override
  bool get isPlaying => _isPlaying;

  @override
  bool get isPaused => _isPaused;

  @override
  bool get isStopped => !_isPlaying && !_isPaused;

  @override
  Future<Duration> getCurrentPosition() async => _position;

  @override
  Future<Duration> getDuration() async => _duration;

  @override
  Stream<Duration> get positionStream => Stream.periodic(
        const Duration(seconds: 1),
        (count) => Duration(seconds: count),
      );

  @override
  Stream<Duration> get durationStream => Stream.value(_duration);

  @override
  Stream<AudioPlayerState> get playerStateStream => Stream.value(
        _isPlaying ? AudioPlayerState.playing : AudioPlayerState.stopped,
      );

  @override
  Stream<void> get onPlayerComplete => const Stream.empty();

  @override
  Stream<AudioInterruption> get onAudioInterruption => const Stream.empty();

  @override
  Future<void> dispose() async {}
}

/// 模拟视频Repository
class MockVideoRepository implements VideoRepository {
  @override
  Future<List<VideoEntity>> getVideosForQuestions(List<int> questionIds) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return MockDataSource.getMockVideos();
  }

  @override
  Future<String> getVideoUrl(String aliyunVid) async {
    await Future.delayed(const Duration(milliseconds: 200));
    return 'https://example.com/videos/$aliyunVid.mp4';
  }

  @override
  Future<VideoEntity?> getVideoByQuestionId(int questionId) async {
    final videos = await getVideosForQuestions([questionId]);
    return videos.isNotEmpty ? videos.first : null;
  }

  @override
  Future<void> saveVideoProgress({
    required int videoId,
    required int currentPosition,
    required int duration,
    String? userId,
  }) async {
    await Future.delayed(const Duration(milliseconds: 100));
  }

  @override
  Future<void> updateVideoPlayCount({
    required int videoId,
    String? userId,
  }) async {
    await Future.delayed(const Duration(milliseconds: 100));
  }

  @override
  Future<List<VideoEntity>> getVideoHistory({
    String? userId,
    int? limit,
  }) async {
    await Future.delayed(const Duration(milliseconds: 200));
    return MockDataSource.getMockVideos();
  }

  @override
  Future<bool> isVideoDownloaded(String aliyunVid) async => false;

  @override
  Future<String?> getLocalVideoPath(String aliyunVid) async => null;

  @override
  Future<void> downloadVideo(String aliyunVid, String videoUrl) async {
    await Future.delayed(const Duration(seconds: 2));
  }

  @override
  Future<void> clearVideoCache() async {}

  @override
  Stream<double> getDownloadProgress(String aliyunVid) => const Stream.empty();

  @override
  Future<void> cancelDownload(String aliyunVid) async {}
}

/// 模拟进度Repository
class MockProgressRepository implements ProgressRepository {
  @override
  Future<void> saveProgress(ExerciseProgress progress, {String? userId}) async {
    await Future.delayed(const Duration(milliseconds: 100));
  }

  @override
  Future<ExerciseProgress> getProgress({String? userId}) async {
    await Future.delayed(const Duration(milliseconds: 200));
    return MockDataSource.getMockProgress();
  }

  @override
  Future<Map<String, dynamic>> getStatistics({String? userId}) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return {
      'totalQuestions': 100,
      'correctCount': 85,
      'wrongCount': 15,
      'accuracy': 0.85,
      'timeSpent': 3600,
      'studyDays': 7,
    };
  }

  @override
  Future<void> syncProgress({String? userId}) async {
    await Future.delayed(const Duration(milliseconds: 500));
  }
}

// 模拟数据源类
class MockQuestionDataSource {}
class MockAudioDataSource {}
class MockVideoDataSource {}
class MockProgressDataSource {}
