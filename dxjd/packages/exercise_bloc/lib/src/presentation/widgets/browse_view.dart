import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/browse/browse_bloc.dart';
import '../bloc/browse/browse_event.dart';
import '../bloc/browse/browse_state.dart';

/// Browse view for reviewing questions
class BrowseView extends StatefulWidget {
  const BrowseView({
    super.key,
    required this.subject,
    required this.type,
    this.sortId,
    this.mainId,
    this.childId,
    this.luid,
    this.ids,
  });

  final int subject;
  final String type;
  final int? sortId;
  final int? mainId;
  final int? childId;
  final int? luid;
  final List<int>? ids;

  @override
  State<BrowseView> createState() => _BrowseViewState();
}

class _BrowseViewState extends State<BrowseView> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Load questions when view is initialized
    context.read<BrowseBloc>().add(LoadBrowseQuestions(
      subject: widget.subject,
      type: widget.type,
      sortId: widget.sortId,
      mainId: widget.mainId,
      childId: widget.childId,
      luid: widget.luid,
      ids: widget.ids,
    ));
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BrowseBloc, BrowseState>(
      builder: (context, state) {
        if (state is BrowseInitial) {
          return const Center(
            child: Text('准备加载背题...'),
          );
        }

        if (state is BrowseLoading) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('正在加载背题...'),
              ],
            ),
          );
        }

        if (state is BrowseError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 16),
                Text(
                  '加载失败',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  state.message,
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    context.read<BrowseBloc>().add(LoadBrowseQuestions(
                      subject: widget.subject,
                      type: widget.type,
                      sortId: widget.sortId,
                      mainId: widget.mainId,
                      childId: widget.childId,
                      luid: widget.luid,
                      ids: widget.ids,
                    ));
                  },
                  child: const Text('重试'),
                ),
              ],
            ),
          );
        }

        if (state is BrowseLoaded) {
          return Column(
            children: [
              // Search and filter bar
              Container(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // Search field
                    TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: '搜索题目...',
                        prefixIcon: const Icon(Icons.search),
                        suffixIcon: _searchController.text.isNotEmpty
                            ? IconButton(
                                onPressed: () {
                                  _searchController.clear();
                                  context.read<BrowseBloc>().add(const ClearBrowseSearch());
                                },
                                icon: const Icon(Icons.clear),
                              )
                            : null,
                        border: const OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        context.read<BrowseBloc>().add(SearchBrowseQuestions(value));
                      },
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // Filter chips
                    Wrap(
                      spacing: 8,
                      children: [
                        FilterChip(
                          label: const Text('单选题'),
                          selected: state.questionTypeFilter == 'single',
                          onSelected: (selected) {
                            context.read<BrowseBloc>().add(FilterQuestions(
                              questionType: selected ? 'single' : null,
                            ));
                          },
                        ),
                        FilterChip(
                          label: const Text('多选题'),
                          selected: state.questionTypeFilter == 'multiple',
                          onSelected: (selected) {
                            context.read<BrowseBloc>().add(FilterQuestions(
                              questionType: selected ? 'multiple' : null,
                            ));
                          },
                        ),
                        FilterChip(
                          label: const Text('判断题'),
                          selected: state.questionTypeFilter == 'judge',
                          onSelected: (selected) {
                            context.read<BrowseBloc>().add(FilterQuestions(
                              questionType: selected ? 'judge' : null,
                            ));
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // Statistics
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildStatItem('总题数', state.totalQuestions.toString()),
                    _buildStatItem('已筛选', state.filteredCount.toString()),
                    _buildStatItem('已答题', state.statistics['answered'].toString()),
                    _buildStatItem('正确率', 
                        state.statistics['answered']! > 0 
                            ? '${((state.statistics['correct']! / state.statistics['answered']!) * 100).toStringAsFixed(1)}%'
                            : '0%'),
                  ],
                ),
              ),
              
              const Divider(),
              
              // Question content
              if (state.currentQuestion != null)
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Question header
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              '第 ${state.currentIndex + 1} 题',
                              style: Theme.of(context).textTheme.titleLarge,
                            ),
                            Row(
                              children: [
                                IconButton(
                                  onPressed: () => context.read<BrowseBloc>().add(
                                    ToggleBrowseCollection(state.currentIndex),
                                  ),
                                  icon: Icon(
                                    state.currentQuestion!.isCollected
                                        ? Icons.bookmark
                                        : Icons.bookmark_border,
                                    color: state.currentQuestion!.isCollected
                                        ? Colors.orange
                                        : null,
                                  ),
                                ),
                                IconButton(
                                  onPressed: () => context.read<BrowseBloc>().add(
                                    const ToggleAnswerVisibility(),
                                  ),
                                  icon: Icon(
                                    state.showAnswers
                                        ? Icons.visibility
                                        : Icons.visibility_off,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // Question title
                        Text(
                          state.currentQuestion!.title,
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // Question image (if available)
                        if (state.currentQuestion!.imagePath != null)
                          Container(
                            width: double.infinity,
                            height: 200,
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Center(
                              child: Text('题目图片'),
                            ),
                          ),
                        
                        const SizedBox(height: 16),
                        
                        // Options
                        ...state.currentQuestion!.options.map((option) {
                          final isCorrect = state.currentQuestion!.correctAnswer.contains(option.key);
                          final isUserAnswer = state.currentQuestion!.userAnswer?.contains(option.key) ?? false;
                          
                          Color? backgroundColor;
                          if (state.showAnswers) {
                            if (isCorrect) {
                              backgroundColor = Colors.green[100];
                            } else if (isUserAnswer && !isCorrect) {
                              backgroundColor = Colors.red[100];
                            }
                          }
                          
                          return Container(
                            margin: const EdgeInsets.only(bottom: 8),
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: backgroundColor,
                              border: Border.all(
                                color: isCorrect && state.showAnswers
                                    ? Colors.green
                                    : Colors.grey,
                                width: isCorrect && state.showAnswers ? 2 : 1,
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Text(
                                  '${option.key.toUpperCase()}.',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(option.text),
                                ),
                                if (state.showAnswers && isCorrect)
                                  const Icon(
                                    Icons.check_circle,
                                    color: Colors.green,
                                  ),
                              ],
                            ),
                          );
                        }).toList(),
                        
                        // Answer explanation (if available and showing answers)
                        if (state.showAnswers && state.currentQuestion!.explanation != null)
                          Container(
                            margin: const EdgeInsets.only(top: 16),
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.grey[100],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  '答案解析:',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(state.currentQuestion!.explanation!),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              
              // Navigation
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    ElevatedButton(
                      onPressed: state.canGoPrevious
                          ? () => context.read<BrowseBloc>().add(const PreviousBrowseQuestion())
                          : null,
                      child: const Text('上一题'),
                    ),
                    Text('${state.currentIndex + 1} / ${state.filteredCount}'),
                    ElevatedButton(
                      onPressed: state.canGoNext
                          ? () => context.read<BrowseBloc>().add(const NextBrowseQuestion())
                          : null,
                      child: const Text('下一题'),
                    ),
                  ],
                ),
              ),
            ],
          );
        }

        return const Center(
          child: Text('未知状态'),
        );
      },
    );
  }

  /// Build statistics item
  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }
}
