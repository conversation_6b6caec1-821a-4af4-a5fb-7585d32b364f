import 'package:flutter/material.dart';

/// Video view for video-based learning
class VideoView extends StatelessWidget {
  const VideoView({
    super.key,
    required this.subject,
    required this.type,
    this.sortId,
  });

  final int subject;
  final String type;
  final int? sortId;

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.video_library,
            size: 64,
            color: Colors.purple,
          ),
          SizedBox(height: 16),
          Text(
            '视频学习',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8),
          Text(
            '视频功能正在开发中...',
            style: TextStyle(
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }
}
