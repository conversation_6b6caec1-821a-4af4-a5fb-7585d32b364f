import 'dart:async';
import 'dart:convert';
import 'dart:ffi';
import 'package:api/api.dart';
import 'package:component_library/component_library.dart';
import 'package:domain_models/domain_models.dart';
import 'package:dxjd/app_routes.dart';
import 'package:dxjd/main.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:home/src/home/<USER>';
import 'package:home/src/home/<USER>/get_the_certificate_controller.dart';
import 'package:home/src/home/<USER>/get_the_certificate_page.dart';
import 'package:home/src/home/<USER>/section_four_page.dart';
import 'package:home_repository/home_repository.dart';
import 'package:login/login.dart';
import 'package:mop/mop.dart';
import 'package:timing_repository/timing_repository.dart';
import 'package:user_repository/user_repository.dart';
import 'custom_widget/grallist.dart';
import 'custom_widget/keep_alive_wrappers.dart';
import 'custom_widget/myIndicator.dart';
import 'page/section_one_page.dart';
import 'page/section_three_controller.dart';
import 'page/section_three_page.dart';
import 'page/section_two_controller.dart';
import 'page/section_two_page.dart';
import 'package:dxjd/mainController.dart';
import 'package:key_value_storage/key_value_storage.dart';
import 'exercise_bloc_demo_page.dart';

/**
 * 考试页面
 */
class ExaminationPage extends StatefulWidget {
  ExaminationPage({
    super.key,
  });

  @override
  State<ExaminationPage> createState() => ExaminationPageState();
}

class ExaminationPageState extends State<ExaminationPage>
    with
        AutomaticKeepAliveClientMixin,
        SingleTickerProviderStateMixin,
        RouteAware {
  @override
  bool get wantKeepAlive => true;
  ExaminationController? examinationController;
  static late Key key;
  @override
  void initState() {
    key = UniqueKey();
    examinationController =
        Get.put(ExaminationController(), permanent: true, tag: key.toString());
    print("-------ExaminationPage initState key}");
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 订阅路由观察者
    // 获取当前路由
    final route = ModalRoute.of(context);
    // 检查路由是否为 PageRoute 类型
    if (route is PageRoute) {
      routeObserver.subscribe(this, route);
    }
  }

  @override
  void dispose() {
    // 取消订阅并释放资源
    routeObserver.unsubscribe(this);
    super.dispose();
  }

  // 当跳转到其他页面时暂停播放
  @override
  void didPushNext() {
    if (examinationController?.currentIndex == 4) {
      if (Get.isRegistered<GetTheCertificateController>()) {
        GetTheCertificateController controller =
            Get.find<GetTheCertificateController>();
        controller.controller.pause();
      }
    }
    //跳转其他页面暂停广告获取
    // examinationController?.refreshAdTimerPause();
    super.didPushNext();
  }

  // 当返回到当前页面时恢复播放
  @override
  void didPopNext() {
    if (examinationController?.currentIndex == 4) {
      if (Get.isRegistered<GetTheCertificateController>()) {
        GetTheCertificateController controller =
            Get.find<GetTheCertificateController>();
        controller.controller.play();
      }
    }
    // 恢复广告获取
    // examinationController?.refreshAdTimerStart();
    examinationController?.startAndStopBannerScroll(
        examinationController!.tabController!.index + 1, false,
        isReset: true);
    super.didPopNext();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    // debugPrint("-------ExaminationPage initState${S.of(context).subjectOne}");
    return SafeArea(
      top: false,
      child: GetBuilder<ExaminationController>(
          init: examinationController,
          autoRemove: false,
          tag: key.toString(),
          assignId: true,
          builder: (logic) {
            return Scaffold(
              body: Stack(
                children: [
                  Column(
                    children: [
                      _appBar(logic, userAccount: logic.userAccount),
                      if (logic.isShowQuestionUpdate)
                        Stack(
                          children: [
                            Padding(
                              padding: REdgeInsets.symmetric(horizontal: 16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    "已更新到${DateTime.now().month}月",
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                        fontSize: 14.sp,
                                        color: AppColors.AAAAAA,
                                        fontFamily: 'PingFangSC-Regular'),
                                  ),
                                  Text(
                                    "${logic.selectCity}新规题库",
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                        fontSize: 10.sp,
                                        color: Colors.grey,
                                        fontFamily: 'PingFangSC-Regular'),
                                  ),
                                ],
                              ),
                            ),
                            Image.asset(
                              "assets/home_img/tiku_loading.gif",
                              key: UniqueKey(),
                            ),
                          ],
                        ),

                      // galleryList(
                      //     bannerListDm: logic.bannerList,
                      //     timingRepository: logic.timingRepository,
                      //     isTabHome: true),
                      !logic.isGetLastTabIndex
                          ? const SizedBox()
                          : _buildAppBarSubjectTabBar(logic),
                      !logic.isGetLastTabIndex
                          ? const SizedBox()
                          : Expanded(
                              // child:
                              // Listener(
                              //   onPointerDown: (PointerDownEvent event) {
                              //     print('==========开始移动');
                              //     isStartScroll = 0;
                              //   },
                              //   onPointerMove: (PointerMoveEvent details) {
                              //     if (isStartScroll == 0) {
                              //       if (currentIndex.toDouble() >
                              //           _tabController.animation!.value) {
                              //         print(
                              //             '==========移动向左移动,当前value${_tabController.animation?.value},当前next$next');
                              //         next = _tabController.index - 1;
                              //         setState(() {
                              //           isStartScroll = 1;
                              //         });
                              //       } else if (currentIndex.toDouble() <
                              //           _tabController.animation!.value) {
                              //         print(
                              //             '==========移动向右移动,当前value${_tabController.animation?.value},当前next$next');
                              //         next = _tabController.index + 1;
                              //         setState(() {
                              //           isStartScroll = 1;
                              //         });
                              //       }
                              //     }
                              //     // 当手指在屏幕上移动时触发
                              //     print(
                              //         '_tabController.animationDuration update: ${_tabController.animation?.value}');
                              //   },
                              //   onPointerUp: (PointerUpEvent details) {
                              // if (isStartScroll == 1) {
                              //   if (next == _tabController.index + 1) {
                              //     if (_tabController.animation!.value <
                              //         _tabController.index.toDouble() + 0.5) {
                              //       setState(() {
                              //         isStartScroll = -1;
                              //       });
                              //     }
                              //   } else if (next == _tabController.index - 1) {
                              //     if (_tabController.animation!.value >
                              //         _tabController.index.toDouble() - 0.5) {
                              //       setState(() {
                              //         isStartScroll = -1;
                              //       });
                              //     }
                              //   }
                              // }
                              // setState(() {
                              //   next = -1;
                              // });
                              // isStartScroll = -1;
                              // },
                              child: TabBarView(
                                physics: const NeverScrollableScrollPhysics(),
                                controller: logic.tabController,
                                children: logic.tabs.map((e) {
                                  return KeepAliveWrappers(
                                    child: Container(
                                      alignment: Alignment.center,
                                      child: e == "科一"
                                          ? SectionOnePage(
                                              gotoPractitionerVideo: () {
                                                Get.toNamed(
                                                    AppRoutes.subOneTheoryVideo,
                                                    arguments: {
                                                      'subject': 1,
                                                      'isPractitioner': true
                                                    });
                                              },
                                              userAccount: logic.userAccount,
                                            )
                                          : e == "科二"
                                              ? SectionTwoPage(
                                                  homeRepository:
                                                      logic.homeRepository,
                                                  gotoSubjectTwoPracticalVideoDetail:
                                                      (
                                                          {int? index,
                                                          TheoryVideoListDm?
                                                              theoryVideoListDM,
                                                          String? vid}) {
                                                    logic.homeRepository
                                                        .setSubTwoPracticalModeAndIndex(
                                                            theoryVideoListDM,
                                                            index!,
                                                            vid);
                                                    Get.toNamed(
                                                      AppRoutes
                                                          .subTwoPracticalVideoDetail,
                                                    );
                                                  },
                                                  switchExaminationRoomCallback:
                                                      (menuMap, exmListData) {
                                                    logic.homeRepository
                                                        .setExmMenuAndListData(
                                                            menuMap,
                                                            exmListData);
                                                    Get.toNamed(
                                                      AppRoutes
                                                          .examinationRoomSelectScreen,
                                                    );
                                                  },
                                                  timingRepository:
                                                      logic.timingRepository,
                                                  userRepository:
                                                      logic.userRepository,
                                                )
                                              : e == "科三"
                                                  ? SectionThreePage(
                                                      homeRepository:
                                                          logic.homeRepository,
                                                      gotoSubjectThreePracticalVideoDetail: (
                                                          {int? index,
                                                          TheoryVideoListDm?
                                                              theoryVideoListDM,
                                                          String? vid}) {
                                                        logic.homeRepository
                                                            .setSubThreePracticalModeAndIndex(
                                                                theoryVideoListDM,
                                                                index!,
                                                                vid);
                                                        Get.toNamed(AppRoutes
                                                            .subThreePracticalVideoDetail);
                                                      },
                                                      subThreeVip45DetailVideoCallback:
                                                          (ExaminationRoomRouteListElementDm?
                                                              examinationRoomRouteListElementDm) {
                                                        logic.homeRepository
                                                            .setExaminationRoomSubThreeRouteListElementDm(
                                                                examinationRoomRouteListElementDm);
                                                        Get.toNamed(AppRoutes
                                                            .subThreeVip45DetailPage);
                                                      },
                                                      // gotoPointMapSubThreeCallback:(){
                                                      //   logic.homeRepository
                                                      //       .setExaminationRoomSubThreeRouteListElementDm(
                                                      //       examinationRoomRouteListElementDm);
                                                      //   Get.toNamed(AppRoutes
                                                      //       .subThreeVip45DetailPage);
                                                      // },
                                                      // switchExaminationRoomSubThreeCallback:
                                                      //     (menuMap,
                                                      //     exmListData) {
                                                      //   logic.homeRepository
                                                      //       .setExmMenuAndListData(
                                                      //       menuMap,
                                                      //       exmListData);
                                                      //   Get.toNamed(
                                                      //     AppRoutes
                                                      //         .examinationRoomSubThreeSelectScreen,
                                                      //   );
                                                      //   },
                                                      //               gotoRoadTestDetailCallback:
                                                      //                   (menuMap,
                                                      //                       exmListData) {
                                                      //                 logic.homeRepository
                                                      //                     .setExmMenuAndListData(
                                                      //                         menuMap,
                                                      //                         exmListData);
                                                      //                 Get.toNamed(
                                                      //                     AppRoutes
                                                      //                         .subThreeRoadTest,
                                                      //                     arguments: {
                                                      //                       'isRoad': true,
                                                      //                     });
                                                      //               },
                                                      timingRepository: logic
                                                          .timingRepository,
                                                      userRepository:
                                                          logic.userRepository)
                                                  : e == '科四'
                                                      ? SectionFourPage(
                                                          gotoPractitionerVideo:
                                                              () {
                                                            Get.toNamed(
                                                                AppRoutes
                                                                    .subOneTheoryVideo,
                                                                arguments: {
                                                                  'subject': 4,
                                                                  'isPractitioner':
                                                                      true
                                                                });
                                                          },
                                                          timingRepository: logic
                                                              .timingRepository,
                                                          userAccount:
                                                              logic.userAccount,
                                                        )
                                                      : GetTheCertificatePage(),
                                    ),
                                  );
                                }).toList(),
                              ),
                            ),
                      // ),
                    ],
                  ),
                  if (logic.floatMap.isEmpty &&
                      logic.dialogMap['Position'] != '首页')
                    const SizedBox()
                  else
                    Positioned(
                      left: logic.xPosition,
                      top: logic.yPosition,
                      child: Draggable(
                        feedback: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(
                              width: 20.w,
                              height: 20.w,
                              child: IconButton(
                                icon: assImg2(
                                  img: "icon_close",
                                ),
                                padding: EdgeInsets.zero,
                                onPressed: () {
                                  logic.floatMap = {};
                                  setState(() {});
                                },
                              ),
                            ),
                            SizedBox(
                              height: 11.h,
                            ),
                            netWorkCacheImg2(
                                img: logic.floatMap['Url'], w: 85.w, h: 85.w),
                          ],
                        ),
                        childWhenDragging: Container(),
                        onDragEnd: (details) {
                          if (details.offset.dy >
                              MediaQuery.of(context).size.height - 64 - 70.h) {
                            return;
                          }
                          setState(() {
                            logic.xPosition = details.offset.dx;
                            logic.yPosition = details.offset.dy;
                          });
                        },
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(
                              width: 20.w,
                              height: 20.w,
                              child: IconButton(
                                icon: assImg2(
                                  img: "icon_close",
                                ),
                                padding: EdgeInsets.zero,
                                onPressed: () {
                                  logic.floatMap = {};
                                  setState(() {});
                                },
                              ),
                            ),
                            SizedBox(
                              height: 11.h,
                            ),
                            InkWell(
                              onTap: () {
                                BuryingPointUtils.instance.addPoint(
                                    buryingPointList: BuryingPointList(
                                        eventType: 4,
                                        entranceType: 4,
                                        action: 1,
                                        browseDuration: 0));
                                if (logic.floatMap['RedirectType'] == 2) {
                                  Map map =
                                      jsonDecode(logic.floatMap['RedirectApp']);
                                  // Mop.instance
                                  //     .openApplet(, path: );
                                  JumpSmallProgramUtils.jump(
                                      logic.floatMap['RedirectPath'],
                                      map['AppId']);
                                } else if (logic.floatMap['RedirectType'] ==
                                    3) {
                                  var map =
                                      jsonDecode(logic.floatMap['RedirectApp']);
                                  logic.timingRepository.openFluwxMiniProgram(
                                    logic.floatMap['RedirectPath'],
                                    map['AppId'],
                                  );
                                } else if (logic.floatMap['RedirectType'] ==
                                    5) {
                                  Get.toNamed(AppRoutes.webView, arguments: {
                                    'url': logic.floatMap['RedirectPath'],
                                    'title': logic.floatMap['title']
                                  });
                                  // widget.goWebView(
                                  //     floatMap['RedirectPath'], floatMap['title']);
                                }
                              },
                              child: netWorkCacheImg2(
                                  img: logic.floatMap['Url'], w: 85.w, h: 85.w),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            );
          }),
    );
  }

  //appBar
  Widget _appBar(ExaminationController logic, {UserAccount? userAccount}) {
    return Container(
      color: Colors.white,
      height: 50.h + MediaQuery.of(context).padding.top,
      padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          _selectCityBtn(logic),
          Container(
            margin: EdgeInsets.symmetric(horizontal: 8.w),
            height: 8.h,
            width: 0.5.w,
            color: AppColors.CCCCCC,
          ),
          GestureDetector(
            onTap: userAccount?.isBind == 1 && userAccount?.platGradStatus != 3
                ? () {
                    Toast.show('您已绑定驾校,不可切换题库!');
                  }
                : userAccount?.isBind == 1 && userAccount?.platGradStatus == 3
                    ? () {
                        Get.toNamed(AppRoutes.topicSelectPage);
                      }
                    : () {
                        if (!MainController.isLoginIntercept()) {
                          return;
                        }
                        Get.toNamed(AppRoutes.homeStudyTypeSelect);
                      },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                app14spA1Text(
                    logic.isChangeCarType == null ||
                            logic.isChangeCarType!.isEmpty
                        ? "${trainTypeMap[userAccount?.topicType ?? "C1"] ?? 'load...'}"
                        : "${trainTypeMap[logic.isChangeCarType ?? "C1"] ?? 'load...'}",
                    // "${trainTypeMap[userAccount?.topicType ?? "C1"] ?? 'load...'}",
                    fontFamily: 'PingFangSC-Semibold'),
                assImg2(img: 'car_pull', w: 6.w, h: 6.h).paddingOnly(top: 3.w),
              ],
            ),
          ),

          // 添加测试重构功能按钮
          Container(
            margin: EdgeInsets.symmetric(horizontal: 8.w),
            height: 8.h,
            width: 0.5.w,
            color: AppColors.CCCCCC,
          ),
          GestureDetector(
            onTap: () {
              if (!MainController.isLoginIntercept()) {
                return;
              }
              _navigateToExerciseBlocExample();
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
              decoration: BoxDecoration(
                color: const Color(0xFF1992EF),
                borderRadius: BorderRadius.circular(4.r),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.auto_awesome,
                    color: Colors.white,
                    size: 12.w,
                  ),
                  SizedBox(width: 4.w),
                  Text(
                    '测试重构',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10.sp,
                      fontWeight: FontWeight.w500,
                      fontFamily: 'PingFangSC-Medium',
                    ),
                  ),
                ],
              ),
            ),
          ),

          Expanded(child: Container()),
          GestureDetector(
            onTap: () {
              logic.timingRepository.openTimingMiniProgram(
                  path: '/pages/operate/article/hot/hot');
            },
            child: assImg2(img: 'icon_dudu', w: 24.w, h: 24.h),
          ).paddingSymmetric(horizontal: 13.w),
          GestureDetector(
            onTap: () {
              if (!MainController.isLoginIntercept()) {
                return;
              }
              BuryingPointUtils.instance.addPoint(
                  buryingPointList: BuryingPointList(
                      eventType: 4,
                      entranceType: 2,
                      action: 1,
                      browseDuration: 0));
              logic.timingRepository.openTimingMiniProgram(
                  path: '/pages/operate/notice/list/list');
            },
            child: Obx(() => Stack(
                  children: [
                    Container(
                      padding:
                          EdgeInsets.only(top: 3.w, bottom: 3.w, right: 6.w),
                      child: logic.homeRepository.messageCount.value > 0
                          ? Image.asset(
                              "assets/home_img/icon_service_gif.gif",
                              width: 24.w,
                              height: 24.h,
                              fit: BoxFit.cover,
                            )
                          : assImg2(img: 'icon_service', w: 24.w, h: 24.h),
                    ),
                    logic.homeRepository.messageCount.value > 0
                        ? Positioned(
                            right: 0.w,
                            top: 0,
                            child: Container(
                                width: 18.h,
                                height: 11.h,
                                decoration: BoxDecoration(
                                    // borderRadius: BorderRadius.circular(18),
                                    // color: Color(0xffF4043E),
                                    image: DecorationImage(
                                  image:
                                      assetImg2(img: 'home_notice_redpiont_bg'),
                                )),
                                child: Center(
                                  child: app10spAAAA7Text(
                                      '${logic.homeRepository.messageCount.value}',
                                      color: Colors.white,
                                      fontFamily: 'PingFangSC-Medium',
                                      height: 1.0.h),
                                )))
                        : const SizedBox(),
                  ],
                )),
          ).paddingOnly(right: 9.w)
        ],
      ),
    );
  }

  //选择城市btn
  Widget _selectCityBtn(ExaminationController logic) {
    return GestureDetector(
      onTap: () {
        if (!MainController.isLoginIntercept()) {
          return;
        }
        if (logic.userAccount?.topicType == null) {
          Get.toNamed(AppRoutes.homeStudyTypeSelect);
        } else {
          logic.goChangeQuestionBankPage();
        }
      },
      child: Row(
        children: [
          assImg2(img: 'icon_location', w: 24.w, h: 24.h),
          app14spA1Text(
                  logic.isChangeCityType == null ||
                          logic.isChangeCityType!.isEmpty
                      ? logic.selectCity
                      : logic.isChangeCityType,
                  overflow: TextOverflow.ellipsis,
                  fontFamily: 'PingFangSC-Medium')
              .paddingOnly(left: 8.w),
        ],
      ).paddingOnly(left: 14.w),
    );
  }

  //首页科目tabBar
  Widget _buildAppBarSubjectTabBar(ExaminationController logic) {
    return Container(
      margin: EdgeInsets.only(bottom: 11.h),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
              color: Color(0xffF5F7F9),
              offset: Offset(-6.0.h, 6.0.h), //阴影y轴偏移量
              blurRadius: 4.h, //阴影模糊程度
              spreadRadius: 1.r)
        ],
      ),
      child: TabBar(
        overlayColor: MaterialStateProperty.all(Colors.transparent),
        dividerHeight: 0,
        // labelStyle: TextStyle(color: AppColors.C9E5500, fontSize: 16.sp),
        // unselectedLabelStyle:W
        //     TextStyle(color: AppColors.E4BB72, fontSize: 15.sp),
        // indicatorWeight: 31,
        // labelPadding: EdgeInsets.zero,
        // padding: EdgeInsets.only(top: 8.h),
        // indicatorSize: TabBarIndicatorSize.tab,
        // unselectedLabelColor: AppColors.AAAAAA,
        // labelColor: AppColors.other_login_text,
        // indicatorPadding: EdgeInsets.only(top: 40.h, left: 20.w, bottom: 1.h),
        // labelPadding: REdgeInsets.only(bottom: 15),
        // indicator: MyArcIndicator(
        //     color: logic.userRepository.getSubVip(logic.currentIndex + 1)
        //         ? const Color(0xffc17600)
        //         : AppColors.other_login_text),
        indicatorSize: TabBarIndicatorSize.tab,
        indicator: logic.userRepository.getSubVip(logic.currentIndex + 1)
            ? BoxDecoration(
                image: DecorationImage(
                image: assetImg2(
                    img: 'home_tab_vip_${logic.currentIndex + 1}_bg', w: 20.w),
              ))
            : BoxDecoration(
                borderRadius: BorderRadius.circular(20.r), // 控制椭圆弧度
                gradient: const LinearGradient(
                    colors: [Color(0xff7CD8FF), Color(0xff3A8DCD)],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter // 渐变颜色
                    ),
              ),
        controller: logic.tabController,
        onTap: (index) {
          logic.changeTabBarIndexCallBack();
        },
        tabs: logic.tabs
            .asMap()
            .entries
            .map((e) => myEchoTab(e.value, e.key, logic))
            .toList(),
        // tabs: [
        //   Tab(
        //     text: "科一",
        //   ),
        //   Tab(
        //     text: "科一",
        //   ),
        //   Tab(
        //     text: "科一",
        //   ),
        //   Tab(
        //     text: "科一",
        //   ),
        // ],
        // tabs: [
        // ],
      ).paddingOnly(left: 14.w, right: 14.w, bottom: 10.h),
    );
  }

  Widget myEchoTab(e, index, ExaminationController logic) {
    return Container(
      alignment: Alignment.center,
      height: 28.h,
      width: 67.w,
      child: Stack(
        children: [
          Text(
            logic.userRepository.getSubVip(index + 1) &&
                    index == logic.currentIndex
                ? ""
                : "$e",
            textAlign: TextAlign.center,
            style: TextStyle(
                fontSize: _getTabSp(index, logic),
                color: _getTextColor(index, logic),
                fontFamily: 'PingFangSC-Semibold'),
          ),
          // logic.userRepository.getSubVip(index + 1)
          //     ? (logic.currentIndex != index
          //         ? Positioned(
          //             top: 4.h,
          //             left: 0,
          //             child: assImg2(img: 'icon_huangguang', w: 11.w)
          //                 .paddingOnly(left: 3.w))
          //         : Positioned(
          //             top: 0,
          //             right: 0,
          //             child: assImg2(img: 'icon_huangguang_selected', w: 18.w)
          //                 .paddingOnly(left: 3.w, bottom: 5.h)))
          //     : const SizedBox()
        ],
      ),
    );
  }

  double _getTabSp(int index, ExaminationController logic) {
    if (logic.currentIndex == index) {
      return 16.sp;
    } else {
      return 14.sp;
    }
  }

  Color _getTextColor(int index, ExaminationController logic) {
    if (logic.userRepository.getSubVip(index + 1)) {
      if (logic.currentIndex == index) {
        return AppColors.white;
      } else {
        return Colors.black;
      }
    } else {
      if (logic.currentIndex == index) {
        return AppColors.white;
      } else {
        return Colors.black;
      }
    }
  }

  /// 导航到Exercise BLoC重构功能页面
  void _navigateToExerciseBlocExample() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ExerciseBlocDemoPage(),
      ),
    );
  }
}

class HomeDialog extends StatelessWidget {
  final String imageUrl;
  Map dialogMap;
  TimingRepository timingRepository;
  HomeDialog({
    required this.imageUrl,
    required this.dialogMap,
    required this.timingRepository,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      surfaceTintColor: Colors.transparent,
      backgroundColor: Colors.transparent, // 背景透明
      child: Stack(
        alignment: Alignment.center,
        children: [
          Container(
            margin: const EdgeInsets.all(40),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
            ),
            child: InkWell(
                onTap: () {
                  BuryingPointUtils.instance.addPoint(
                      buryingPointList: BuryingPointList(
                          eventType: 4,
                          entranceType: 3,
                          action: 1,
                          browseDuration: 0));
                  if (dialogMap['RedirectType'] == 2) {
                    Map map = jsonDecode(dialogMap['RedirectApp']);
                    // Mop.instance
                    //     .openApplet(, path: );
                    JumpSmallProgramUtils.jump(
                        dialogMap['RedirectPath'], map['AppId']);
                  } else if (dialogMap['RedirectType'] == 3) {
                    var map = jsonDecode(dialogMap['RedirectApp']);
                    timingRepository.openFluwxMiniProgram(
                      dialogMap['RedirectPath'],
                      map['AppId'],
                    );
                  } else if (dialogMap['RedirectType'] == 5) {
                    Get.toNamed(AppRoutes.webView, arguments: {
                      'url': dialogMap['RedirectPath'],
                      'title': dialogMap['title']
                    });
                  }
                },
                child: Image.network(imageUrl)),
          ),
          Positioned(
            right: 0,
            top: 0,
            child: IconButton(
              icon: Icon(
                Icons.close,
                size: 30.w,
                color: Colors.white,
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ),
        ],
      ),
    );
  }
}

class CouponDialog extends StatefulWidget {
  Map couponMap = {};
  CouponDialog({
    super.key,
    required this.couponMap,
  });

  @override
  State<CouponDialog> createState() => _CouponDialogState();
}

class _CouponDialogState extends State<CouponDialog> {
  int surplusTime = 0;
  String surplusTimeStr = "00:00:00";
  int dialogSurplusTimeStr = 2;
  var countDownTimer, dialogCountDownTimer;

  @override
  void initState() {
    _startTimer();
    _dismissDialogCountDown();
    super.initState();
  }

  //dialog显示倒计时
  void _dismissDialogCountDown() {
    const oneSec = Duration(seconds: 1);
    int count = 3;
    dialogCountDownTimer = Timer.periodic(oneSec, (timer) {
      count--;
      var leftTime = count;
      if (leftTime <= 0) {
        timer.cancel();
        setState(() {
          dialogSurplusTimeStr = 0;
        });
        _dismissDialog();
      } else {
        setState(() {
          dialogSurplusTimeStr = count;
        });
      }
    });
  }

  //启动计时器，实时倒计时剩余优惠时间
  void _startTimer() {
    surplusTime = widget.couponMap['ValidTime'] * 60 * 60;
    const oneSec = Duration(seconds: 1);
    countDownTimer = Timer.periodic(oneSec, (timer) {
      // var now = DateTime.now().millisecondsSinceEpoch;
      // var endTime = widget.surplusTime;
      setState(() {
        surplusTime--;
      });
    });
  }

  String formatSeconds(int totalSeconds) {
    // 计算小时、分钟和秒
    int hours = totalSeconds ~/ 3600;
    int minutes = (totalSeconds % 3600) ~/ 60;
    int seconds = totalSeconds % 60;

    // 格式化为两位数，不足补零
    String hoursStr = hours.toString().padLeft(2, '0');
    String minutesStr = minutes.toString().padLeft(2, '0');
    String secondsStr = seconds.toString().padLeft(2, '0');

    return '$hoursStr:$minutesStr:$secondsStr';
  }

  //跳转或关闭dialog
  void _dismissDialog() async {
    if (countDownTimer != null) {
      countDownTimer.cancel();
      countDownTimer = null;
    }
    if (dialogCountDownTimer != null) {
      dialogCountDownTimer.cancel();
      dialogCountDownTimer = null;
    }
    Navigator.of(context).pop();
    JumpCouPonPage.jumpCouPonUsePage(
        SellsGoodsInfoList.fromJson(widget.couponMap['SellsGoodsInfoList'][0]));
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      surfaceTintColor: Colors.transparent,
      backgroundColor: Colors.transparent, // 背景透明
      child: SizedBox(
        height: 362.h,
        child: Stack(
          alignment: Alignment.center,
          children: [
            assImg2(img: 'bg_receive_coupon', w: 353.w, h: 362.h),
            Positioned(
                left: 70.5.w,
                top: 110.h,
                child: Container(
                  padding: REdgeInsets.symmetric(horizontal: 9, vertical: 3),
                  height: 22.h,
                  width: 128.w,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFFED7A2F), Color(0xFFEC5629)],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ),
                    borderRadius: BorderRadius.only(
                        topRight: Radius.circular(11.r),
                        bottomRight: Radius.circular(11.r),
                        topLeft: Radius.circular(11.r)),
                  ),
                  child: Center(
                      child: Text(
                    '降价限时:${formatSeconds(surplusTime)}',
                    style: TextStyle(color: Colors.white, fontSize: 12.sp),
                  )),
                )),
            Positioned(
                left: 77.w,
                top: 180.h,
                child: Row(
                  children: [
                    SizedBox(
                      width: 74.w,
                      child: Row(
                        children: [
                          Text(
                            "￥",
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 14.sp,
                                height: 0.5,
                                fontWeight: FontWeight.bold,
                                fontFamily: "PingFangSC-Bold"),
                          ),
                          Text(
                            "${widget.couponMap['Discount'] ~/ 100}",
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 40.sp,
                                height: 0.75,
                                fontWeight: FontWeight.bold,
                                fontFamily: "PingFangSC-Bold"),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      width: 74.w,
                      height: 42.h,
                      child: Column(
                        children: [
                          Expanded(
                            child: Center(
                              child: Text(
                                widget.couponMap['CouponName'],
                                style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 14.sp,
                                    fontWeight: FontWeight.bold,
                                    overflow: TextOverflow.clip,
                                    fontFamily: "PingFangSC-Bold"),
                              ),
                            ),
                          ),
                        ],
                      ),
                    )
                  ],
                )),
            Positioned(
                right: 0,
                bottom: 70.h,
                left: 0,
                child: GestureDetector(
                  onTap: () {
                    _dismissDialog();
                  },
                  child: assImg2(img: 'icon_go_receive', w: 164.w, h: 51.h),
                )),
            Positioned(
                bottom: 30.h,
                child: Text(
                  "${dialogSurplusTimeStr}秒后自动领取",
                  style: TextStyle(
                      color: Colors.white,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold,
                      fontFamily: "PingFangSC-Bold"),
                )),
            Positioned(
                bottom: 0.h,
                child: InkWell(
                    onTap: () {
                      Get.back();
                    },
                    child: assImg2(img: 'icon_close_x', w: 30.w, h: 30.w))),
          ],
        ),
      ),
    );
  }
}

class NewUserDialog extends StatefulWidget {
  NewUserDialog({
    super.key,
    required this.surplusTime,
    required this.userRepository,
  });
  int surplusTime = 0;
  UserRepository userRepository;

  @override
  State<NewUserDialog> createState() => _NewUserDialogState(
      surplusTime: surplusTime, userRepository: userRepository);
}

class _NewUserDialogState extends State<NewUserDialog> {
  _NewUserDialogState({
    required this.surplusTime,
    required this.userRepository,
  });

  int surplusTime = 0;
  UserRepository userRepository;

  String surplusTimeStr = "00:00:00";
  int dialogSurplusTimeStr = 2;
  var countDownTimer, dialogCountDownTimer;
  @override
  void initState() {
    _startTimer();
    _dismissDialogCountDown();
    super.initState();
  }

  //dialog显示倒计时
  void _dismissDialogCountDown() {
    const oneSec = Duration(seconds: 1);
    int count = 3;
    dialogCountDownTimer = Timer.periodic(oneSec, (timer) {
      count--;
      var leftTime = count;
      if (leftTime <= 0) {
        timer.cancel();
        setState(() {
          dialogSurplusTimeStr = 0;
        });
        _dismissDialog();
      } else {
        setState(() {
          dialogSurplusTimeStr = count;
        });
      }
    });
  }

  //启动计时器，实时倒计时剩余优惠时间
  void _startTimer() {
    const oneSec = Duration(seconds: 1);
    countDownTimer = Timer.periodic(oneSec, (timer) {
      // var now = DateTime.now().millisecondsSinceEpoch;
      // var endTime = widget.surplusTime;
      widget.surplusTime--;
      var leftTime = widget.surplusTime;
      if (leftTime <= 0) {
        timer.cancel();
        setState(() {
          surplusTimeStr = "00:00:00";
        });
      } else {
        // var h = leftTime ~/ 3600000;
        // var m = (leftTime - h * 3600000) ~/ 60000;
        // var s = (leftTime - h * 3600000 - m * 60000) ~/ 1000;
        setState(() {
          surplusTimeStr = DateUtilForDxjk.second2HMS(leftTime);
        });
      }
    });
  }

  //跳转或关闭dialog
  void _dismissDialog() async {
    if (userRepository.vipProductMap.vip1 ||
        userRepository.vipProductMap.vip4) {
    } else {
      String cityCode = "";
      String cityName = "";
      MainController mainController = Get.find<MainController>();
      UserAccount? userInfo = await mainController.userRepository.userAccountDM;
      if (userInfo?.isBind == 1 && userInfo?.reqisterDivision != 0) {
        cityCode = userInfo?.reqisterDivision.toString() ?? "440100";
        cityName = cityMap[cityCode.toString().substring(0, 4)] ?? "广州市";
      } else {
        cityCode = mainController.homeRepository.cityInfo.cityCode;
        cityName = mainController.homeRepository.cityInfo.cityName;
      }
      String shopPath =
          '/pages/vip/part1/part1?type=jingxuan600ti&goods=vip1vip4&city=$cityCode&cityname=$cityName';
      JumpSmallProgramUtils.jump(shopPath, 'fc2259710004813765');
    }
    if (countDownTimer != null) {
      countDownTimer.cancel();
      countDownTimer = null;
    }
    if (dialogCountDownTimer != null) {
      dialogCountDownTimer.cancel();
      dialogCountDownTimer = null;
    }
    Navigator.of(context).pop();
  }

  @override
  void dispose() {
    if (countDownTimer != null) {
      countDownTimer.cancel();
      countDownTimer = null;
    }
    if (dialogCountDownTimer != null) {
      dialogCountDownTimer.cancel();
      dialogCountDownTimer = null;
    }
    debugPrint("销毁计时器");
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      surfaceTintColor: Colors.transparent,
      backgroundColor: Colors.transparent, // 背景透明
      child: Container(
        height: 373.h,
        child: Stack(
          alignment: Alignment.center,
          children: [
            assImg2(img: 'bg_tanchuan', w: 373.w, h: 373.h),
            Positioned(
                left: 70.5.w,
                top: 38.h,
                child: Container(
                  height: 18.5.h,
                  width: 80.w,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(11.5.w),
                  ),
                  child: Center(child: app15spE12020Text(surplusTimeStr)),
                )),
            Positioned(
                left: 7.5.w,
                top: 38.h,
                child: Center(
                    child: app14sp268Text('活动限时',
                        color: AppColors.white,
                        fontFamily: 'PingFangSC-Semibold',
                        wordSpacing: 1.0.w))),
            Positioned(
                right: 0,
                bottom: 33.h,
                left: 0,
                child: GestureDetector(
                  onTap: () {
                    _dismissDialog();
                  },
                  child: assImg2(img: 'btn', w: 219.w, h: 64.h),
                )
                // IconButton(
                //   icon: Icon(
                //     Icons.close,
                //     size: 30.w,
                //     color: Colors.white,
                //   ),
                //   onPressed: () {
                //     Navigator.of(context).pop();
                //   },
                // ),
                ),
            Positioned(
                right: 0,
                bottom: 0.h,
                left: 0,
                child: Center(
                  child: Container(
                    height: 33.h,
                    width: 33.w,
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.AAAAAA, width: 2.0),
                      borderRadius: BorderRadius.circular(16.5.w),
                    ),
                    child: Center(
                        child: app16spAAAText('${dialogSurplusTimeStr}S')),
                  ),
                )),
          ],
        ),
      ),
    );
  }
}
