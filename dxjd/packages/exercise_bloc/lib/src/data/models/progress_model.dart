import '../../domain/entities/exercise_progress.dart';

/// Data model for exercise progress
class ProgressModel {
  const ProgressModel({
    required this.subject,
    required this.type,
    this.userId,
    this.sortId,
    required this.currentIndex,
    required this.totalQuestions,
    required this.answeredQuestions,
    required this.correctCount,
    required this.wrongCount,
    required this.timeSpent,
    required this.score,
    required this.isCompleted,
    this.startTime,
    this.endTime,
    required this.lastUpdated,
  });

  final int subject;
  final String type;
  final String? userId;
  final int? sortId;
  final int currentIndex;
  final int totalQuestions;
  final int answeredQuestions;
  final int correctCount;
  final int wrongCount;
  final Duration timeSpent;
  final int score;
  final bool isCompleted;
  final DateTime? startTime;
  final DateTime? endTime;
  final DateTime lastUpdated;

  /// Convert from domain entity
  factory ProgressModel.fromEntity(ExerciseProgress entity) {
    return ProgressModel(
      subject: entity.subject,
      type: entity.type,
      userId: entity.userId,
      sortId: entity.sortId,
      currentIndex: entity.currentIndex,
      totalQuestions: entity.totalQuestions,
      answeredQuestions: entity.answeredQuestions,
      correctCount: entity.correctCount,
      wrongCount: entity.wrongCount,
      timeSpent: entity.timeSpent,
      score: entity.score,
      isCompleted: entity.isCompleted,
      startTime: entity.startTime,
      endTime: entity.endTime,
      lastUpdated: entity.lastUpdated,
    );
  }

  /// Convert to domain entity
  ExerciseProgress toEntity() {
    return ExerciseProgress(
      subject: subject,
      type: type,
      userId: userId,
      sortId: sortId,
      currentIndex: currentIndex,
      totalQuestions: totalQuestions,
      answeredQuestions: answeredQuestions,
      correctCount: correctCount,
      wrongCount: wrongCount,
      timeSpent: timeSpent,
      score: score,
      isCompleted: isCompleted,
      startTime: startTime,
      endTime: endTime,
      lastUpdated: lastUpdated,
    );
  }

  /// JSON serialization
  factory ProgressModel.fromJson(Map<String, dynamic> json) {
    return ProgressModel(
      subject: json['subject'] as int,
      type: json['type'] as String,
      userId: json['user_id'] as String?,
      sortId: json['sort_id'] as int?,
      currentIndex: json['current_index'] as int? ?? 0,
      totalQuestions: json['total_questions'] as int? ?? 0,
      answeredQuestions: json['answered_questions'] as int? ?? 0,
      correctCount: json['correct_count'] as int? ?? 0,
      wrongCount: json['wrong_count'] as int? ?? 0,
      timeSpent: Duration(
        milliseconds: json['time_spent'] as int? ?? 0,
      ),
      score: json['score'] as int? ?? 0,
      isCompleted: json['is_completed'] as bool? ?? false,
      startTime: json['start_time'] != null
          ? DateTime.parse(json['start_time'] as String)
          : null,
      endTime: json['end_time'] != null
          ? DateTime.parse(json['end_time'] as String)
          : null,
      lastUpdated: json['last_updated'] != null
          ? DateTime.parse(json['last_updated'] as String)
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'subject': subject,
      'type': type,
      'user_id': userId,
      'sort_id': sortId,
      'current_index': currentIndex,
      'total_questions': totalQuestions,
      'answered_questions': answeredQuestions,
      'correct_count': correctCount,
      'wrong_count': wrongCount,
      'time_spent': timeSpent.inMilliseconds,
      'score': score,
      'is_completed': isCompleted,
      'start_time': startTime?.toIso8601String(),
      'end_time': endTime?.toIso8601String(),
      'last_updated': lastUpdated.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  ProgressModel copyWith({
    int? subject,
    String? type,
    String? userId,
    int? sortId,
    int? currentIndex,
    int? totalQuestions,
    int? answeredQuestions,
    int? correctCount,
    int? wrongCount,
    Duration? timeSpent,
    int? score,
    bool? isCompleted,
    DateTime? startTime,
    DateTime? endTime,
    DateTime? lastUpdated,
  }) {
    return ProgressModel(
      subject: subject ?? this.subject,
      type: type ?? this.type,
      userId: userId ?? this.userId,
      sortId: sortId ?? this.sortId,
      currentIndex: currentIndex ?? this.currentIndex,
      totalQuestions: totalQuestions ?? this.totalQuestions,
      answeredQuestions: answeredQuestions ?? this.answeredQuestions,
      correctCount: correctCount ?? this.correctCount,
      wrongCount: wrongCount ?? this.wrongCount,
      timeSpent: timeSpent ?? this.timeSpent,
      score: score ?? this.score,
      isCompleted: isCompleted ?? this.isCompleted,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// Calculate completion percentage
  double get completionPercentage {
    if (totalQuestions == 0) return 0.0;
    return (answeredQuestions / totalQuestions) * 100.0;
  }

  /// Calculate accuracy percentage
  double get accuracyPercentage {
    if (answeredQuestions == 0) return 0.0;
    return (correctCount / answeredQuestions) * 100.0;
  }

  /// Get time spent in minutes
  double get timeSpentMinutes {
    return timeSpent.inMilliseconds / 60000.0;
  }

  /// Check if progress is valid
  bool get isValid {
    return subject > 0 &&
        type.isNotEmpty &&
        currentIndex >= 0 &&
        totalQuestions >= 0 &&
        answeredQuestions >= 0 &&
        correctCount >= 0 &&
        wrongCount >= 0 &&
        score >= 0;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is ProgressModel &&
        other.subject == subject &&
        other.type == type &&
        other.userId == userId &&
        other.sortId == sortId &&
        other.currentIndex == currentIndex &&
        other.totalQuestions == totalQuestions &&
        other.answeredQuestions == answeredQuestions &&
        other.correctCount == correctCount &&
        other.wrongCount == wrongCount &&
        other.timeSpent == timeSpent &&
        other.score == score &&
        other.isCompleted == isCompleted &&
        other.startTime == startTime &&
        other.endTime == endTime &&
        other.lastUpdated == lastUpdated;
  }

  @override
  int get hashCode {
    return Object.hash(
      subject,
      type,
      userId,
      sortId,
      currentIndex,
      totalQuestions,
      answeredQuestions,
      correctCount,
      wrongCount,
      timeSpent,
      score,
      isCompleted,
      startTime,
      endTime,
      lastUpdated,
    );
  }

  @override
  String toString() {
    return 'ProgressModel('
        'subject: $subject, '
        'type: $type, '
        'userId: $userId, '
        'sortId: $sortId, '
        'currentIndex: $currentIndex, '
        'totalQuestions: $totalQuestions, '
        'answeredQuestions: $answeredQuestions, '
        'correctCount: $correctCount, '
        'wrongCount: $wrongCount, '
        'timeSpent: $timeSpent, '
        'score: $score, '
        'isCompleted: $isCompleted, '
        'startTime: $startTime, '
        'endTime: $endTime, '
        'lastUpdated: $lastUpdated'
        ')';
  }
}
