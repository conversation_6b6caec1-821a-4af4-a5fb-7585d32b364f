import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:exercise_bloc/exercise_bloc.dart';

/// 练习演示页面 - 展示完整的做题流程
class ExerciseDemoPage extends StatefulWidget {
  const ExerciseDemoPage({super.key});

  @override
  State<ExerciseDemoPage> createState() => _ExerciseDemoPageState();
}

class _ExerciseDemoPageState extends State<ExerciseDemoPage> {
  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<ExerciseBloc>(
          create: (context) => getIt<ExerciseBloc>()
            ..add(const LoadQuestions(
              subject: 1,
              type: 'chapter',
              sortId: 1,
            )),
        ),
        BlocProvider<ListenBloc>(
          create: (context) => getIt<ListenBloc>(),
        ),
        BlocProvider<BrowseBloc>(
          create: (context) => getIt<BrowseBloc>(),
        ),
        BlocProvider<VideoBloc>(
          create: (context) => getIt<VideoBloc>(),
        ),
        BlocProvider<ModeSwitchBloc>(
          create: (context) => getIt<ModeSwitchBloc>(),
        ),
      ],
      child: Scaffold(
        backgroundColor: Colors.white,
        body: SafeArea(
          child: Column(
            children: [
              // 自定义AppBar
              _buildCustomAppBar(),

              // 主要内容区域
              Expanded(
                child: BlocBuilder<ModeSwitchBloc, ModeSwitchState>(
                  builder: (context, state) {
                    return Column(
                      children: [
                        // 模式切换TabBar
                        _buildModeTabBar(state),

                        // 内容区域
                        Expanded(
                          child: IndexedStack(
                            index: state.currentMode.index,
                            children: const [
                              ExerciseView(subject: 1, type: 'chapter'),
                              ListenView(subject: 1, type: 'chapter'),
                              BrowseView(subject: 1, type: 'chapter'),
                              VideoView(subject: 1, type: 'chapter'),
                            ],
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCustomAppBar() {
    return Container(
      height: 48.0 + MediaQuery.of(context).padding.top,
      padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Color(0x1A000000),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // 返回按钮
          Positioned(
            left: 0,
            top: 0,
            bottom: 0,
            child: IconButton(
              onPressed: () => Navigator.pop(context),
              icon: const Icon(Icons.arrow_back_ios, size: 20),
              color: const Color(0xFF333333),
            ),
          ),

          // 标题
          const Center(
            child: Text(
              '顺序练习演示',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF333333),
              ),
            ),
          ),

          // 功能按钮
          Positioned(
            right: 0,
            top: 0,
            bottom: 0,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  onPressed: () => _showFontSizeDialog(),
                  icon: const Icon(Icons.text_fields, size: 20),
                  color: const Color(0xFF666666),
                ),
                IconButton(
                  onPressed: () => _showQuestionListDialog(),
                  icon: const Icon(Icons.list, size: 20),
                  color: const Color(0xFF666666),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModeTabBar(ModeSwitchState state) {
    const modes = ['做题', '听题', '背题', '视频'];

    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          // TabBar容器
          Container(
            width: 204.0,
            height: 32.0,
            decoration: BoxDecoration(
              color: const Color(0x991992EF),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: modes.asMap().entries.map((entry) {
                final index = entry.key;
                final title = entry.value;
                final isSelected = index == state.currentMode.index;

                return Expanded(
                  child: GestureDetector(
                    onTap: () =>
                        _switchMode(context, ExerciseMode.values[index]),
                    child: Container(
                      height: 32.0,
                      decoration: isSelected
                          ? const BoxDecoration(
                              color: Color(0xFF1992EF),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(8)),
                            )
                          : null,
                      alignment: Alignment.center,
                      child: Text(
                        title,
                        style: isSelected
                            ? const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              )
                            : const TextStyle(
                                color: Color(0xFF666666),
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),

          const SizedBox(height: 16),

          // 进度信息
          BlocBuilder<ExerciseBloc, ExerciseState>(
            builder: (context, exerciseState) {
              if (exerciseState is ExerciseLoaded) {
                return Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF5F7FA),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildProgressItem(
                          '题目', '1/${exerciseState.questions.length}'),
                      _buildProgressItem(
                          '正确', '${exerciseState.progress.correctCount}'),
                      _buildProgressItem(
                          '错误', '${exerciseState.progress.wrongCount}'),
                      _buildProgressItem('进度',
                          '${(1 / exerciseState.questions.length * 100).toInt()}%'),
                    ],
                  ),
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildProgressItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF1992EF),
          ),
        ),
        const SizedBox(height: 2),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Color(0xFF666666),
          ),
        ),
      ],
    );
  }

  void _switchMode(BuildContext context, ExerciseMode mode) {
    switch (mode) {
      case ExerciseMode.exercise:
        context.read<ModeSwitchBloc>().add(const SwitchToExercise());
        break;
      case ExerciseMode.listen:
        context.read<ModeSwitchBloc>().add(const SwitchToListen());
        break;
      case ExerciseMode.browse:
        context.read<ModeSwitchBloc>().add(const SwitchToBrowse());
        break;
      case ExerciseMode.video:
        context.read<ModeSwitchBloc>().add(const SwitchToVideo());
        break;
    }
  }

  void _showFontSizeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('字体大小'),
        content: const Text('字体大小调节功能'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showQuestionListDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('题目列表'),
        content: const Text('题目导航功能'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
