import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:exercise_bloc/exercise_bloc.dart';
import 'package:quiz/quiz.dart';

import 'pages/home_page.dart';
import 'pages/real_exercise_page.dart';
import 'pages/real_mock_exam_page.dart';
import 'injection/real_injection.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化数据库
  await DBManager.get().init();

  // 配置真实数据源的依赖注入
  configureRealDependencies();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Exercise BLoC Example',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      initialRoute: '/',
      routes: {
        '/': (context) => const HomePage(),
        '/exercise': (context) => const RealExercisePage(),
        '/mock_exam': (context) => const RealMockExamPage(),
      },
    );
  }
}
