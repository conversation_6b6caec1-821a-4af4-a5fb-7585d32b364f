import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/entities/question_entity.dart';
import '../bloc/listen/listen_bloc.dart';
import '../bloc/listen/listen_event.dart';
import '../bloc/listen/listen_state.dart';
import 'common/exercise_styles.dart';
import 'common/option_widget.dart';

/// Listen view widget for audio-based question practice
/// Matches the original Listen implementation
class ListenView extends StatefulWidget {
  const ListenView({
    super.key,
    required this.subject,
    required this.type,
    this.sortId,
    this.mainId,
    this.childId,
    this.luid,
    this.ids,
  });

  final int subject;
  final String type;
  final int? sortId;
  final int? mainId;
  final int? childId;
  final int? luid;
  final List<dynamic>? ids;

  @override
  State<ListenView> createState() => _ListenViewState();
}

class _ListenViewState extends State<ListenView> {
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();

    // Load questions when widget initializes
    context.read<ListenBloc>().add(LoadListenQuestions(
          subject: widget.subject,
          type: widget.type,
          sortId: widget.sortId,
          mainId: widget.mainId,
          childId: widget.childId,
          luid: widget.luid,
          ids: widget.ids?.cast<int>(),
        ));
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ListenBloc, ListenState>(
      listener: (context, state) {
        if (state is ListenLoaded) {
          // Handle navigation to specific question
          if (state.currentIndex != _pageController.page?.round()) {
            _pageController.animateToPage(
              state.currentIndex,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          }
        }
      },
      builder: (context, state) {
        if (state is ListenLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (state is ListenError) {
          return _buildErrorWidget(state.message);
        }

        if (state is ListenLoaded) {
          return _buildListenContent(state);
        }

        return const Center(
          child: Text('No questions available'),
        );
      },
    );
  }

  Widget _buildErrorWidget(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: ExerciseStyles.dp(64),
            color: ExerciseStyles.red,
          ),
          SizedBox(height: ExerciseStyles.dp(16)),
          Text(
            'Error: $message',
            style: ExerciseStyles.styleText1H16W500,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: ExerciseStyles.dp(16)),
          ElevatedButton(
            onPressed: () {
              context.read<ListenBloc>().add(LoadListenQuestions(
                    subject: widget.subject,
                    type: widget.type,
                    sortId: widget.sortId,
                    mainId: widget.mainId,
                    childId: widget.childId,
                    luid: widget.luid,
                    ids: widget.ids?.cast<int>(),
                  ));
            },
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  Widget _buildListenContent(ListenLoaded state) {
    return PageView.builder(
      controller: _pageController,
      onPageChanged: (index) {
        context.read<ListenBloc>().add(JumpToListenQuestion(index));
      },
      itemCount: state.questions.length,
      itemBuilder: (context, index) {
        final question = state.questions[index];
        return _buildQuestionPage(question, state);
      },
    );
  }

  Widget _buildQuestionPage(QuestionEntity question, ListenLoaded state) {
    return Container(
      key: ValueKey(question.id),
      color: Colors.white,
      child: SingleChildScrollView(
        child: Column(
          children: [
            // Audio control section
            Container(
              padding: EdgeInsets.all(ExerciseStyles.dp(16)),
              child: Column(
                children: [
                  // Audio title
                  Container(
                    alignment: Alignment.centerLeft,
                    child: Row(
                      children: [
                        Container(
                          margin: EdgeInsets.only(top: ExerciseStyles.dp(2)),
                          child: Icon(
                            Icons.headphones,
                            size: ExerciseStyles.dp(20),
                            color: ExerciseStyles.blue,
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(left: ExerciseStyles.dp(6)),
                          alignment: Alignment.centerLeft,
                          child: Stack(
                            children: [
                              Positioned(
                                bottom: ExerciseStyles.dp(2),
                                child: Container(
                                  width: ExerciseStyles.dp(68),
                                  height: ExerciseStyles.dp(6),
                                  decoration: ExerciseStyles.styleBoxYellow2R50,
                                ),
                              ),
                              Text(
                                '听题练习',
                                style: ExerciseStyles.styleText1H16W500,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: ExerciseStyles.dp(16)),

                  // Audio controls
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Play/Pause button
                      GestureDetector(
                        onTap: () => _toggleAudio(question, state),
                        child: Container(
                          width: ExerciseStyles.dp(64),
                          height: ExerciseStyles.dp(64),
                          decoration: BoxDecoration(
                            color: ExerciseStyles.blue,
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.2),
                                offset: const Offset(0, 2),
                                blurRadius: 4,
                              ),
                            ],
                          ),
                          child: Icon(
                            question.isPlaying ? Icons.pause : Icons.play_arrow,
                            size: ExerciseStyles.dp(32),
                            color: Colors.white,
                          ),
                        ),
                      ),

                      SizedBox(width: ExerciseStyles.dp(24)),

                      // Stop button
                      GestureDetector(
                        onTap: () => _stopAudio(question),
                        child: Container(
                          width: ExerciseStyles.dp(48),
                          height: ExerciseStyles.dp(48),
                          decoration: BoxDecoration(
                            color: ExerciseStyles.text3,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.stop,
                            size: ExerciseStyles.dp(24),
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: ExerciseStyles.dp(16)),

                  // Play count info
                  Text(
                    '播放次数: ${question.playCount}',
                    style: ExerciseStyles.styleText2H14W500,
                  ),
                ],
              ),
            ),

            // Question title (simplified for listen mode)
            Container(
              padding: EdgeInsets.only(
                top: ExerciseStyles.dp(16),
                left: ExerciseStyles.dp(16),
                right: ExerciseStyles.dp(16),
              ),
              alignment: AlignmentDirectional.centerStart,
              child: Text(
                '请仔细听题，然后选择正确答案',
                style: ExerciseStyles.styleText1H18W500,
              ),
            ),

            SizedBox(height: ExerciseStyles.dp(20)),

            // Options (simplified display for listen mode)
            if (question.options.isNotEmpty) ...[
              Container(
                padding:
                    EdgeInsets.symmetric(horizontal: ExerciseStyles.dp(16)),
                alignment: Alignment.centerLeft,
                child: Text(
                  '选项:',
                  style: ExerciseStyles.styleText1H16W500,
                ),
              ),
              SizedBox(height: ExerciseStyles.dp(12)),
              for (final option in question.options)
                if (option.text.isNotEmpty)
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: ExerciseStyles.dp(16),
                      vertical: ExerciseStyles.dp(4),
                    ),
                    child: ListenOptionWidget(
                      option: option.key,
                      text: option.text,
                    ),
                  ),
            ],

            SizedBox(height: ExerciseStyles.dp(40)),

            // Show answer button (after listening)
            if (question.playCount > 0)
              Container(
                margin: EdgeInsets.symmetric(horizontal: ExerciseStyles.dp(24)),
                child: GestureDetector(
                  onTap: () => _showAnswer(question),
                  child: Container(
                    alignment: AlignmentDirectional.center,
                    height: ExerciseStyles.dp(46),
                    decoration: ExerciseStyles.styleBtnBlue,
                    child: Text(
                      '查看答案',
                      style: ExerciseStyles.styleWhite16W500,
                    ),
                  ),
                ),
              ),

            // Answer display (if shown)
            if (question.isAnswered) ...[
              SizedBox(height: ExerciseStyles.dp(20)),
              Container(
                margin: EdgeInsets.all(ExerciseStyles.dp(16)),
                padding: EdgeInsets.all(ExerciseStyles.dp(16)),
                decoration: ExerciseStyles.styleBoxWhiteR4,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.check_circle,
                          size: ExerciseStyles.dp(20),
                          color: ExerciseStyles.green1,
                        ),
                        SizedBox(width: ExerciseStyles.dp(8)),
                        Text(
                          '正确答案',
                          style: ExerciseStyles.styleText1H16W500.copyWith(
                            color: ExerciseStyles.green1,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: ExerciseStyles.dp(12)),
                    Text(
                      '答案: ${question.correctAnswer.toUpperCase()}',
                      style: ExerciseStyles.styleText1H16W500,
                    ),
                    if (question.explanation != null &&
                        question.explanation!.isNotEmpty) ...[
                      SizedBox(height: ExerciseStyles.dp(12)),
                      Text(
                        '解析: ${question.explanation}',
                        style: ExerciseStyles.styleText1H16W500,
                      ),
                    ],
                  ],
                ),
              ),
            ],

            SizedBox(height: ExerciseStyles.dp(100)), // Bottom padding
          ],
        ),
      ),
    );
  }

  void _toggleAudio(QuestionEntity question, ListenLoaded state) {
    if (question.isPlaying) {
      context.read<ListenBloc>().add(PauseAudio());
    } else {
      final questionIndex = state.questions.indexOf(question);
      context
          .read<ListenBloc>()
          .add(PlayQuestionAudio(questionIndex: questionIndex));
    }
  }

  void _stopAudio(QuestionEntity question) {
    context.read<ListenBloc>().add(StopAudio());
  }

  void _showAnswer(QuestionEntity question) {
    // For now, just show a dialog with the answer
    // In the original implementation, this would update the question state
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('正确答案'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('答案: ${question.correctAnswer.toUpperCase()}'),
            if (question.explanation != null && question.explanation!.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text('解析: ${question.explanation}'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
