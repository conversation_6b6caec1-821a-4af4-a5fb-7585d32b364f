import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:component_library/component_library.dart';
import 'package:domain_models/domain_models.dart';
import 'package:dxjd/app_routes.dart';
import 'package:dxjd/mainController.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:easy_rich_text/easy_rich_text.dart';
import 'package:flukit/flukit.dart';
import 'package:flukit/flukit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:home/home.dart';
import 'package:home/src/home/<USER>/section_four_controller.dart';
import 'package:home/src/home/<USER>/sub_one_item_page/get_more_home_page.dart';
import 'package:home/src/home/<USER>/sub_one_item_page/sub_four_before_exam_page.dart';
import 'package:home/src/home/<USER>/sub_one_item_page/sub_four_before_rule_page.dart';
import 'package:home_repository/home_repository.dart';
import 'package:login/login.dart';
import 'package:quiz/quiz.dart';
import 'package:timing_repository/timing_repository.dart';
import 'package:timing/timing.dart';
import 'package:tools/tools.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:user_repository/user_repository.dart';

import '../custom_widget/common_view.dart';
import '../custom_widget/grallist.dart';
import 'package:api/api.dart';

/**
 * 科目四page
 */
class SectionFourPage extends StatelessWidget {
  SectionFourPage(
      {super.key,
      required this.gotoPractitionerVideo,
      required this.timingRepository,
      required this.userAccount,});

  UserAccount? userAccount;
  final VoidCallback gotoPractitionerVideo;
  final TimingRepository timingRepository;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SectionFourController>(
      init: SectionFourController(),
      tag: ExaminationPageState.key.toString(),
      builder: (logic) {
        return Container(
          // color: Color(0xffF5F7F9),
          decoration: BoxDecoration(
              gradient: LinearGradient(
                  colors: [Color(0xffFFFFFF), Color(0xffF5F7F9)],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter)),
          child: Column(children: [
            Expanded(
              // backgroundColor: AppColors.F9F8F8,
              child: EasyRefresh.builder(
                scrollController: logic.refreshScrollController,
                header: const CupertinoHeader(triggerOffset: 20.0),
                onRefresh: () {
                  logic.onRefresh();
                },
                childBuilder: (context, physics) {
                  return CustomScrollView(
                      physics: physics,
                      controller: logic.refreshScrollController,
                      slivers: [
                        sliverPaddingToBoxAdapter(
                          padding: EdgeInsets.only(top: 0.h, bottom: 11.h),
                          child: GalleryList(
                              // bannerListDm: logic.bannerList,
                              bannerListDm: logic.bannerList,
                              autoPlay: logic.bannerIsStartScroll,
                              carouselController: logic.carouselController,
                              subject: 4,
                              timingRepository: timingRepository,
                              isTabHome: true),
                        ),
                        _buildTopic(logic), //专题

                        // sliverPaddingToBoxAdapter(
                        //     padding: EdgeInsets.only(top: 15.h, bottom: 11.h),
                        //     child: SizedBox(
                        //       height: 180.h,
                        //       child: Row(
                        //         crossAxisAlignment: CrossAxisAlignment.center,
                        //         children: [
                        //           Expanded(
                        //               child: Column(
                        //             children: [
                        //               _buildSequentialExercises(context),
                        //               SizedBox(height: 8.h),
                        //               _buildMockExam(context)
                        //             ],
                        //           )),
                        //           Expanded(child: _buildVIPSubject(context)),
                        //         ],
                        //       ),
                        //     )),
                        // _buildExamTitle(),
                        // _buildStudyHouse(),
                        // _buildIndicator(),
                        _buildScrollTip(logic),
                        _buildStudyHouse(4, timingRepository,logic),
                        userAccount == null
                            ? const SliverToBoxAdapter(child: SizedBox())
                            : (userAccount?.topicType == 'A2' ||
                                    userAccount?.topicType == 'B2' ||
                                    userAccount?.topicType == 'C6'
                                ? _buildQualificationTraining(gotoPractitionerVideo)
                                : const SliverToBoxAdapter(child: SizedBox())),
                        _buildNewMyAchievement(logic), //我的成绩单
                        _buildLiveing(logic),
                        // _buildLiveList(zhiboimg),
                        _bringViewExam(logic),
                        _buildInformation(logic)
                      ]);
                },
              ),
            )
          ]),
        );
      }
    );
  }

  Widget _bringViewExam(SectionFourController logic) {
    BringViewListElement? item = logic.bringVIewExaminationModel?.list?[0];
    return SliverToBoxAdapter(
      child: logic.bringVIewExaminationModel == null
          ? const SizedBox()
          : Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10.r),
              ),
              margin: REdgeInsets.symmetric(horizontal: 11, vertical: 11),
              padding: REdgeInsets.only(top: 11, bottom: 11, left: 11),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(item?.title ?? '',
                          style: TextStyle(
                              fontSize: 16.sp,
                              fontFamily: 'PingFangSC-Bold',
                              fontWeight: FontWeight.bold,
                              color: AppColors.privacy_dialog_titile_color)),
                      SizedBox(width: 6.w),
                      const Image(
                        image: AssetImage(
                            "assets/home_img/icon_location_blue.png"),
                        width: 20,
                        height: 20,
                        fit: BoxFit.fill,
                      ),
                      SizedBox(width: 5.w),
                      Text(
                          '${cityMap[item?.city.toString().substring(0, 4)] ?? '未知城市'}',
                          style: TextStyle(
                              fontSize: 14.sp,
                              fontFamily: 'PingFangSC-Medium',
                              fontWeight: FontWeight.w500,
                              color: const Color(0xFF268FF7))),
                      const Spacer(),
                      InkWell(
                        onTap: () {
                          if (!MainController.isLoginIntercept()) {
                            return;
                          }
                          Get.toNamed(AppRoutes.subjectThreeCatalogPage,
                              arguments: {
                                'selectIndex': 0,
                                'isNeedLoadingData': true
                              });
                        },
                        child: Row(
                          children: [
                            Text("切换考场",
                                style: TextStyle(
                                    fontSize: 12.sp,
                                    fontFamily: 'PingFangSC-Medium',
                                    fontWeight: FontWeight.w500,
                                    color: const Color(0xFF8A8A8A))),
                            const Image(
                              image: AssetImage(
                                  "assets/home_img/icon_grey_left.png"),
                              width: 20,
                              height: 20,
                              fit: BoxFit.fill,
                            ),
                            const SizedBox(
                              height: 8,
                            )
                          ],
                        ),
                      )
                    ],
                  ),
                  SizedBox(height: 12.h),
                  Text("根据您的位置和考场通过率推荐",
                      style: TextStyle(
                          fontSize: 12.sp,
                          fontFamily: 'PingFangSC-Medium',
                          fontWeight: FontWeight.w500,
                          color: const Color(0xFF8A8A8A))),
                  SizedBox(height: 14.h),
                  SizedBox(
                    width: double.infinity,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Wrap(
                            spacing: 13.w,
                            children: List.generate(item?.label?.length ?? 0,
                                (sIndex) {
                              return Container(
                                padding: REdgeInsets.symmetric(
                                    vertical: 3, horizontal: 7),
                                decoration: BoxDecoration(
                                    border: Border.all(
                                        color: const Color(0xFF2BA9F4),
                                        width: 1),
                                    borderRadius: BorderRadius.circular(10.r)),
                                child: Text(item?.label?[sIndex] ?? "",
                                    style: TextStyle(
                                        fontSize: 11.sp,
                                        fontFamily: 'PingFangSC-Regular',
                                        color: const Color(0xFF2BA9F4))),
                              );
                            })),
                        SizedBox(
                          height: 14.h,
                        ),
                        SizedBox(
                          height: 114.h,
                          child: ListView.builder(
                              itemCount: item?.examCatalogInfo?.length ?? 0,
                              scrollDirection: Axis.horizontal,
                              itemBuilder: (context, tIndex) {
                                return Container(
                                  margin: REdgeInsets.only(right: 12),
                                  width: 130.w,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      InkWell(
                                        onTap: () {
                                          logic.goViewExaminationPage(
                                              item?.productId, item, tIndex);
                                        },
                                        child: Container(
                                          height: 80.h,
                                          alignment: Alignment.center,
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(10.r),
                                            image: DecorationImage(
                                                image: NetworkImage(item
                                                        ?.examCatalogInfo?[
                                                            tIndex]
                                                        .cover ??
                                                    ""),
                                                fit: BoxFit.cover),
                                          ),
                                          child: Image.asset(
                                            'assets/home_img/grey_bofang.png',
                                            width: 30.w,
                                            height: 30.h,
                                          ),
                                        ),
                                      ),
                                      SizedBox(height: 12.h),
                                      Text(
                                          item?.examCatalogInfo?[tIndex]
                                                  .videoName ??
                                              "",
                                          style: TextStyle(
                                              fontSize: 14.sp,
                                              fontFamily: 'PingFangSC-Medium',
                                              fontWeight: FontWeight.w500,
                                              overflow: TextOverflow.ellipsis,
                                              color: const Color(0xFF000000)))
                                    ],
                                  ),
                                );
                              }),
                        )
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 14.h,
                  ),
                  InkWell(
                      onTap: () {
                        if (!MainController.isLoginIntercept()) {
                          return;
                        }
                        JumpUtils.get().jumpRealHome(4);
                      },
                      child: assImg2(img: "real_exam_mock")),
                ],
              ),
            ),
    );
  }

  //新版-科四-我的成绩单
  Widget _buildNewMyAchievement(SectionFourController logic) {
    return sliverPaddingToBoxAdapter(
        padding: REdgeInsets.only(top: 6.h, left: 11.w, right: 11.w),
        child: Stack(
          children: [
            GestureDetector(
              onTap: () {
                const shopPath = '/pages/grade/index/index';
                JumpSmallProgramUtils.jump(shopPath, "fc2230308094514309");
              },
              child: white8radiusContainer(
                  height: 148.h,
                  child: Column(
                    children: [
                      Row(
                        children: [
                          app16spA1Text('我的成绩单',
                              fontFamily: 'PingFangSC-Semibold'),
                          SizedBox(width: 4.w),
                          assImg2(img: 'new_icon_more', w: 16.w, h: 16.h)
                        ],
                      ).paddingOnly(bottom: 10.h, left: 12.w, top: 12.h),
                      Container(
                              height: 70.h,
                              decoration: BoxDecoration(
                                  image: DecorationImage(
                                image: assetImg2(img: "achievement_subject_bg"),
                              )),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                children: [
                                  _buildNewMyAchievementItem('科目一',
                                      logic.myAchievementData['Subject1'] ?? -1),
                                  Container(
                                    color: AppColors.E1E1E1,
                                    height: 15.h,
                                    width: 1.w,
                                  ),
                                  _buildNewMyAchievementItem('科目二',
                                      logic.myAchievementData['Subject2'] ?? -1),
                                  Container(
                                    color: AppColors.E1E1E1,
                                    height: 15.h,
                                    width: 1.w,
                                  ),
                                  _buildNewMyAchievementItem('科目三',
                                      logic.myAchievementData['Subject3'] ?? -1),
                                  Container(
                                    color: AppColors.E1E1E1,
                                    height: 15.h,
                                    width: 1.w,
                                  ),
                                  _buildNewMyAchievementItem('科目四',
                                      logic.myAchievementData['Subject4'] ?? -1),
                                ],
                              ).paddingSymmetric(horizontal: 6.h))
                          .paddingSymmetric(horizontal: 11.h),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildTipsItem('如何查成绩', onTap: () {
                            Tools.get().jump(const QueryAchievement());
                          }),
                          _buildTipsItem('如何约考', onTap: () {
                            Tools.get().jump(const HowToAppointmentPage());
                          }),
                          _buildTipsItem('考前规则', onTap: () {
                            Tools.get().jump(const SubjectFourBeforeRulePage());
                          }),
                          _buildTipsItem('考前注意', onTap: () {
                            Tools.get().jump(const SubjectFourBeforeExamPage());
                          }),
                        ],
                      ).paddingOnly(top: 10.h)
                    ],
                  )),
            ),
            //分享成绩单
            Positioned(
              top: 0.h,
              right: 0.w,
              child: GestureDetector(
                onTap: () {
                  if (!MainController.isLoginIntercept()) {
                    return;
                  }
                  if(logic.myAchievementData['Subject1'] == null && logic.myAchievementData['Subject2'] == null && logic.myAchievementData['Subject3'] == null && logic.myAchievementData['Subject4'] == null){
                    Toast.show("请填写成绩后再进行分享哦");
                    return;
                  }
                  timingRepository.shareGraded();
                }.throttleWithTimeout(timeout: 5000),
                child: assImg2(img: "share_achievement", w: 97.w, h: 25.h),
              ),
            ),
          ],
        ));
  }

  //新版-我的成绩单-科目item
  Widget _buildNewMyAchievementItem(String subject, int score) {
    return SizedBox(
      child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            app14spFFFFText(subject,
                color: const Color(0xff707070),
                fontFamily: 'PingFangSC-Semibold'),
            app14spFFFFText(score == -1 ? '— —' : '$score',
                color: score > 60
                    ? Colors.black
                    : score == -1
                        ? const Color(0xffCECECE)
                        : const Color(0xffCE2828),
                fontFamily: 'PingFangSC-Semibold')
          ]),
    );
  }

  //新版-我的成绩单-tips item
  Widget _buildTipsItem(String text, {onTap}) {
    return GestureDetector(
        onTap: onTap,
        child: Row(
          children: [
            assImg2(img: 'new_icon_tips', w: 14.w, h: 14.h),
            app12spAAAText(text,
                color: AppColors.B8B8B8, fontFamily: 'PingFangSC-Medium')
          ],
        ));
  }

  Widget _buildTopic(SectionFourController logic) {
    return sliverPaddingToBoxAdapter(
        padding: REdgeInsets.symmetric(horizontal: 33.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Column(
              children: [
                SizedBox(height: 5.h),
                meIconButton2(
                    icon: "icon_youjianrunan",
                    text: "由简入难",
                    color: const Color(0xff6B6B6B),
                    onTap: () {
                      if (!MainController.isLoginIntercept()) {
                        return;
                      }
                      BuryingPointUtils.instance.addPoint(
                          buryingPointList: BuryingPointList(
                              eventType: 3,
                              entranceType: 22,
                              action: 1,
                              browseDuration: 0));
                      // timingRepository.openTimingMiniProgram();
                      JumpUtils.get().jumpLadder(JkKey.SUBFOUR);
                    }),
                SizedBox(height: 79.h),
                meIconButton2(
                    icon: "icon_2s_ans",
                    text: "两秒答题",
                    color: const Color(0xff6B6B6B),
                    onTap: () {
                      if (!MainController.isLoginIntercept()) {
                        return;
                      }
                      BuryingPointUtils.instance.addPoint(
                          buryingPointList: BuryingPointList(
                              eventType: 3,
                              entranceType: 23,
                              action: 1,
                              browseDuration: 0));
                      JumpUtils.get().jumpTwoSecondAnswer(JkKey.SUBFOUR);
                    }),
                SizedBox(height: 79.h),
                meIconButton2(
                    icon: "icon_mijuan",
                    text: "考前密卷",
                    color: const Color(0xff6B6B6B),
                    onTap: () {
                      if (!MainController.isLoginIntercept()) {
                        return;
                      }
                      BuryingPointUtils.instance.addPoint(
                          buryingPointList: BuryingPointList(
                              eventType: 3,
                              entranceType: 24,
                              action: 1,
                              browseDuration: 0));
                      // timingRepository.openTimingMiniProgram();
                      JumpUtils.get().jumpEssence(JkKey.SUBFOUR);
                    }),
              ],
            ),
            Column(
              children: [
                GestureDetector(
                  onTap: () {
                    if (!MainController.isLoginIntercept()) {
                      return;
                    }
                    if (ITools.get().isVip(4)) {
                      BuryingPointUtils.instance.addPoint(
                          buryingPointList: BuryingPointList(
                              eventType: 3,
                              entranceType: 25,
                              action: 1,
                              browseDuration: 0));
                    } else {
                      BuryingPointUtils.instance.addPoint(
                          buryingPointList: BuryingPointList(
                              eventType: 1,
                              entranceType: 20,
                              action: 1,
                              browseDuration: 0));
                    }
                    JumpUtils.get()
                        .jumpPromote(4, vipType: 4, type: 'jingxuan600ti');
                  },
                  child: Container(
                      height: 140.h,
                      width: 140.w,
                      decoration: BoxDecoration(
                          image: DecorationImage(
                              image: assetImg2(img: "bg_home_vip"))),
                      child: Stack(
                        children: [
                          Positioned(
                            left: 37.w,
                            top: 26.h,
                            child:
                                assImg2(img: "icon_vvvvip", w: 31.w, h: 31.h),
                          ),
                          Positioned(
                            left: 68.w,
                            top: 35.h,
                            child: app16spFFFText("课程",
                                fontFamily: 'PingFangSC-Medium'),
                          ),
                          Positioned(
                            left: 22.w,
                            bottom: 57.h,
                            height: 24.w,
                            child: Center(
                              child: app14spFFFFText(
                                  '${(logic.vipModel?.helpCount?.customValue == 2 ? logic.vipModel?.helpCount?.actualValue : logic.vipModel?.helpCount?.customValue) ?? 0}人已报名',
                                  fontFamily: 'PingFangSC-Semibold'),
                            ),
                          ),
                          Positioned(
                            left: 0,
                            right: 0,
                            bottom: 23.h,
                            height: 24.w,
                            child: Center(
                              child: app17spFFFFText(
                                  '${(logic.vipModel?.qualificationRate?.customValue == 2 ? logic.vipModel?.qualificationRate?.actualValue : logic.vipModel?.qualificationRate?.customValue) ?? 0}%',
                                  color: AppColors.D76798,
                                  fontFamily: 'PingFangSC-Medium'),
                            ),
                          ),
                          Positioned(
                            right: 60.w,
                            bottom: 8.h,
                            height: 24.w,
                            child: Center(
                              child: app10spD76798Text('通过率',
                                  fontFamily: 'PingFangSC-Medium'),
                            ),
                          ),
                          Positioned(
                            right: 48.w,
                            bottom: 8.h,
                            height: 24.w,
                            child: Center(
                              child: InkWell(
                                child: assImg2(
                                    img: 'tips_wenhao_subone',
                                    w: 11.w,
                                    h: 11.h),
                                onTap: () {
                                  Toast.show(
                                      '“考试通过率”来自大象驾到后台统计数据。指科一四vip用户，获取到了vip补偿资格后，在正式考试中没有通过的，并且有在大象驾到APP上报他的“不通过成绩”。大象据此统计出vip课程“不通过率”，再据此得出vip课程“考试通过率”',
                                      duration: 2000);
                                },
                              ),
                            ),
                          ),
                        ],
                      )),
                ),
                SizedBox(height: 12.h),
                GestureDetector(
                  onTap: () {
                    BuryingPointUtils.instance.addPoint(
                        buryingPointList: BuryingPointList(
                            eventType: 3,
                            entranceType: 26,
                            action: 1,
                            browseDuration: 0));
                    JumpUtils.get().jumpChapter(JkKey.SUBFOUR);
                  },
                  child: Container(
                      height: 140.h,
                      width: 140.w,
                      decoration: BoxDecoration(
                          image: DecorationImage(
                              image: assetImg2(img: "bg_homg_shunxulianxi"))),
                      child: Stack(
                        children: [
                          Positioned(
                            top: 32.h,
                            right: 0,
                            left: 0,
                            height: 35.w,
                            child: Center(
                                child: app16spFFFText("顺序练习",
                                    fontFamily: 'PingFangSC-Medium')),
                          ),
                          Positioned(
                            left: 0.w,
                            right: 0,
                            bottom: 56.h,
                            height: 24.w,
                            child: Center(
                              child: app14spFFFFText('${logic.completed}/${logic.total}',
                                  fontFamily: 'PingFangSC-Semibold'),
                            ),
                          ),
                          Positioned(
                            left: 0,
                            right: 0,
                            bottom: 22.h,
                            height: 24.w,
                            child: Center(
                              child: app14spFFFFText('本地题库',
                                  color: const Color(0xff469BD6),
                                  fontFamily: 'PingFangSC-Medium'),
                            ),
                          ),
                          Center(
                            child: TurnBox(
                              turns: (2 / 1),
                              child: GradientCircularProgressIndicator(
                                colors: const [
                                  // AppColors.other_login_text,
                                  // AppColors.other_login_text,
                                  Color(0xFFFFFFFF),
                                  Color(0xFF2BA9F4),
                                  // Color(0xFF000000),
                                  // Color(0xFF000000),
                                ],
                                radius: 66.h,
                                stokeWidth: 5.0.w,
                                strokeCapRound: true,
                                backgroundColor: Colors.transparent,
                                totalAngle: 2 * pi,
                                value:
                                    logic.completed == 0 ? 0.01 : logic.completed / logic.total,
                                // value: 1,
                              ),
                            ),
                          ),
                        ],
                      )),
                ),
                SizedBox(height: 12.h),
                GestureDetector(
                  onTap: () {
                    if (!MainController.isLoginIntercept()) {
                      return;
                    }
                    BuryingPointUtils.instance.addPoint(
                        buryingPointList: BuryingPointList(
                            eventType: 3,
                            entranceType: 28,
                            action: 1,
                            browseDuration: 0));
                    JumpUtils.get().jumpMockHome(JkKey.SUBFOUR);
                  },
                  child: Container(
                      height: 140.h,
                      width: 140.w,
                      decoration: BoxDecoration(
                          image: DecorationImage(
                              image: assetImg2(img: "bg_homg_monikaoshi"))),
                      child: Stack(
                        children: [
                          Positioned(
                            top: 32.h,
                            right: 0,
                            left: 0,
                            height: 35.w,
                            child: Center(
                                child: app16spFFFText("模拟考试",
                                    fontFamily: 'PingFangSC-Medium')),
                          ),
                          Positioned(
                            left: 0.w,
                            right: 0,
                            bottom: 56.h,
                            height: 24.w,
                            child: Center(
                              child: app14spFFFFText('${logic.examAverage}分',
                                  fontFamily: 'PingFangSC-Semibold'),
                            ),
                          ),
                          Positioned(
                            left: 0,
                            right: 0,
                            bottom: 22.h,
                            height: 24.w,
                            child: Center(
                              child: app14spFFFFText('真实考场',
                                  color: const Color(0xff829BEE),
                                  fontFamily: 'PingFangSC-Medium'),
                            ),
                          ),
                        ],
                      )),
                )
              ],
            ),
            Column(
              children: [
                SizedBox(height: 5.h),
                meIconButton2(
                    icon: "icon_cuoti",
                    text: "错题收藏",
                    color: const Color(0xff6B6B6B),
                    onTap: () {
                      if (!MainController.isLoginIntercept()) {
                        return;
                      }
                      BuryingPointUtils.instance.addPoint(
                          buryingPointList: BuryingPointList(
                              eventType: 3,
                              entranceType: 29,
                              action: 1,
                              browseDuration: 0));
                      // timingRepository.openTimingMiniProgram();
                      JumpUtils.get().jumpFolder(JkKey.SUBFOUR, "error");
                    }),
                SizedBox(height: 79.h),
                meIconButton2(
                    icon: "icon_yicuoti",
                    text: "易错题",
                    color: const Color(0xff6B6B6B),
                    onTap: () {
                      if (!MainController.isLoginIntercept()) {
                        return;
                      }
                      BuryingPointUtils.instance.addPoint(
                          buryingPointList: BuryingPointList(
                              eventType: 3,
                              entranceType: 30,
                              action: 1,
                              browseDuration: 0));
                      // timingRepository.openTimingMiniProgram();
                      JumpUtils.get().jumpMixed(JkKey.SUBFOUR);
                    }),
                SizedBox(height: 79.h),
                // meIconButton2(
                //     icon: "icon_lilunshiping",
                //     text: "理论视频",
                //     color: const Color(0xff6B6B6B),
                //     onTap: () {
                //       if (!MainController.isLoginIntercept()) {
                //         return;
                //       }
                //       final isStrict = ITiming.get().openStrictTiming ?? false;
                //       if (isStrict) {
                //         gotoTheoryVideo.call();
                //       } else {
                //         JumpUtils.get().startTiming(true, JkKey.SUBFOUR, () async {
                //           gotoTheoryVideo.call();
                //         });
                //       }
                //     }),
                meIconButton2(
                    icon: "icon_more_home_item",
                    text: "更多",
                    color: const Color(0xff6B6B6B),
                    onTap: () {
                      BuryingPointUtils.instance.addPoint(
                          buryingPointList: BuryingPointList(
                              eventType: 3,
                              entranceType: 31,
                              action: 1,
                              browseDuration: 0));
                      Tools.get().jump(const GetMoreHomeItemPage(subject: 4));
                    }),
              ],
            ),
          ],
        ));
  }

  //顺序练习
  Widget _buildSequentialExercises(context,SectionFourController logic) {
    return Expanded(
      child: InkWell(
        overlayColor: MaterialStateProperty.all(Colors.transparent),
        child: Container(
            width: MediaQuery.of(context).size.width,
            decoration: BoxDecoration(
              image: DecorationImage(
                  fit: BoxFit.fill,
                  image: assetImg2(img: "bg_homg_shunxulianxi")),
              borderRadius: BorderRadius.all(Radius.circular(10.r)),
            ),
            child:
                app12spC66666Text("${logic.total}题", fontFamily: 'PingFangSC-Medium')
                    .paddingOnly(top: 37.h, left: 17.w)),
        onTap: () {
          // timingRepository.openTimingMiniProgram();
          JumpUtils.get().jumpChapter(JkKey.SUBFOUR);
        },
      ),
    );
  }

//模拟考试
  Widget _buildMockExam(context) {
    return Expanded(
        child: InkWell(
      overlayColor: MaterialStateProperty.all(Colors.transparent),
      child: Container(
          width: MediaQuery.of(context).size.width,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(10.r)),
            image: DecorationImage(
                fit: BoxFit.fill, image: assetImg2(img: "bg_homg_monikaoshi")),
          ),
          child: app13spC66666Text("100分", fontFamily: 'PingFangSC-Regular')
              .paddingOnly(top: 36.h, left: 15.w)),
      onTap: () {
        if (!MainController.isLoginIntercept()) {
          return;
        }
        // timingRepository.openTimingMiniProgram();
        JumpUtils.get().jumpMockHome(JkKey.SUBFOUR);
      },
    ));
  }

  //考题指示
  Widget _buildIndicator(SectionFourController logic) {
    return sliverPaddingToBoxAdapter(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            margin: EdgeInsets.only(top: 6.w),
            width: logic.page == 0 ? 25.w : 6.w,
            height: 4.w,
            decoration: Style.style_box_dark_r50,
          ),
          SizedBox(width: 6.w),
          Container(
            margin: EdgeInsets.only(top: 6.w),
            width: logic.page == 0 ? 6.w : 25.w,
            height: 4.w,
            decoration: Style.style_box_dark_r50,
          ),
        ],
      ),
    );
  }

  //学车资讯
  Widget _buildInformation(SectionFourController logic) {
    return sliverPaddingToBoxAdapter(
      padding: EdgeInsets.symmetric(horizontal: 11.h),
      child: white8radiusContainer(
        padding: EdgeInsets.all(11.h),
        // boxShadow: [
        //   BoxShadow(
        //       color: AppColors.C000000,
        //       offset: Offset(-1.0.h, 1.0.h), //阴影y轴偏移量
        //       blurRadius: 1.h, //阴影模糊程度
        //       spreadRadius: 1.r //阴影扩散程度
        //       )
        // ],
        width: 343.h,
        // height: 292.h,
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                app16spAC5000Text('学车资讯',
                    color: const Color(0xff000000),
                    fontFamily: 'PingFangSC-Semibold'),
                iconMore(onTap: () {
                  const infoPath = '/pages/operate/article/list/list';
                  timingRepository.openTimingMiniProgram(path: infoPath);
                  // Toast.show("学车资讯更多信息");
                  debugPrint("学车资讯更多信息");
                })
              ],
            ),
            ListView.separated(
              padding: EdgeInsets.zero,
              physics: const NeverScrollableScrollPhysics(),
              itemBuilder: (_, index) {
                return infoItem(logic.subjectFourArticleInfo!.articleListDM[index],
                    index, timingRepository);
              },
              itemCount: logic.subjectFourArticleInfo == null
                  ? 0
                  : logic.subjectFourArticleInfo!.articleListDM.length > 2
                      ? 2
                      : logic.subjectFourArticleInfo!.articleListDM.length,
              separatorBuilder: (BuildContext context, int index) {
                return const Divider(color: Color(0xFFF5F7F9),height: 1,);
              },
              shrinkWrap: true,
            )
          ],
        ),
      ).paddingOnly(bottom: 3.h),
    );
  }

//home页面资讯item
  Widget infoItem(ArticleInfoDataDmList item, int index,
      TimingRepository timingRepository) {
    return GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          BuryingPointUtils.instance.addPoint(
              buryingPointList: BuryingPointList(
                  eventType: 4, entranceType: 6, action: 1, browseDuration: 0));
          timingRepository.openTimingMiniProgram(
              path: '/pages/operate/article/details/details?id=${item.id}');

          // Toast.show("第$index个item页面");
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              flex: 2,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  app14spA1Text(item.title,
                          color: const Color(0xff3D3D3D),
                          fontFamily: 'PingFangSC-Semibold')
                      .paddingOnly(bottom: 30.h),
                  app12spAAAText('${item.views}人 阅读',
                      color: const Color(0xffBEBEBE),
                      fontFamily: 'PingFangSC-Semibold')
                ],
              ).paddingOnly(right: 20.w),
            ),
            Expanded(
              flex: 1,
              child: item.cover.isEmpty
                  ? const SizedBox()
                  : netWorkCacheImg2(
                      img: item.cover[0], w: 118.w, h: 80.h, fit: BoxFit.fill),
            )
          ],
        ).paddingOnly(top: 15.h, bottom: 15.h));
  }

//直播Live
  Widget _buildLiveing(SectionFourController logic) {
    return sliverPaddingToBoxAdapter(
        padding: EdgeInsets.symmetric(horizontal: 12.w),
        child: logic.hotLiveList == null
            ? const SizedBox()
            : Container(
                // width: double.infinity,
                decoration: BoxDecoration(
                    image: DecorationImage(
                        image: assetImg2(
                          img: "bg_home_live",
                        ),
                        fit: BoxFit.fill)),
                // gradient: const LinearGradient(
                //     colors: [AppColors.feddd7, AppColors.fcedca],
                //     begin: Alignment.centerLeft,
                //     end: Alignment.centerRight),
                // padding: EdgeInsets.all(16.w),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 71.w,
                          height: 18.h,
                          decoration: BoxDecoration(
                            image: DecorationImage(
                              image: assetImg2(
                                img: "ic_home_live_tips",
                              ),
                              fit: BoxFit.fill,
                            ),
                          ),
                          // child: Row(
                          //   children: [
                          //     assImg2(img: 'icon_hone_zhiboke_re', w: 12.w, h: 16.h),
                          //     app12spFFFFFText('热门课',
                          //             fontFamily: 'PingFangSC-Regular')
                          //         .paddingOnly(left: 2.w)
                          //   ],
                          // ),
                        ).paddingOnly(top: 11.h, left: 11.w),
                        Expanded(
                          child: Align(
                            alignment: Alignment.centerRight,
                            child: app12spFF6D5EText(
                                    color: Color(0xff454545),
                                    "${logic.hotLiveList?.popularityScore.toString() ?? "0"}人正在观看")
                                // app20sp1A1A1A7Text(_logic.hotLiveList?.title ?? "",
                                //         fontWeight: FontWeight.w500,
                                //         fontFamily: "PingFangSC-Medium")
                                .paddingOnly(right: 15.w),
                          ),
                        ),
                      ],
                    ),
                    logic.hotLiveList == null
                        ? const SizedBox()
                        : Row(
                            children: [
                              Expanded(
                                  child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  app16spA1Text(logic.hotLiveList?.title ?? "",
                                          fontWeight: FontWeight.w500,
                                          overflow: TextOverflow.ellipsis,
                                          fontFamily: "PingFangSC-Semibold")
                                      .paddingOnly(right: 8.w),
                                  app12spAAAText(
                                          logic.hotLiveList?.description ?? "",
                                          overflow: TextOverflow.ellipsis,
                                          color: const Color(0xff137CCF),
                                          fontFamily: 'PingFangSC-Medium')
                                      .paddingOnly(top: 12.h),
                                  Container(
                                    height: 32.h,
                                    child: InkWell(
                                        onTap: () {
                                          Map map = jsonDecode(
                                              logic.hotLiveList!.platformLink!);
                                          if (logic.hotLiveList!.platformType == 1 ||
                                              logic.hotLiveList!.platformType == 3) {
                                            launchUrl(map["path"]);
                                          } else {
                                            timingRepository
                                                .openFluwxMiniProgram(
                                              map["Path"],
                                              map['AppId'],
                                            );
                                          }
                                          // launchUrl(_logic.hotLiveList?.shareUrl ?? "");
                                        },
                                        child: logic.hotLiveList?.liveStatus == 1
                                            ? Image(
                                                image: AssetImage(
                                                  "assets/home_img/follow_live.gif",
                                                ),
                                                fit: BoxFit.fitWidth)
                                            : Image(
                                                image: AssetImage(
                                                    "assets/home_img/living_room.gif"),
                                                fit: BoxFit.fitWidth)),
                                  ).paddingOnly(top: 21.h, bottom: 8.h)
                                ],
                              ).paddingOnly(left: 12.w)),
                              Expanded(
                                  child: netWorkCacheImg2(
                                img: logic.hotLiveList?.cover,
                                w: 170.w,
                                h: 96.h,
                              ).paddingOnly(right: 12.w)),
                            ],
                          ).paddingOnly(top: 8.h)
                  ],
                ),
              ).paddingOnly(top: 11.h));
  }

//直播Live列表
//   Widget _buildLiveList(zhiboimg) {
//     return sliverPaddingToBoxAdapter(
//         padding: EdgeInsets.symmetric(horizontal: 16.w),
//         child: SizedBox(
//           height: 11.h,
//         )
//         // Container(
//         //   margin: EdgeInsets.symmetric(vertical: 16.h),
//         //   padding: EdgeInsets.all(16.h),
//         //   height: 203.h,
//         //   decoration: BoxDecoration(
//         //       borderRadius: BorderRadius.circular(8.r), color: AppColors.D0E9FC),
//         //   child: Column(
//         //     children: [
//         //       Row(
//         //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//         //         children: [
//         //           app20sp1A1A1A7Text('直播快速学科四'),
//         //           iconMore(onTap: () {
//         //             Toast.show("直播更多信息");
//         //             debugPrint("直播更多信息");
//         //           })
//         //         ],
//         //       ),
//         //       SizedBox(
//         //         height: 131.h,
//         //         child: ListView.builder(
//         //             padding: EdgeInsets.zero,
//         //             itemCount: zhiboimg.length,
//         //             scrollDirection: Axis.horizontal,
//         //             itemBuilder: (_, index) {
//         //               return liveItem(zhiboimg, index);
//         //             }),
//         //       ).paddingOnly(top: 5.h)
//         //     ],
//         //   ),
//         // ),
//         );
//   }

//直播列表item
  Widget liveItem(zhiboimg, int index) {
    return Container(
      width: 253.w,
      // height: 131.h,
      decoration: BoxDecoration(
          color: AppColors.white, borderRadius: BorderRadius.circular(4.r)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          assImg2(img: zhiboimg[index], w: 86.w, h: 107.h)
              .paddingSymmetric(horizontal: 16.w),
          Expanded(
              child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              app16spA1Text('3小时精髓课3小时精髓课').paddingOnly(right: 16.w),
              GestureDetector(
                onTap: () {},
                child: Container(
                    height: 28.h,
                    width: 89.w,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(18.r),
                        color: AppColors.other_login_text),
                    child: Center(child: app14spFFFFText('立即学习'))),
              ),
            ],
          ))
        ],
      ),
    ).paddingOnly(right: 16.w);
  }

//VIP课程
//   Widget _buildVIPSubject(context) {
//     return Stack(children: [
//       InkWell(
//         overlayColor: MaterialStateProperty.all(Colors.transparent),
//         child: Container(
//             decoration: BoxDecoration(
//               borderRadius: BorderRadius.all(Radius.circular(8.r)),
//               image: DecorationImage(
//                   fit: BoxFit.fill, image: assetImg2(img: "bg_home_vip")),
//             ),
//             child: Stack(
//               children: [
//                 Positioned(
//                     top: 92.h,
//                     left: 22.w,
//                     child: Text(
//                       "${(logic.vipModel?.qualificationRate?.customValue == 2 ? logic.vipModel?.qualificationRate?.actualValue : logic.vipModel?.qualificationRate?.customValue) ?? 0}",
//                       style: TextStyle(
//                           fontSize: 27.sp,
//                           color: AppColors.white,
//                           fontFamily: 'PingFangSC-Regular'),
//                     ))
//               ],
//             )),
//         onTap: () {
//           if (!MainController.isLoginIntercept()) {
//             return;
//           }
//           // Toast.show("跳转VIP页面");
//           JumpUtils.get().jumpPromote(4, vipType: 4, type: 'jingxuan600ti');
//         },
//       ).paddingOnly(top: 3.h, left: 12.w),
//       Positioned(
//           top: 0.h,
//           left: 12.w,
//           child: assImg2(
//               img: 'bg_home_vip_tab', w: 98.w, h: 24.h, fit: BoxFit.fill)),
//       Positioned(
//           top: 3.h,
//           left: 20.w,
//           child: app12spFFFFFText(
//               '已帮助${(logic.vipModel?.helpCount?.customValue == 2 ? logic.vipModel?.helpCount?.actualValue : logic.vipModel?.helpCount?.customValue) ?? 0}人')),
//       Positioned(
//           top: 70.h,
//           left: 90.w,
//           child: InkWell(
//             child: assImg2(img: 'icon_my_wenhao', w: 14.w, h: 14.h)
//                 .paddingSymmetric(horizontal: 11.w),
//             onTap: () {
//               Toast.show(
//                   '“考试通过率”来自大象驾到后台统计数据。指科一四vip用户，获取到了vip补偿资格后，在正式考试中没有通过的，并且有在大象驾到APP上报他的“不通过成绩”。大象据此统计出vip课程“不通过率”，再据此得出vip课程“考试通过率”',
//                   duration: 2000);
//             },
//           ))
//     ]);
//   }

//从业资格培训59724
  Widget _buildQualificationTraining(VoidCallback gotoPractitionerVideo) {
    return sliverPaddingToBoxAdapter(
        padding: EdgeInsets.symmetric(horizontal: 15.w),
        child: InkWell(
          overlayColor: MaterialStateProperty.all(Colors.transparent),
          onTap: () {
            if (TimingHelper.isRemoteTrain) {
              Toast.show('请先结束理论计时再开启从业培训');
              return;
            }
            if (!(ITiming.get().openPractitioner ?? false)) {
              Toast.show('您未开通从业资格培训，请联系报名驾校开通后再次尝试!');
              return;
            }
            gotoPractitionerVideo.call();
          },
          child: Container(
            padding: EdgeInsets.only(right: 16.w),
            height: 72.h,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.r),
                color: AppColors.C5E6FF,
                image: DecorationImage(
                    image: assetImg2(img: "img_home_congye"),
                    fit: BoxFit.fill)),
            child: Row(
              children: [
                app16spA1Text('从业资格培训', fontWeight: FontWeight.w600)
                    .paddingOnly(left: 66.w),
                const Spacer(),
                Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    _buildStudyHouseItem(
                        '已培训时长', ITiming.get().practitionerPeriod(4),
                        top: 16.h, right: 0),
                    _buildStudyHouseItem(
                        '需培训时长', ITiming.get().practitionerLimit(4),
                        top: 1.h, right: 0)
                  ],
                ),
                const Spacer(),
                assImg2(img: 'icon_more_white', w: 24.w, h: 24.h)
              ],
            ),
          ).paddingSymmetric(vertical: 15.h),
        ));
  }

//学时详情
  Widget _buildStudyHouse(int subject, TimingRepository timingRepository,SectionFourController logic) {
    return sliverPaddingToBoxAdapter(
        padding: EdgeInsets.all(5.h),
        child: Stack(
          children: [
            GestureDetector(
              onTap: () {
                if (!MainController.isLoginIntercept()) {
                  return;
                }
                BuryingPointUtils.instance.addPoint(
                    buryingPointList: BuryingPointList(
                        eventType: 3,
                        entranceType: subject==1?35:subject==2?36:subject==3?37:38,
                        action: 1, browseDuration: 0)
                );
                JumpTimings.get().jumpPeriod(subject);
              },
              child: white8radiusContainer(
                // height: 245.h,
                // boxShadow: [
                //   BoxShadow(
                //       color: AppColors.C000000,
                //       offset: Offset(-1.0.h, 1.0.h), //阴影y轴偏移量
                //       blurRadius: 1.h, //阴影模糊程度
                //       spreadRadius: 1.r)
                // ],
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // GestureDetector(
                    //     child:
                    Row(
                      children: [
                        app16spA1Text('我的学时',fontFamily: 'PingFangSC-Semibold'),
                        SizedBox(width: 4.w),
                        assImg2(img: 'new_icon_more', w: 16.w, h: 16.h)
                      ],
                    ),
                    // onTap: () {
                    //   // timingRepository.openTimingMiniProgram(path: 'pages/courseDuration/courseDuration');
                    //   JumpTimings.get().jumpPeriod(subject);
                    // }
                    // ),
                    // app12sp1A1AText('共需学习学时：284分钟').paddingOnly(right: 26.w, top: 28.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildStudyHouseItem(
                              '目标学时', ITiming.get().limit(subject),),
                            _buildStudyHouseItem(
                                '有效学时', ITiming.get().periodValid(subject)),
                          ],
                        ),
                        _buildStudyHouseItem(
                            '已学时长', ITiming.get().period(subject)),
                      ],
                    ),
                    SizedBox(height: 25.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Stack(
                          alignment: Alignment.center,
                          children: <Widget>[
                            Container(
                              height: 116.h,
                              width: 116.w,
                              decoration: BoxDecoration(
                                // color: Colors.blueAccent,
                                  image: DecorationImage(
                                    image: assetImg2(img: "study_house_progress_bg"),
                                  )
                              ),
                              child: TurnBox(
                                turns: (1 / 2),
                                child: GradientCircularProgressIndicator(
                                  colors: const [
                                    // AppColors.other_login_text,
                                    // AppColors.other_login_text,
                                    Color(0xFFFFFFFF),
                                    Color(0xFF2FA7F6),
                                  ],
                                  radius: 58.5.r,
                                  stokeWidth: 9.0,
                                  strokeCapRound: true,
                                  backgroundColor: Colors.transparent,
                                  totalAngle: 2.0 * pi,
                                  value: ITiming.get()
                                      .periodValidRatio(subject) /
                                      100,
                                  // value: 0.2,
                                ),
                              ),
                            ),
                            Positioned(
                              top: 38.0.h,
                              left: (ITiming.get().periodValidRatio(subject).toInt())>9?41.w:45.w,
                              child: app16spAC5000Text(
                                  color: Color(0xff2EA5F6),
                                  "${ITiming.get().periodValidRatio(subject).toInt()>100?100:ITiming.get().periodValidRatio(subject).toInt()}%",
                                  fontFamily: 'PingFangSC-Semibold'),
                            ),
                            Positioned(
                              top: 60.0.h,
                              left: 34.w,
                              child: app12spAAAText(color: Color(0xff333333),
                                "学时进度",fontFamily: 'PingFangSC-Semibold',
                              ),
                            ),
                          ],
                        ),
                        GestureDetector(
                          onTap: () {
                            if (!MainController.isLoginIntercept()) {
                              return;
                            }
                            BuryingPointUtils.instance.addPoint(
                                buryingPointList: BuryingPointList(
                                    eventType: 3,
                                    entranceType: 43,
                                    action: 1,
                                    browseDuration: 0));
                            JumpUtils.get().jumpChapter(JkKey.SUBFOUR);
                          },
                          child: Stack(
                            alignment: Alignment.center,
                            children: <Widget>[
                              Container(
                                height: 116.h,
                                width: 116.w,
                                decoration: BoxDecoration(
                                  // color: Colors.blueAccent,
                                    image: DecorationImage(
                                      image: assetImg2(img: "study_house_progress_bg"),
                                    )
                                ),
                                child: TurnBox(
                                  turns: (1 / 2),
                                  child: GradientCircularProgressIndicator(
                                    colors: const [
                                      // AppColors.other_login_text,
                                      // AppColors.other_login_text,
                                      Color(0xFF757FFF),
                                      Color(0xFF2FA7F6),
                                    ],
                                    radius: 58.5.r,
                                    stokeWidth: 9.0,
                                    strokeCapRound: true,
                                    backgroundColor: Colors.transparent,
                                    totalAngle: 2.0 * pi,
                                    // value: ITiming.get()
                                    //     .periodValidRatio(subject) /
                                    //     100,
                                    value: logic.total==0?0:(logic.completed/logic.total),
                                    // value: 0.22,
                                  ),
                                ),
                              ),
                              Positioned(
                                top: 38.0.h,
                                left: (logic.total==0?0:(((logic.completed/logic.total)*100).round()))>9?41.w:45.w,
                                child: app16spAC5000Text(
                                    color: Color(0xff2EA5F6),
                                    "${(logic.total==0?0:((logic.completed/logic.total)*100).round())>100?100:logic.total==0?0:(((logic.completed/logic.total)*100).round())}%",
                                    fontFamily: 'PingFangSC-Semibold'),
                              ),
                              Positioned(
                                top: 60.0.h,
                                left: 24.w,
                                child: app12spAAAText(color: Color(0xff333333),
                                  "考场题库",fontFamily: 'PingFangSC-Semibold',
                                ),
                              ),
                              Positioned(
                                  top: 60.0.h,
                                  left: 73.w,
                                  child: assImg2(img: 'kaochangtiku_jiantou',h: 16.h,w: 16.w)
                              ),
                            ],
                          ),
                        ),
                      ],
                    )
                  ],
                ).paddingOnly(left: 12.w, top: 12.h, bottom: 11.h,right: 5.w),
              ),
            ),
            Positioned(
              top: 0.h,
              right: 0.w,
              child: GestureDetector(
                  onTap: (){
                    timingRepository.openFluwxKefuMiniProgram();
                  },
                  child: assImg2(img: 'Ip_xueshi_kefu', w: 65.w, h: 25.h)
              ),
            )
          ],
        ).paddingOnly(top: 6.h, left: 6.w, right: 6.w));
  }

  //学时监管item
  Widget _buildStudyHouseItem(String title, int time,
      {double? top, double? right}) {
    return EasyRichText(
      "$title：$time分钟",
      // strutStyle: StrutStyle(fontSize: 12.sp),
      patternList: [
        EasyRichTextPattern(
            targetString: title,
            style: TextStyle(
                color: AppColors.C333333, fontSize: 14.sp,fontFamily:title == '已学时长'? 'PingFangSC-Regular' : 'PingFangSC-Semibold')),
        EasyRichTextPattern(
            targetString: time.toString(),
            style: TextStyle(
                color: top == null || right != null
                    ? AppColors.C2BA9F4
                    : AppColors.privacy_dialog_titile_color,
                fontSize: 14.sp,fontFamily:title == '已学时长'? 'PingFangSC-Regular' : 'PingFangSC-Semibold')),
        EasyRichTextPattern(
            targetString: "分钟",
            style: TextStyle(
                color: top == null || right != null
                    ? AppColors.C2BA9F4
                    : AppColors.privacy_dialog_titile_color,
                fontSize: 14.sp,fontFamily:title == '已学时长'? 'PingFangSC-Regular' : 'PingFangSC-Semibold'))
      ],
    ).paddingOnly(top: top ?? 10.h, right: right ?? 26.w);
  }


//   滚动提示组件
  Widget _buildScrollTip(SectionFourController logic) {
    return SliverToBoxAdapter(
        child: IHome.get().pass4 == null
            ? const SizedBox()
            : Container(
                margin: REdgeInsets.only(left: 11, right: 11, top: 11),
                height: 43.h, // 固定高度
                child: ListView.builder(
                  controller: logic.scrollController,
                  padding: EdgeInsets.zero,
                  physics: const ClampingScrollPhysics(), // 禁止用户手动滚动
                  itemCount: logic.colors.length,
                  itemBuilder: (context, index) {
                    return index == 0
                        ? _buildPassRateTip()
                        : _buildAppointTip();
                  },
                ),
              ));
  }

  //通关率提示
  Widget _buildPassRateTip() {
    bool isRecord = IHome.get().pass4!.isHigh.isNotEmpty;
    return Container(
      height: 43.h,
      decoration: BoxDecoration(
        image: DecorationImage(
          image: isRecord
              ? assetImg2(img: "home_tips_tongguolv")
              : assetImg2(img: "home_tips_yuce"),
        ),
        // borderRadius: BorderRadius.circular(8.r),
        // color: const Color(0xFFFCF0EB),
      ),
      child: isRecord
          ? Row(
        children: [
          SizedBox(width: 9.w),
          assImg2(img: 'icon_home_tongguanlv', w: 30.w, h: 30.h),
          SizedBox(width: 2.w),
          Text(
            '您科一考试预测通过率：',
            style: TextStyle(
                color: const Color(0xFF000000),
                fontSize: 14.sp,
                fontFamily: 'PingFangSC-Medium'),
          ),
          Text(
            '${IHome.get().pass4!.rate}%',
            style: TextStyle(
                color: const Color(0xFFFB9A50),
                fontSize: 18.sp,
                fontFamily: 'PingFangSC-Semibold'),
          ),
          const Spacer(),
          InkWell(
            onTap: () {
              if (!MainController.isLoginIntercept()) {
                return;
              }
              JumpUtils.get().jumpMockHome(JkKey.SUBFOUR);
            },
            child: Container(
              height: 29.h,
              width: 70.w,
              padding:
              REdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
              decoration: BoxDecoration(
                  image: DecorationImage(
                      image: assetImg2(img: "bg_home_gotifen"))
                // color: const Color(0xFF268FF7),
                // borderRadius: BorderRadius.circular(12.r)
              ),
              // child: Text(
              //   '去模考',
              //   style: TextStyle(
              //       color: const Color(0xFFFFFFFF),
              //       fontSize: 14.sp,
              //       fontFamily: 'PingFangSC-Semibold'),
              // ),
            ),
          ),
          SizedBox(width: 8.w),
        ],
      )
          : Row(
        children: [
          SizedBox(width: 9.w),
          assImg2(img: 'icon_home_untongguanlv', w: 30.w, h: 30.h),
          SizedBox(width: 2.w),
          Text(
            '2天',
            style: TextStyle(
                color: const Color(0xFFFF643D),
                fontSize: 14.sp,
                fontFamily: 'PingFangSC-Semibold'),
          ),
          Text(
            '内模考',
            style: TextStyle(
                color: const Color(0xFF000000),
                fontSize: 14.sp,
                fontFamily: 'PingFangSC-Medium'),
          ),
          Text(
            '3次',
            style: TextStyle(
                color: const Color(0xFFFF663E),
                fontSize: 14.sp,
                fontFamily: 'PingFangSC-Semibold'),
          ),
          Text(
            '课预测',
            style: TextStyle(
                color: const Color(0xFF000000),
                fontSize: 14.sp,
                fontFamily: 'PingFangSC-Medium'),
          ),
          Text(
            '通过率',
            style: TextStyle(
                color: const Color(0xFFFF653E),
                fontSize: 16.sp,
                fontFamily: 'PingFangSC-Semibold'),
          ),
          const Spacer(),
          InkWell(
            onTap: () {
              if (!MainController.isLoginIntercept()) {
                return;
              }
              JumpUtils.get().jumpMockHome(JkKey.SUBFOUR);
            },
            child: Container(
              height: 29.h,
              width: 70.w,
              padding:
              REdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
              decoration: BoxDecoration(
                  image: DecorationImage(
                      image: assetImg2(img: "bg_home_gook"))
                // color: const Color(0xFF268FF7),
                // borderRadius: BorderRadius.circular(12.r)
              ),
              // child: Text(
              //   '去模考',
              //   style: TextStyle(
              //       color: const Color(0xFFFFFFFF),
              //       fontSize: 14.sp,
              //       fontFamily: 'PingFangSC-Semibold'),
              // ),
            ),
          ),
          SizedBox(width: 8.w),
        ],
      ),
    );
  }

  //约考提示
  Widget _buildAppointTip() {
    return Container(
      decoration: BoxDecoration(
        image: DecorationImage(
          image: assetImg2(img: "home_tips_qumokao"),
        ),
        // borderRadius: BorderRadius.circular(8.r),
        // color: const Color(0xFFE8F3FF),
      ),
      height: 43.h,
      child: Row(
        children: [
          SizedBox(width: 9.w),
          assImg2(img: 'icon_home_mokaotishi', w: 30.w, h: 30.h),
          SizedBox(width: 2.w),
          Text(
            '务必',
            style: TextStyle(
                color: const Color(0xFF000000),
                fontSize: 14.sp,
                fontFamily: 'PingFangSC-Medium'),
          ),
          Text(
            '3次',
            style: TextStyle(
                color: const Color(0xFF2BA9F4),
                fontSize: 16.sp,
                fontFamily: 'PingFangSC-Semibold'),
          ),
          Text(
            '模考达到',
            style: TextStyle(
                color: const Color(0xFF000000),
                fontSize: 14.sp,
                fontFamily: 'PingFangSC-Medium'),
          ),
          Text(
            '93分',
            style: TextStyle(
                color: const Color(0xFF2BA9F4),
                fontSize: 16.sp,
                fontFamily: 'PingFangSC-Semibold'),
          ),
          Text(
            '再去约考!',
            style: TextStyle(
                color: const Color(0xFF000000),
                fontSize: 14.sp,
                fontFamily: 'PingFangSC-Medium'),
          ),
          const Spacer(),
          InkWell(
            onTap: () {
              if (!MainController.isLoginIntercept()) {
                return;
              }
              JumpUtils.get().jumpMockHome(JkKey.SUBFOUR);
            },
            child: Container(
              height: 29.h,
              width: 70.w,
              padding: REdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
              decoration: BoxDecoration(
                  image: DecorationImage(
                      image: assetImg2(img: "bg_home_mokaotishi"))
                // gradient: const LinearGradient(colors: [
                //   Color(0xFFEC4133),
                //   Color(0xFFFF5F0C),
                // ], begin: Alignment.centerLeft, end: Alignment.centerRight),
                // borderRadius: BorderRadius.circular(12.r)
              ),
            ),
          ),
          SizedBox(width: 8.w),
        ],
      ),
    );
  }
}
