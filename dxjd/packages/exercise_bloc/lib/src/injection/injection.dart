import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';

import '../domain/repositories/question_repository.dart';
import '../domain/repositories/audio_repository.dart';
import '../domain/repositories/progress_repository.dart';

import '../domain/usecases/load_questions.dart';
import '../domain/usecases/answer_question.dart';
import '../domain/usecases/play_audio.dart';
import '../domain/usecases/save_progress.dart';

import '../presentation/bloc/exercise/exercise_bloc.dart';
import '../presentation/bloc/listen/listen_bloc.dart';
import '../presentation/bloc/browse/browse_bloc.dart';
import '../presentation/bloc/mode_switch/mode_switch_bloc.dart';

import 'injection.config.dart';

final getIt = GetIt.instance;

@InjectableInit()
void configureDependencies() => getIt.init();

/// Manual dependency injection setup
/// This will be replaced by generated code when build_runner is executed
void setupDependencies() {
  // Repositories (these will be implemented in the data layer)
  // getIt.registerLazySingleton<QuestionRepository>(() => QuestionRepositoryImpl());
  // getIt.registerLazySingleton<AudioRepository>(() => AudioRepositoryImpl());
  // getIt.registerLazySingleton<ProgressRepository>(() => ProgressRepositoryImpl());

  // Use cases
  getIt.registerLazySingleton<LoadQuestions>(
    () => LoadQuestions(getIt<QuestionRepository>()),
  );
  
  getIt.registerLazySingleton<AnswerQuestion>(
    () => AnswerQuestion(getIt<QuestionRepository>()),
  );
  
  getIt.registerLazySingleton<PlayAudio>(
    () => PlayAudio(
      getIt<AudioRepository>(),
      getIt<QuestionRepository>(),
    ),
  );
  
  getIt.registerLazySingleton<SaveProgress>(
    () => SaveProgress(getIt<ProgressRepository>()),
  );

  // BLoCs
  getIt.registerFactory<ExerciseBloc>(
    () => ExerciseBloc(
      loadQuestions: getIt<LoadQuestions>(),
      answerQuestion: getIt<AnswerQuestion>(),
      saveProgress: getIt<SaveProgress>(),
      questionRepository: getIt<QuestionRepository>(),
    ),
  );
  
  getIt.registerFactory<ListenBloc>(
    () => ListenBloc(
      loadQuestions: getIt<LoadQuestions>(),
      playAudio: getIt<PlayAudio>(),
      saveProgress: getIt<SaveProgress>(),
      audioRepository: getIt<AudioRepository>(),
      questionRepository: getIt<QuestionRepository>(),
    ),
  );
  
  getIt.registerFactory<BrowseBloc>(
    () => BrowseBloc(
      loadQuestions: getIt<LoadQuestions>(),
      questionRepository: getIt<QuestionRepository>(),
    ),
  );
  
  getIt.registerLazySingleton<ModeSwitchBloc>(
    () => ModeSwitchBloc(),
  );
}

/// Injectable annotations for code generation

@module
abstract class RepositoryModule {
  // These will be implemented when we create the data layer
  // @lazySingleton
  // QuestionRepository get questionRepository => QuestionRepositoryImpl();
  
  // @lazySingleton
  // AudioRepository get audioRepository => AudioRepositoryImpl();
  
  // @lazySingleton
  // ProgressRepository get progressRepository => ProgressRepositoryImpl();
}

@module
abstract class UseCaseModule {
  @lazySingleton
  LoadQuestions loadQuestions(QuestionRepository repository) =>
      LoadQuestions(repository);
  
  @lazySingleton
  AnswerQuestion answerQuestion(QuestionRepository repository) =>
      AnswerQuestion(repository);
  
  @lazySingleton
  PlayAudio playAudio(
    AudioRepository audioRepository,
    QuestionRepository questionRepository,
  ) =>
      PlayAudio(audioRepository, questionRepository);
  
  @lazySingleton
  SaveProgress saveProgress(ProgressRepository repository) =>
      SaveProgress(repository);
}

@module
abstract class BlocModule {
  @factory
  ExerciseBloc exerciseBloc(
    LoadQuestions loadQuestions,
    AnswerQuestion answerQuestion,
    SaveProgress saveProgress,
    QuestionRepository questionRepository,
  ) =>
      ExerciseBloc(
        loadQuestions: loadQuestions,
        answerQuestion: answerQuestion,
        saveProgress: saveProgress,
        questionRepository: questionRepository,
      );
  
  @factory
  ListenBloc listenBloc(
    LoadQuestions loadQuestions,
    PlayAudio playAudio,
    SaveProgress saveProgress,
    AudioRepository audioRepository,
    QuestionRepository questionRepository,
  ) =>
      ListenBloc(
        loadQuestions: loadQuestions,
        playAudio: playAudio,
        saveProgress: saveProgress,
        audioRepository: audioRepository,
        questionRepository: questionRepository,
      );
  
  @factory
  BrowseBloc browseBloc(
    LoadQuestions loadQuestions,
    QuestionRepository questionRepository,
  ) =>
      BrowseBloc(
        loadQuestions: loadQuestions,
        questionRepository: questionRepository,
      );
  
  @lazySingleton
  ModeSwitchBloc modeSwitchBloc() => ModeSwitchBloc();
}
