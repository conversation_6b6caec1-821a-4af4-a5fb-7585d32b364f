import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:exercise_bloc/src/presentation/pages/exercise_page.dart';
import 'package:exercise_bloc/src/presentation/bloc/exercise/exercise_bloc.dart';
import 'package:exercise_bloc/src/presentation/bloc/listen/listen_bloc.dart';
import 'package:exercise_bloc/src/presentation/bloc/browse/browse_bloc.dart';
import 'package:exercise_bloc/src/presentation/bloc/video/video_bloc.dart';
import 'package:exercise_bloc/src/presentation/bloc/mode_switch/mode_switch_bloc.dart';

import '../injection/real_injection.dart';

/// 真实的练习页面 - 使用exercise_bloc包和真实数据源
/// 完全复现旧版PageChapter的功能和UI
class RealExercisePage extends StatelessWidget {
  const RealExercisePage({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<ExerciseBloc>(
          create: (context) => getIt<ExerciseBloc>(),
        ),
        BlocProvider<ListenBloc>(
          create: (context) => getIt<ListenBloc>(),
        ),
        BlocProvider<BrowseBloc>(
          create: (context) => getIt<BrowseBloc>(),
        ),
        BlocProvider<VideoBloc>(
          create: (context) => getIt<VideoBloc>(),
        ),
        BlocProvider<ModeSwitchBloc>(
          create: (context) => getIt<ModeSwitchBloc>(),
        ),
      ],
      child: const ExercisePage(
        subject: 1, // 科目一
        type: 'chapter', // 顺序练习
        sortId: 1, // 默认章节ID，可以根据需要修改
        title: '顺序练习',
      ),
    );
  }
}
