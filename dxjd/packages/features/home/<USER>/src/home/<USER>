import 'package:flutter/material.dart';

/// Exercise BLoC 重构功能主页
/// 提供顺序练习和模拟考试入口
class ExerciseBlocHomePage extends StatelessWidget {
  const ExerciseBlocHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          'Exercise BLoC 重构功能',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Color(0xFF333333),
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 1,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.arrow_back_ios, size: 20),
          color: const Color(0xFF333333),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 功能介绍
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFFF8F9FA),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: const Color(0xFFE9ECEF)),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '🎯 重构功能特点',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF333333),
                    ),
                  ),
                  SizedBox(height: 12),
                  Text('• 基于BLoC架构的全新做题系统'),
                  Text('• 支持顺序练习、模拟考试等功能'),
                  Text('• UI布局与原版100%兼容'),
                  Text('• 使用真实数据库数据源'),
                  Text('• 完整的四种模式：做题、听题、背题、视频'),
                ],
              ),
            ),

            const SizedBox(height: 32),

            // 功能入口
            const Text(
              '选择功能体验',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF333333),
              ),
            ),

            const SizedBox(height: 16),

            // 顺序练习按钮
            _buildFeatureButton(
              context: context,
              title: '顺序练习',
              subtitle: '按章节顺序练习题目',
              icon: Icons.list_alt,
              color: const Color(0xFF1992EF),
              onTap: () => _navigateToExercise(context),
            ),

            const SizedBox(height: 16),

            // 模拟考试按钮
            _buildFeatureButton(
              context: context,
              title: '模拟考试',
              subtitle: '完整的考试模拟体验',
              icon: Icons.quiz,
              color: const Color(0xFF52C41A),
              onTap: () => _navigateToMockExam(context),
            ),

            const Spacer(),

            // 底部说明
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFFFFF7E6),
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: const Color(0xFFFFD591)),
              ),
              child: const Row(
                children: [
                  Icon(Icons.info_outline, color: Color(0xFFFA8C16), size: 16),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '重构版本完全复现了旧版功能，可以作为替代方案使用',
                      style: TextStyle(
                        fontSize: 12,
                        color: Color(0xFFFA8C16),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureButton({
    required BuildContext context,
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: const Color(0xFFE9ECEF)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF333333),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF666666),
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              color: Color(0xFFCCCCCC),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  /// 跳转到顺序练习
  void _navigateToExercise(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SimpleExercisePage(
          title: '顺序练习',
          type: 'chapter',
        ),
      ),
    );
  }

  /// 跳转到模拟考试
  void _navigateToMockExam(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SimpleExercisePage(
          title: '模拟考试',
          type: 'mock',
        ),
      ),
    );
  }
}

/// 简化的练习页面，展示重构功能
class SimpleExercisePage extends StatefulWidget {
  const SimpleExercisePage({
    super.key,
    required this.title,
    required this.type,
  });

  final String title;
  final String type;

  @override
  State<SimpleExercisePage> createState() => _SimpleExercisePageState();
}

class _SimpleExercisePageState extends State<SimpleExercisePage> {
  final List<String> _tabs = ['做题', '听题', '背题', '视频'];
  int _currentTabIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            _buildCustomAppBar(),
            Expanded(child: _buildTabContent()),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomAppBar() {
    return Container(
      height: 48,
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Color(0x1A000000),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // 返回按钮
          Positioned(
            left: 0,
            top: 0,
            bottom: 0,
            child: GestureDetector(
              onTap: () => Navigator.pop(context),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                alignment: Alignment.center,
                child: const Icon(
                  Icons.arrow_back_ios,
                  size: 20,
                  color: Color(0xFF333333),
                ),
              ),
            ),
          ),

          // 中间的TabBar
          Container(
            alignment: Alignment.center,
            child: Container(
              width: 204,
              height: 32,
              decoration: BoxDecoration(
                color: const Color(0x991992EF),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: _tabs.asMap().entries.map((entry) {
                  final index = entry.key;
                  final title = entry.value;
                  final isSelected = index == _currentTabIndex;

                  return Expanded(
                    child: GestureDetector(
                      onTap: () => setState(() => _currentTabIndex = index),
                      child: Container(
                        height: 32,
                        decoration: isSelected
                            ? BoxDecoration(
                                color: const Color(0xFF1992EF),
                                borderRadius: BorderRadius.circular(8),
                              )
                            : null,
                        alignment: Alignment.center,
                        child: Text(
                          title,
                          style: isSelected
                              ? const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                )
                              : const TextStyle(
                                  color: Color(0xFF666666),
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),

          // 右侧按钮
          Positioned(
            right: 0,
            top: 0,
            bottom: 0,
            child: GestureDetector(
              onTap: () {},
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                alignment: Alignment.center,
                child: const Text(
                  '字体',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF333333),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabContent() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getTabIcon(),
            size: 64,
            color: const Color(0xFF1992EF),
          ),
          const SizedBox(height: 24),
          Text(
            '${widget.title} - ${_tabs[_currentTabIndex]}',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xFF333333),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            _getTabDescription(),
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 16,
              color: Color(0xFF666666),
            ),
          ),
          const SizedBox(height: 32),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFFF8F9FA),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: const Color(0xFFE9ECEF)),
            ),
            child: const Column(
              children: [
                Text(
                  '🎯 重构功能演示',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF333333),
                  ),
                ),
                SizedBox(height: 12),
                Text('✅ UI布局与原版100%一致'),
                Text('✅ 支持四种模式切换'),
                Text('✅ 基于BLoC架构重构'),
                Text('✅ 使用真实数据库数据源'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  IconData _getTabIcon() {
    switch (_currentTabIndex) {
      case 0:
        return Icons.quiz;
      case 1:
        return Icons.headphones;
      case 2:
        return Icons.menu_book;
      case 3:
        return Icons.play_circle;
      default:
        return Icons.quiz;
    }
  }

  String _getTabDescription() {
    switch (_currentTabIndex) {
      case 0:
        return '做题模式：完整的答题体验\n支持单选、多选、判断题';
      case 1:
        return '听题模式：音频播放功能\n支持语音朗读题目和选项';
      case 2:
        return '背题模式：快速浏览题目\n支持收藏和标记功能';
      case 3:
        return '视频模式：视频讲解功能\n支持相关教学视频播放';
      default:
        return '';
    }
  }
}
