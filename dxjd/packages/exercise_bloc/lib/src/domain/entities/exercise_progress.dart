import 'package:equatable/equatable.dart';

/// Exercise progress entity
class ExerciseProgress extends Equatable {
  const ExerciseProgress({
    required this.subject,
    required this.type,
    required this.currentIndex,
    required this.totalQuestions,
    required this.correctCount,
    required this.wrongCount,
    required this.answeredQuestions,
    this.startTime,
    this.endTime,
    this.timeSpent = Duration.zero,
    this.score = 0,
  });

  /// Subject (1 for subject 1, 4 for subject 4)
  final int subject;
  
  /// Exercise type (chapter, random, error, collect, etc.)
  final String type;
  
  /// Current question index
  final int currentIndex;
  
  /// Total number of questions
  final int totalQuestions;
  
  /// Number of correct answers
  final int correctCount;
  
  /// Number of wrong answers
  final int wrongCount;
  
  /// Map of question ID to user answer
  final Map<int, String> answeredQuestions;
  
  /// Exercise start time
  final DateTime? startTime;
  
  /// Exercise end time
  final DateTime? endTime;
  
  /// Total time spent
  final Duration timeSpent;
  
  /// Current score
  final int score;

  /// Calculate completion percentage
  double get completionPercentage {
    if (totalQuestions == 0) return 0.0;
    return (correctCount + wrongCount) / totalQuestions;
  }

  /// Calculate accuracy percentage
  double get accuracyPercentage {
    final totalAnswered = correctCount + wrongCount;
    if (totalAnswered == 0) return 0.0;
    return correctCount / totalAnswered;
  }

  /// Check if exercise is completed
  bool get isCompleted => (correctCount + wrongCount) >= totalQuestions;

  /// Create a copy with updated fields
  ExerciseProgress copyWith({
    int? subject,
    String? type,
    int? currentIndex,
    int? totalQuestions,
    int? correctCount,
    int? wrongCount,
    Map<int, String>? answeredQuestions,
    DateTime? startTime,
    DateTime? endTime,
    Duration? timeSpent,
    int? score,
  }) {
    return ExerciseProgress(
      subject: subject ?? this.subject,
      type: type ?? this.type,
      currentIndex: currentIndex ?? this.currentIndex,
      totalQuestions: totalQuestions ?? this.totalQuestions,
      correctCount: correctCount ?? this.correctCount,
      wrongCount: wrongCount ?? this.wrongCount,
      answeredQuestions: answeredQuestions ?? this.answeredQuestions,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      timeSpent: timeSpent ?? this.timeSpent,
      score: score ?? this.score,
    );
  }

  @override
  List<Object?> get props => [
        subject,
        type,
        currentIndex,
        totalQuestions,
        correctCount,
        wrongCount,
        answeredQuestions,
        startTime,
        endTime,
        timeSpent,
        score,
      ];
}

/// Listen progress entity
class ListenProgress extends Equatable {
  const ListenProgress({
    required this.subject,
    required this.currentIndex,
    required this.totalQuestions,
    required this.playedQuestions,
    this.isPlaying = false,
    this.currentPosition = Duration.zero,
    this.totalDuration = Duration.zero,
  });

  /// Subject (1 for subject 1, 4 for subject 4)
  final int subject;
  
  /// Current question index
  final int currentIndex;
  
  /// Total number of questions
  final int totalQuestions;
  
  /// Map of question ID to play count
  final Map<int, int> playedQuestions;
  
  /// Whether audio is currently playing
  final bool isPlaying;
  
  /// Current playback position
  final Duration currentPosition;
  
  /// Total duration of current audio
  final Duration totalDuration;

  /// Calculate listening progress percentage
  double get progressPercentage {
    if (totalQuestions == 0) return 0.0;
    return playedQuestions.length / totalQuestions;
  }

  /// Create a copy with updated fields
  ListenProgress copyWith({
    int? subject,
    int? currentIndex,
    int? totalQuestions,
    Map<int, int>? playedQuestions,
    bool? isPlaying,
    Duration? currentPosition,
    Duration? totalDuration,
  }) {
    return ListenProgress(
      subject: subject ?? this.subject,
      currentIndex: currentIndex ?? this.currentIndex,
      totalQuestions: totalQuestions ?? this.totalQuestions,
      playedQuestions: playedQuestions ?? this.playedQuestions,
      isPlaying: isPlaying ?? this.isPlaying,
      currentPosition: currentPosition ?? this.currentPosition,
      totalDuration: totalDuration ?? this.totalDuration,
    );
  }

  @override
  List<Object?> get props => [
        subject,
        currentIndex,
        totalQuestions,
        playedQuestions,
        isPlaying,
        currentPosition,
        totalDuration,
      ];
}
