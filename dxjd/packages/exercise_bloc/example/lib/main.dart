import 'package:flutter/material.dart';
import 'package:exercise_bloc/exercise_bloc.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize the exercise module
  // await ExerciseModule.initialize(
  //   enableDebugMode: true,
  //   baseUrl: 'https://api.example.com',
  //   databasePath: 'exercise_example.db',
  // );

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Exercise BLoC Example',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const HomePage(),
    );
  }
}

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Exercise BLoC Example'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'Exercise BLoC Module Example',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 32),

            // Chapter Exercise Button
            ElevatedButton.icon(
              onPressed: () {
                // TODO: Navigate to exercise page
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('章节练习功能开发中...')),
                );
              },
              icon: const Icon(Icons.quiz),
              label: const Text('章节练习'),
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(200, 50),
              ),
            ),

            const SizedBox(height: 16),

            // Random Exercise Button
            ElevatedButton.icon(
              onPressed: () {
                // TODO: Navigate to exercise page
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('随机练习功能开发中...')),
                );
              },
              icon: const Icon(Icons.shuffle),
              label: const Text('随机练习'),
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(200, 50),
              ),
            ),

            const SizedBox(height: 16),

            // Error Exercise Button
            ElevatedButton.icon(
              onPressed: () {
                // TODO: Navigate to exercise page
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('错题练习功能开发中...')),
                );
              },
              icon: const Icon(Icons.error_outline),
              label: const Text('错题练习'),
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(200, 50),
              ),
            ),

            const SizedBox(height: 16),

            // Listen Mode Button
            ElevatedButton.icon(
              onPressed: () {
                // TODO: Navigate to exercise page
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('听题练习功能开发中...')),
                );
              },
              icon: const Icon(Icons.headphones),
              label: const Text('听题练习'),
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(200, 50),
              ),
            ),

            const SizedBox(height: 16),

            // Browse Mode Button
            ElevatedButton.icon(
              onPressed: () {
                // TODO: Navigate to exercise page
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('背题练习功能开发中...')),
                );
              },
              icon: const Icon(Icons.book),
              label: const Text('背题练习'),
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(200, 50),
              ),
            ),

            const SizedBox(height: 32),

            // Module Info
            Container(
              padding: const EdgeInsets.all(16),
              margin: const EdgeInsets.symmetric(horizontal: 32),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Column(
                children: [
                  Text(
                    '模块信息',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text('版本: 0.0.1'),
                  Text('状态: 开发中'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
