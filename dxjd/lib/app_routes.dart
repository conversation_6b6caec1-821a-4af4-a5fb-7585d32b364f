import 'package:component_library/component_library.dart';
import 'package:dxjd/splash_page.dart';
import 'package:dxjd/tab_container_screen.dart';
import 'package:get/get.dart';
import 'package:home/home.dart';
import 'package:login/login.dart';
import 'package:mine/mine.dart';

class AppRoutes {
  static const String splash = '/';
  static const String home = '/home';

  static const String settings = '/settings';
  static const String subOneTheoryVideo = '/sub_one_theory_video';
  static const String homeWebViewPath = '/homeWebViewPath';
  static const String wechatLogin = '/wechat_login';
  static const String homeStudyTypeSelect = '/home_study_type_select';
  static const String theoryVideoDetailPage = '/video_theory_detail';
  static const String cityListSelectPage = '/city_list_select';
  static const String examinationRoomSelectScreen =
      '/sub_two_switch_examination_room';
  static const String subTwoVipDetailPage = '/sub_two_vip_examination_room';
  static const String bindStudentPage = '/bind_student';
  static const String subThreePracticalVideoDetail = '/sub_three_practical_video';
  static const String subThreeVip45DetailPage =
      '/sub_three_vip_45_examination_room';
  static const String webView = '/web_view';
  static const String personalInfoPath = '/personalInfoPath';
  static const String myDocPath = '/my_doc';
  static const String accountManagePage = '/account_manage';
  static const String userServiceProtocol = '/user_service_protocol';
  static const String privacyPolicy = '/privacy_policy';
  static const String phoneLogin = '/phone_login';
  static const String passwordLogin = '/password_login';
  static const String forgetPassword = '/forget_password';
  static const String privacySettingsPage = '/privacy_setting';
  static const String personalInformationCollectionChecklistPage =
      '/personal_information_collection_checklist';
  static const String collectionDetailPage = '/collect_detail';
  static const String sharedListPage = '/shared_list';
  static const String settingBindPhonePage = '/setting_bind_phone';
  static const String modifyPassWordPage = '/modify_pass_word';
  static const String logOffAccountPage = '/logoff_account';
  static const String subTwoPracticalVideoDetail = '/sub_two_practical_video';
  static const String subThreeVipDetailPage = '/sub_three_vip_examination_room';
  static const String subOneVideoCatalogPage = '/video_theory_catalog';
  static const String subjectThreeVipRoadDetailScreen = '/subject_three_vip_road_detail_screen';
  static const String bringViewExamination = '/bring_view_examination';
  //地图
  static const String mapViewPage = '/map_view';
  static const String subjectThreeCatalogPage = '/subject_three_catalog_page';
  static const String detailPointMapPage = '/detail_point_map_page';
  static const String imageViewerPage = '/image_viewer_page';
  static const String certificateDetailPage = '/certificate_detail_page';
  //学车经验 页面
  static const String studyCarExperiencePage = '/study_car_experience_page';
  static const String studyCarExperienceRulePage = '/study_car_experience_rule_page';
  static const String couponPage = '/coupon_page';
  static const String dyStudyAttentionPage = '/dyStudyAttentionPage';
  static const String topicSelectPage = '/topic_select_page';
  static final routes = [
    GetPage(name: AppRoutes.home, page: () => TabContainerScreen()),
    GetPage(name: AppRoutes.splash, page: () => const SplashScreen()),
    GetPage(name: AppRoutes.wechatLogin, page: () => const WechatLoginPage()),
    GetPage(name: AppRoutes.homeStudyTypeSelect, page: () => const StudyTypeSelect()),
    GetPage(
        name: AppRoutes.subOneTheoryVideo,
        page: () => SubjectOneTheoryVideoScreen(),
       ),
    GetPage(
        name: AppRoutes.theoryVideoDetailPage,
        page: () => const TheoryVideoDetailScreen(),
       ),
    GetPage(
        name: AppRoutes.cityListSelectPage,
        page: () => const CityListCustomHeaderPage(),
       ),
    GetPage(
        name: AppRoutes.examinationRoomSelectScreen,
        page: () => ExaminationRoomSelectScreen()),
    GetPage(
        name: AppRoutes.mapViewPage,
        page: () => const MapViewPage()),
    GetPage(
        name: AppRoutes.subTwoVipDetailPage,
        page: () => const SubjectTwoVipVideoDetailScreen()),
    GetPage(
        name: AppRoutes.bindStudentPage, page: () => const BindStudentPageScreen()),
    GetPage(
        name: AppRoutes.subjectThreeCatalogPage, page: () =>  SubjectThreeCatalogPage()),
    GetPage(
        name: AppRoutes.subThreePracticalVideoDetail,
        page: () => const SubjectThreePracticalVideoDetailScreen()),
    GetPage(
        name: AppRoutes.subThreeVip45DetailPage,
        page: () => const SubjectThreeVip45VideoDetailScreen()),
    GetPage(
        name: AppRoutes.imageViewerPage,
        page: () => ImageViewerPage()),
    GetPage(name: AppRoutes.webView, page: () => HomeWebView()),
    GetPage(name: AppRoutes.personalInfoPath, page: () => const PersonalInfoPage()),
    GetPage(name: AppRoutes.myDocPath, page: () => const MyStudentDoc()),
    GetPage(name: AppRoutes.subjectThreeVipRoadDetailScreen, page: () => const SubjectThreeVipRoadDetailScreen()),
    GetPage(name: AppRoutes.bringViewExamination, page: () => const BringViewExaminationPage()),
    //设置页面
    GetPage(name: AppRoutes.settings, page: () => const SettingPage()),

    GetPage(
        name: AppRoutes.userServiceProtocol, page: () => const UserServiceProtocol()),
    GetPage(name: AppRoutes.privacyPolicy, page: () => const PrivacyProtocol()),
    GetPage(name: AppRoutes.phoneLogin, page: () => const PhoneLoginPage()),
    GetPage(name: AppRoutes.passwordLogin, page: () => const PassWordLogin()),
    GetPage(name: AppRoutes.forgetPassword, page: () => const ForgetPassWordPage()),
    GetPage(
        name: AppRoutes.privacySettingsPage, page: () => PrivacySettingPage()),
    GetPage(
        name: AppRoutes.personalInformationCollectionChecklistPage,
        page: () => PersonalInformationCollectionChecklistPage()),
    GetPage(
        name: AppRoutes.collectionDetailPage,
        page: () => CollectionDetailPage()),
    GetPage(name: AppRoutes.sharedListPage, page: () => const SharedListPage()),
    GetPage(name: AppRoutes.accountManagePage, page: () => const AccountManagePage()),
    GetPage(
        name: AppRoutes.settingBindPhonePage,
        page: () => const SettingBindPhonePage()),
    GetPage(
        name: AppRoutes.modifyPassWordPage, page: () => const SettingModifyPwPage()),
    GetPage(name: AppRoutes.logOffAccountPage, page: () => const LogoffAccountPage()),
    GetPage(
        name: AppRoutes.subTwoPracticalVideoDetail,
        page: () => const SubjectTwoPracticalVideoDetailScreen()),
    GetPage(
        name: AppRoutes.subThreeVipDetailPage,
        page: () => const SubjectThreeVipVideoDetailScreen()),
    GetPage(
        name: AppRoutes.subOneVideoCatalogPage,
        page: () => const TheoryVideoCatalogPage()),
    GetPage(
        name: AppRoutes.detailPointMapPage,
        page: () => const DetailPointMapPage()),
    GetPage(
        name: AppRoutes.certificateDetailPage,
        page: () =>  const CertificateDetailPage()),
    //学车经验 页面
    GetPage(
        name: AppRoutes.studyCarExperiencePage,
        page: () => StudyCarExperiencePage()),
    //学车经验 规则页面
    GetPage(
        name: AppRoutes.studyCarExperienceRulePage,
        page: () => const StudyCarExperienceRulePage()),
    // GetPage(
    //     name: AppRoutes.dyStudyAttentionPage,
    //     page: () => const DyStudyAttentionPage(timingRepository: ,)),
    GetPage(
        name: AppRoutes.couponPage,
        page: () => const CouponPage()),

    GetPage(
        name: AppRoutes.topicSelectPage,
        page: () => const TopicSelectPage()),

  ];
}
// 中间件类
// class AuthMiddleware extends GetMiddleware {
//   @override
//   RouteSettings? redirect(String? route) {
//     // 检查用户是否已登录
//    // 这里替换为实际的登录状态检查逻辑
//     if (! MainController.isExitToken) {
//       // 如果未登录，跳转到登录页面
//      return const RouteSettings(name: AppRoutes.wechatLogin);
//     }
//     return null; // 已登录，继续跳转
//   }
// }