import '../entities/exercise_progress.dart';

/// Abstract repository for progress data operations
abstract class ProgressRepository {
  /// Save exercise progress
  Future<void> saveExerciseProgress(ExerciseProgress progress);

  /// Get exercise progress
  Future<ExerciseProgress?> getExerciseProgress({
    required int subject,
    required String type,
    String? userId,
  });

  /// Save listen progress
  Future<void> saveListenProgress(ListenProgress progress);

  /// Get listen progress
  Future<ListenProgress?> getListenProgress({
    required int subject,
    String? userId,
  });

  /// Delete progress data
  Future<void> deleteProgress({
    required int subject,
    required String type,
    String? userId,
  });

  /// Get all progress records for a user
  Future<List<ExerciseProgress>> getAllExerciseProgress({
    String? userId,
  });

  /// Get progress statistics
  Future<Map<String, dynamic>> getProgressStats({
    required int subject,
    String? userId,
  });

  /// Sync progress with server
  Future<void> syncProgress({
    String? userId,
  });

  /// Clear all local progress data
  Future<void> clearAllProgress();
}
