import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/exercise/exercise_bloc.dart';
import '../bloc/exercise/exercise_event.dart';
import '../bloc/exercise/exercise_state.dart';

/// Exercise view for doing questions
class ExerciseView extends StatefulWidget {
  const ExerciseView({
    super.key,
    required this.subject,
    required this.type,
    this.sortId,
    this.mainId,
    this.childId,
    this.luid,
    this.ids,
  });

  final int subject;
  final String type;
  final int? sortId;
  final int? mainId;
  final int? childId;
  final int? luid;
  final List<int>? ids;

  @override
  State<ExerciseView> createState() => _ExerciseViewState();
}

class _ExerciseViewState extends State<ExerciseView> {
  @override
  void initState() {
    super.initState();
    // Load questions when view is initialized
    context.read<ExerciseBloc>().add(LoadQuestions(
      subject: widget.subject,
      type: widget.type,
      sortId: widget.sortId,
      mainId: widget.mainId,
      childId: widget.childId,
      luid: widget.luid,
      ids: widget.ids,
    ));
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ExerciseBloc, ExerciseState>(
      builder: (context, state) {
        if (state is ExerciseInitial) {
          return const Center(
            child: Text('准备加载题目...'),
          );
        }

        if (state is ExerciseLoading) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('正在加载题目...'),
              ],
            ),
          );
        }

        if (state is ExerciseError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 16),
                Text(
                  '加载失败',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  state.message,
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    context.read<ExerciseBloc>().add(LoadQuestions(
                      subject: widget.subject,
                      type: widget.type,
                      sortId: widget.sortId,
                      mainId: widget.mainId,
                      childId: widget.childId,
                      luid: widget.luid,
                      ids: widget.ids,
                    ));
                  },
                  child: const Text('重试'),
                ),
              ],
            ),
          );
        }

        if (state is ExerciseLoaded || state is QuestionAnswered) {
          final questions = state is ExerciseLoaded 
              ? state.questions 
              : (state as QuestionAnswered).questions;
          final currentIndex = state is ExerciseLoaded 
              ? state.currentIndex 
              : (state as QuestionAnswered).currentIndex;
          final progress = state is ExerciseLoaded 
              ? state.progress 
              : (state as QuestionAnswered).progress;

          if (questions.isEmpty) {
            return const Center(
              child: Text('没有找到题目'),
            );
          }

          final currentQuestion = questions[currentIndex];

          return Column(
            children: [
              // Progress bar
              LinearProgressIndicator(
                value: (currentIndex + 1) / questions.length,
                backgroundColor: Colors.grey[300],
                valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
              ),
              
              // Question info
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '第 ${currentIndex + 1} 题 / 共 ${questions.length} 题',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    Text(
                      '正确: ${progress.correctCount} | 错误: ${progress.wrongCount}',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
              
              // Question content
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Question title
                      Text(
                        currentQuestion.title,
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Question image (if available)
                      if (currentQuestion.imagePath != null)
                        Container(
                          width: double.infinity,
                          height: 200,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Center(
                            child: Text('题目图片'),
                          ),
                        ),
                      
                      const SizedBox(height: 16),
                      
                      // Options
                      ...currentQuestion.options.map((option) {
                        final isSelected = currentQuestion.userAnswer?.contains(option.key) ?? false;
                        final isCorrect = currentQuestion.correctAnswer.contains(option.key);
                        final showResult = currentQuestion.isAnswered;
                        
                        Color? backgroundColor;
                        if (showResult) {
                          if (isSelected && isCorrect) {
                            backgroundColor = Colors.green[100];
                          } else if (isSelected && !isCorrect) {
                            backgroundColor = Colors.red[100];
                          } else if (!isSelected && isCorrect) {
                            backgroundColor = Colors.green[50];
                          }
                        } else if (isSelected) {
                          backgroundColor = Colors.blue[100];
                        }
                        
                        return Container(
                          margin: const EdgeInsets.only(bottom: 8),
                          child: InkWell(
                            onTap: currentQuestion.isAnswered 
                                ? null 
                                : () => _selectOption(option.key),
                            child: Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: backgroundColor,
                                border: Border.all(
                                  color: isSelected ? Colors.blue : Colors.grey,
                                  width: isSelected ? 2 : 1,
                                ),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                children: [
                                  Text(
                                    '${option.key.toUpperCase()}.',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(option.text),
                                  ),
                                  if (showResult && isCorrect)
                                    const Icon(
                                      Icons.check_circle,
                                      color: Colors.green,
                                    ),
                                  if (showResult && isSelected && !isCorrect)
                                    const Icon(
                                      Icons.cancel,
                                      color: Colors.red,
                                    ),
                                ],
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                      
                      // Answer explanation (if answered)
                      if (state is QuestionAnswered && state.explanation != null)
                        Container(
                          margin: const EdgeInsets.only(top: 16),
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                '答案解析:',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(state.explanation!),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ),
              
              // Navigation buttons
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    ElevatedButton(
                      onPressed: currentIndex > 0 
                          ? () => context.read<ExerciseBloc>().add(const PreviousQuestion())
                          : null,
                      child: const Text('上一题'),
                    ),
                    ElevatedButton(
                      onPressed: currentIndex < questions.length - 1 
                          ? () => context.read<ExerciseBloc>().add(const NextQuestion())
                          : null,
                      child: const Text('下一题'),
                    ),
                  ],
                ),
              ),
            ],
          );
        }

        if (state is ExerciseCompleted) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.check_circle,
                  size: 64,
                  color: Colors.green,
                ),
                const SizedBox(height: 16),
                Text(
                  '练习完成！',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  '正确: ${state.progress.correctCount} | 错误: ${state.progress.wrongCount}',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    context.read<ExerciseBloc>().add(const ResetExercise());
                  },
                  child: const Text('重新开始'),
                ),
              ],
            ),
          );
        }

        return const Center(
          child: Text('未知状态'),
        );
      },
    );
  }

  /// Select option
  void _selectOption(String optionKey) {
    final bloc = context.read<ExerciseBloc>();
    final state = bloc.state;
    
    if (state is ExerciseLoaded) {
      bloc.add(AnswerQuestion(
        questionIndex: state.currentIndex,
        answer: optionKey,
      ));
    }
  }
}
