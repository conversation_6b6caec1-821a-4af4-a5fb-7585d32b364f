import '../../domain/entities/exercise_progress.dart';
import '../../domain/repositories/progress_repository.dart';
import '../datasources/progress_local_datasource.dart';
import '../datasources/progress_remote_datasource.dart';
import '../models/progress_model.dart';

/// Implementation of ProgressRepository
/// Manages exercise progress data with local caching and remote synchronization
class ProgressRepositoryImpl implements ProgressRepository {
  ProgressRepositoryImpl({
    required ProgressLocalDataSource localDataSource,
    required ProgressRemoteDataSource remoteDataSource,
  })  : _localDataSource = localDataSource,
        _remoteDataSource = remoteDataSource;

  final ProgressLocalDataSource _localDataSource;
  final ProgressRemoteDataSource _remoteDataSource;

  @override
  Future<ExerciseProgress> getProgress({
    required int subject,
    required String type,
    String? userId,
    int? sortId,
  }) async {
    try {
      // Try to get from local first for better performance
      final localProgress = await _localDataSource.getProgress(
        subject: subject,
        type: type,
        userId: userId,
        sortId: sortId,
      );

      if (localProgress != null) {
        // Try to sync with remote in background
        _syncProgressInBackground(
          subject: subject,
          type: type,
          userId: userId,
          sortId: sortId,
        );
        
        return localProgress.toEntity();
      }

      // If no local data, try remote
      final remoteProgress = await _remoteDataSource.getProgress(
        subject: subject,
        type: type,
        userId: userId,
        sortId: sortId,
      );

      if (remoteProgress != null) {
        // Cache to local
        await _localDataSource.saveProgress(remoteProgress);
        return remoteProgress.toEntity();
      }

      // Return default progress if no data found
      return ExerciseProgress.initial();
    } catch (e) {
      // Fallback to local data if remote fails
      try {
        final localProgress = await _localDataSource.getProgress(
          subject: subject,
          type: type,
          userId: userId,
          sortId: sortId,
        );

        return localProgress?.toEntity() ?? ExerciseProgress.initial();
      } catch (localError) {
        // Return default progress if all fails
        return ExerciseProgress.initial();
      }
    }
  }

  @override
  Future<void> saveProgress(ExerciseProgress progress) async {
    try {
      final progressModel = ProgressModel.fromEntity(progress);
      
      // Save to local first
      await _localDataSource.saveProgress(progressModel);

      // Try to sync with remote
      try {
        await _remoteDataSource.saveProgress(progressModel);
      } catch (remoteError) {
        // Log remote error but don't fail the operation
        // The data is safely stored locally and can be synced later
      }
    } catch (e) {
      throw Exception('Failed to save progress: $e');
    }
  }

  @override
  Future<void> updateProgress({
    required int subject,
    required String type,
    required int totalQuestions,
    required int answeredQuestions,
    required int correctAnswers,
    required int wrongAnswers,
    required Duration timeSpent,
    String? userId,
    int? sortId,
  }) async {
    try {
      // Get current progress
      final currentProgress = await getProgress(
        subject: subject,
        type: type,
        userId: userId,
        sortId: sortId,
      );

      // Update progress
      final updatedProgress = currentProgress.copyWith(
        totalQuestions: totalQuestions,
        answeredQuestions: answeredQuestions,
        correctCount: correctAnswers,
        wrongCount: wrongAnswers,
        timeSpent: timeSpent,
        lastUpdated: DateTime.now(),
      );

      // Save updated progress
      await saveProgress(updatedProgress);
    } catch (e) {
      throw Exception('Failed to update progress: $e');
    }
  }

  @override
  Future<void> resetProgress({
    required int subject,
    required String type,
    String? userId,
    int? sortId,
  }) async {
    try {
      // Create fresh progress
      final freshProgress = ExerciseProgress.initial().copyWith(
        subject: subject,
        type: type,
        userId: userId,
        sortId: sortId,
        lastUpdated: DateTime.now(),
      );

      await saveProgress(freshProgress);
    } catch (e) {
      throw Exception('Failed to reset progress: $e');
    }
  }

  @override
  Future<List<ExerciseProgress>> getAllProgress({
    String? userId,
  }) async {
    try {
      // Try remote first for comprehensive data
      final remoteProgressList = await _remoteDataSource.getAllProgress(
        userId: userId,
      );

      if (remoteProgressList.isNotEmpty) {
        // Cache to local
        for (final progress in remoteProgressList) {
          await _localDataSource.saveProgress(progress);
        }
        
        return remoteProgressList.map((model) => model.toEntity()).toList();
      }

      // Fallback to local
      final localProgressList = await _localDataSource.getAllProgress(
        userId: userId,
      );

      return localProgressList.map((model) => model.toEntity()).toList();
    } catch (e) {
      // Fallback to local data if remote fails
      try {
        final localProgressList = await _localDataSource.getAllProgress(
          userId: userId,
        );

        return localProgressList.map((model) => model.toEntity()).toList();
      } catch (localError) {
        throw Exception(
            'Failed to get all progress: Remote error: $e, Local error: $localError');
      }
    }
  }

  @override
  Future<Map<String, dynamic>> getStatistics({
    required int subject,
    String? userId,
  }) async {
    try {
      return await _localDataSource.getStatistics(
        subject: subject,
        userId: userId,
      );
    } catch (e) {
      throw Exception('Failed to get statistics: $e');
    }
  }

  @override
  Future<void> syncWithServer({
    String? userId,
  }) async {
    try {
      await _remoteDataSource.syncData(userId: userId);
    } catch (e) {
      throw Exception('Failed to sync with server: $e');
    }
  }

  @override
  Future<void> clearProgress({
    String? userId,
  }) async {
    try {
      await _localDataSource.clearProgress(userId: userId);
      
      // Try to clear remote data as well
      try {
        await _remoteDataSource.clearProgress(userId: userId);
      } catch (remoteError) {
        // Log remote error but don't fail the operation
      }
    } catch (e) {
      throw Exception('Failed to clear progress: $e');
    }
  }

  /// Sync progress with remote in background without blocking the UI
  Future<void> _syncProgressInBackground({
    required int subject,
    required String type,
    String? userId,
    int? sortId,
  }) async {
    try {
      final remoteProgress = await _remoteDataSource.getProgress(
        subject: subject,
        type: type,
        userId: userId,
        sortId: sortId,
      );

      if (remoteProgress != null) {
        await _localDataSource.saveProgress(remoteProgress);
      }
    } catch (e) {
      // Silently fail background sync
    }
  }
}
