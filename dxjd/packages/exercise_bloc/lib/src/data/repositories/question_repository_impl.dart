import '../../domain/entities/question_entity.dart';
import '../../domain/repositories/question_repository.dart';
import '../datasources/question_remote_datasource.dart';
import '../datasources/question_local_datasource.dart';
import '../models/question_model.dart';

/// Implementation of QuestionRepository
class QuestionRepositoryImpl implements QuestionRepository {
  QuestionRepositoryImpl({
    required QuestionRemoteDataSource remoteDataSource,
    required QuestionLocalDataSource localDataSource,
  })  : _remoteDataSource = remoteDataSource,
        _localDataSource = localDataSource;

  final QuestionRemoteDataSource _remoteDataSource;
  final QuestionLocalDataSource _localDataSource;

  @override
  Future<List<QuestionEntity>> getQuestions({
    required int subject,
    required String type,
    List<int>? ids,
    int? sortId,
    int? mainId,
    int? childId,
    int? luid,
  }) async {
    try {
      // Try to get from remote first
      final remoteQuestions = await _remoteDataSource.getQuestions(
        subject: subject,
        type: type,
        ids: ids,
        sortId: sortId,
        mainId: mainId,
        childId: childId,
        luid: luid,
      );

      // Cache to local database
      if (remoteQuestions.isNotEmpty) {
        await _localDataSource.saveQuestions(remoteQuestions);
      }

      // Convert to entities
      return remoteQuestions.map((model) => model.toEntity()).toList();
    } catch (e) {
      // Fallback to local data if remote fails
      try {
        final localQuestions = await _localDataSource.getQuestions(
          subject: subject,
          type: type,
          ids: ids,
          sortId: sortId,
          mainId: mainId,
          childId: childId,
          luid: luid,
        );

        return localQuestions.map((model) => model.toEntity()).toList();
      } catch (localError) {
        throw Exception('Failed to load questions: Remote error: $e, Local error: $localError');
      }
    }
  }

  @override
  Future<QuestionEntity?> getQuestionById(int id) async {
    try {
      // Try local first for single question
      final localQuestions = await _localDataSource.getQuestions(
        subject: 1, // Default subject
        type: 'single',
        ids: [id],
      );

      if (localQuestions.isNotEmpty) {
        return localQuestions.first.toEntity();
      }

      // If not found locally, try remote
      final remoteQuestions = await _remoteDataSource.getQuestions(
        subject: 1,
        type: 'single',
        ids: [id],
      );

      if (remoteQuestions.isNotEmpty) {
        // Cache to local
        await _localDataSource.saveQuestions(remoteQuestions);
        return remoteQuestions.first.toEntity();
      }

      return null;
    } catch (e) {
      throw Exception('Failed to get question by ID: $e');
    }
  }

  @override
  Future<void> saveAnswer({
    required int questionId,
    required String answer,
    required bool isCorrect,
    required int subject,
  }) async {
    try {
      // Save to local first
      await _localDataSource.saveAnswer(
        questionId: questionId,
        answer: answer,
        isCorrect: isCorrect,
        subject: subject,
      );

      // Try to sync with remote
      try {
        await _remoteDataSource.saveAnswer(
          questionId: questionId,
          answer: answer,
          isCorrect: isCorrect,
          subject: subject,
        );
      } catch (remoteError) {
        // Log remote error but don't fail the operation
        // The data is safely stored locally and can be synced later
        print('Failed to sync answer to remote: $remoteError');
      }
    } catch (e) {
      throw Exception('Failed to save answer: $e');
    }
  }

  @override
  Future<List<QuestionEntity>> getErrorQuestions({
    required int subject,
    String? userId,
  }) async {
    try {
      // Try remote first
      final remoteQuestions = await _remoteDataSource.getErrorQuestions(
        subject: subject,
        userId: userId,
      );

      return remoteQuestions.map((model) => model.toEntity()).toList();
    } catch (e) {
      // Fallback to local
      try {
        final localQuestions = await _localDataSource.getErrorQuestions(
          subject: subject,
          userId: userId,
        );

        return localQuestions.map((model) => model.toEntity()).toList();
      } catch (localError) {
        throw Exception('Failed to load error questions: Remote error: $e, Local error: $localError');
      }
    }
  }

  @override
  Future<List<QuestionEntity>> getCollectedQuestions({
    required int subject,
    String? userId,
  }) async {
    try {
      // Try remote first
      final remoteQuestions = await _remoteDataSource.getCollectedQuestions(
        subject: subject,
        userId: userId,
      );

      return remoteQuestions.map((model) => model.toEntity()).toList();
    } catch (e) {
      // Fallback to local
      try {
        final localQuestions = await _localDataSource.getCollectedQuestions(
          subject: subject,
          userId: userId,
        );

        return localQuestions.map((model) => model.toEntity()).toList();
      } catch (localError) {
        throw Exception('Failed to load collected questions: Remote error: $e, Local error: $localError');
      }
    }
  }

  @override
  Future<void> toggleCollection({
    required int questionId,
    required bool isCollected,
    String? userId,
  }) async {
    try {
      // Save to local first
      await _localDataSource.toggleCollection(
        questionId: questionId,
        isCollected: isCollected,
        userId: userId,
      );

      // Try to sync with remote
      try {
        await _remoteDataSource.toggleCollection(
          questionId: questionId,
          isCollected: isCollected,
          userId: userId,
        );
      } catch (remoteError) {
        print('Failed to sync collection status to remote: $remoteError');
      }
    } catch (e) {
      throw Exception('Failed to toggle collection: $e');
    }
  }

  @override
  Future<void> deleteQuestion({
    required int questionId,
    required String type,
    String? userId,
  }) async {
    try {
      // Delete from local first
      await _localDataSource.deleteQuestion(
        questionId: questionId,
        type: type,
        userId: userId,
      );

      // Try to sync with remote
      try {
        await _remoteDataSource.deleteQuestion(
          questionId: questionId,
          type: type,
          userId: userId,
        );
      } catch (remoteError) {
        print('Failed to sync deletion to remote: $remoteError');
      }
    } catch (e) {
      throw Exception('Failed to delete question: $e');
    }
  }

  @override
  Future<void> saveListenProgress({
    required int questionId,
    required int playCount,
    required DateTime playTime,
    String? userId,
  }) async {
    try {
      // Save to local first
      await _localDataSource.saveListenProgress(
        questionId: questionId,
        playCount: playCount,
        playTime: playTime,
        userId: userId,
      );

      // Try to sync with remote
      try {
        await _remoteDataSource.saveListenProgress(
          questionId: questionId,
          playCount: playCount,
          playTime: playTime,
          userId: userId,
        );
      } catch (remoteError) {
        print('Failed to sync listen progress to remote: $remoteError');
      }
    } catch (e) {
      throw Exception('Failed to save listen progress: $e');
    }
  }

  @override
  Future<List<QuestionEntity>> getQuestionsBySort({
    required int sortId,
    required int subject,
    String? userId,
  }) async {
    return getQuestions(
      subject: subject,
      type: 'chapter',
      sortId: sortId,
    );
  }

  @override
  Future<List<QuestionEntity>> searchQuestions({
    required String keyword,
    required int subject,
  }) async {
    try {
      // Get all questions for the subject
      final questions = await getQuestions(
        subject: subject,
        type: 'all',
      );

      // Filter by keyword
      final filteredQuestions = questions.where((question) {
        final titleMatch = question.title.toLowerCase().contains(keyword.toLowerCase());
        final optionMatch = question.options.any((option) =>
            option.text.toLowerCase().contains(keyword.toLowerCase()));
        final explanationMatch = question.explanation?.toLowerCase().contains(keyword.toLowerCase()) ?? false;
        
        return titleMatch || optionMatch || explanationMatch;
      }).toList();

      return filteredQuestions;
    } catch (e) {
      throw Exception('Failed to search questions: $e');
    }
  }

  @override
  Future<List<QuestionEntity>> getRandomQuestions({
    required int subject,
    required int count,
    List<int>? excludeIds,
  }) async {
    try {
      // Get all questions for the subject
      final allQuestions = await getQuestions(
        subject: subject,
        type: 'all',
      );

      // Filter out excluded IDs
      var availableQuestions = allQuestions;
      if (excludeIds != null && excludeIds.isNotEmpty) {
        availableQuestions = allQuestions
            .where((q) => !excludeIds.contains(q.id))
            .toList();
      }

      // Shuffle and take the requested count
      availableQuestions.shuffle();
      final randomQuestions = availableQuestions.take(count).toList();

      return randomQuestions;
    } catch (e) {
      throw Exception('Failed to get random questions: $e');
    }
  }

  @override
  Future<void> updateQuestionStats({
    required int questionId,
    int? correctCount,
    int? errorCount,
    int? answerCount,
  }) async {
    // This would typically update statistics in the database
    // For now, we'll just log the update
    print('Updating stats for question $questionId: '
        'correct: $correctCount, error: $errorCount, answer: $answerCount');
  }

  @override
  Future<void> batchSaveAnswers({
    required List<QuestionEntity> questions,
    required int subject,
    String? userId,
  }) async {
    try {
      for (final question in questions) {
        if (question.isAnswered && question.userAnswer != null) {
          await saveAnswer(
            questionId: question.id,
            answer: question.userAnswer!,
            isCorrect: question.isCorrect,
            subject: subject,
          );
        }
      }
    } catch (e) {
      throw Exception('Failed to batch save answers: $e');
    }
  }

  @override
  Future<Map<String, int>> getQuestionStats({
    required int subject,
    String? userId,
  }) async {
    try {
      return await _localDataSource.getStatistics(
        subject: subject,
        userId: userId,
      );
    } catch (e) {
      throw Exception('Failed to get question stats: $e');
    }
  }

  @override
  Future<void> syncWithServer({
    String? userId,
  }) async {
    try {
      await _remoteDataSource.syncData(userId: userId);
    } catch (e) {
      throw Exception('Failed to sync with server: $e');
    }
  }

  @override
  Future<void> clearCache() async {
    try {
      await _localDataSource.clearCache();
    } catch (e) {
      throw Exception('Failed to clear cache: $e');
    }
  }
}
