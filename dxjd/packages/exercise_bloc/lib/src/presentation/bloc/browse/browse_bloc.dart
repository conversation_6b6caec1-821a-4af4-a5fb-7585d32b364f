import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../domain/entities/question_entity.dart';
import '../../../domain/usecases/load_questions.dart';
import '../../../domain/repositories/question_repository.dart';
import 'browse_event.dart';
import 'browse_state.dart';

/// BLoC for managing browse state
class BrowseBloc extends Bloc<BrowseEvent, BrowseState> {
  BrowseBloc({
    required LoadQuestions loadQuestions,
    required QuestionRepository questionRepository,
  })  : _loadQuestions = loadQuestions,
        _questionRepository = questionRepository,
        super(const BrowseInitial()) {
    on<LoadBrowseQuestions>(_onLoadBrowseQuestions);
    on<JumpToBrowseQuestion>(_onJumpToBrowseQuestion);
    on<NextBrowseQuestion>(_onNextBrowseQuestion);
    on<PreviousBrowseQuestion>(_onPreviousBrowseQuestion);
    on<ToggleBrowseCollection>(_onToggleBrowseCollection);
    on<DeleteBrowseQuestion>(_onDeleteBrowseQuestion);
    on<ToggleAnswerVisibility>(_onToggleAnswerVisibility);
    on<FilterQuestions>(_onFilterQuestions);
    on<SearchBrowseQuestions>(_onSearchBrowseQuestions);
    on<ClearBrowseSearch>(_onClearBrowseSearch);
    on<ResetBrowse>(_onResetBrowse);
    on<LoadBrowseErrorQuestions>(_onLoadBrowseErrorQuestions);
    on<LoadBrowseCollectedQuestions>(_onLoadBrowseCollectedQuestions);
  }

  final LoadQuestions _loadQuestions;
  final QuestionRepository _questionRepository;

  /// Handle loading questions for browsing
  Future<void> _onLoadBrowseQuestions(
    LoadBrowseQuestions event,
    Emitter<BrowseState> emit,
  ) async {
    emit(const BrowseLoading());

    try {
      final params = LoadQuestionsParams(
        subject: event.subject,
        type: event.type,
        ids: event.ids,
        sortId: event.sortId,
        mainId: event.mainId,
        childId: event.childId,
        luid: event.luid,
      );

      final questions = await _loadQuestions(params);

      if (questions.isEmpty) {
        emit(const BrowseError('No questions found'));
        return;
      }

      emit(BrowseLoaded(
        questions: questions,
        filteredQuestions: questions,
        currentIndex: 0,
        subject: event.subject,
        type: event.type,
        showAnswers: true,
        canGoNext: questions.length > 1,
        canGoPrevious: false,
      ));
    } catch (e) {
      emit(BrowseError('Failed to load questions: ${e.toString()}'));
    }
  }

  /// Handle jump to specific question
  void _onJumpToBrowseQuestion(
    JumpToBrowseQuestion event,
    Emitter<BrowseState> emit,
  ) {
    final currentState = state;
    if (currentState is! BrowseLoaded) return;

    if (event.index >= 0 &&
        event.index < currentState.filteredQuestions.length) {
      emit(currentState.copyWith(
        currentIndex: event.index,
        canGoNext: event.index < currentState.filteredQuestions.length - 1,
        canGoPrevious: event.index > 0,
      ));
    }
  }

  /// Handle next question navigation
  void _onNextBrowseQuestion(
    NextBrowseQuestion event,
    Emitter<BrowseState> emit,
  ) {
    final currentState = state;
    if (currentState is! BrowseLoaded) return;

    if (currentState.currentIndex < currentState.filteredQuestions.length - 1) {
      final newIndex = currentState.currentIndex + 1;
      emit(currentState.copyWith(
        currentIndex: newIndex,
        canGoNext: newIndex < currentState.filteredQuestions.length - 1,
        canGoPrevious: true,
      ));
    }
  }

  /// Handle previous question navigation
  void _onPreviousBrowseQuestion(
    PreviousBrowseQuestion event,
    Emitter<BrowseState> emit,
  ) {
    final currentState = state;
    if (currentState is! BrowseLoaded) return;

    if (currentState.currentIndex > 0) {
      final newIndex = currentState.currentIndex - 1;
      emit(currentState.copyWith(
        currentIndex: newIndex,
        canGoNext: true,
        canGoPrevious: newIndex > 0,
      ));
    }
  }

  /// Handle toggle collection
  Future<void> _onToggleBrowseCollection(
    ToggleBrowseCollection event,
    Emitter<BrowseState> emit,
  ) async {
    final currentState = state;
    if (currentState is! BrowseLoaded) return;

    if (event.questionIndex < 0 ||
        event.questionIndex >= currentState.filteredQuestions.length) {
      return;
    }

    try {
      final question = currentState.filteredQuestions[event.questionIndex];
      final newCollectionStatus = !question.isCollected;

      await _questionRepository.toggleCollection(
        questionId: question.id,
        isCollected: newCollectionStatus,
      );

      // Update the question in both lists
      final updatedQuestion = question.copyWith(
        isCollected: newCollectionStatus,
      );

      // Update main questions list
      final updatedQuestions =
          List<QuestionEntity>.from(currentState.questions);
      final mainIndex = updatedQuestions.indexWhere((q) => q.id == question.id);
      if (mainIndex >= 0) {
        updatedQuestions[mainIndex] = updatedQuestion;
      }

      // Update filtered questions list
      final updatedFilteredQuestions =
          List<QuestionEntity>.from(currentState.filteredQuestions);
      updatedFilteredQuestions[event.questionIndex] = updatedQuestion;

      emit(BrowseCollectionToggled(
        questions: updatedQuestions,
        filteredQuestions: updatedFilteredQuestions,
        currentIndex: currentState.currentIndex,
        subject: currentState.subject,
        type: currentState.type,
        isCollected: newCollectionStatus,
        showAnswers: currentState.showAnswers,
      ));
    } catch (e) {
      emit(BrowseError('Failed to toggle collection: ${e.toString()}'));
    }
  }

  /// Handle delete question
  Future<void> _onDeleteBrowseQuestion(
    DeleteBrowseQuestion event,
    Emitter<BrowseState> emit,
  ) async {
    final currentState = state;
    if (currentState is! BrowseLoaded) return;

    if (event.questionIndex < 0 ||
        event.questionIndex >= currentState.filteredQuestions.length) {
      return;
    }

    try {
      final question = currentState.filteredQuestions[event.questionIndex];

      await _questionRepository.deleteQuestion(
        questionId: question.id,
        type: currentState.type,
      );

      // Remove from both lists
      final updatedQuestions =
          List<QuestionEntity>.from(currentState.questions);
      updatedQuestions.removeWhere((q) => q.id == question.id);

      final updatedFilteredQuestions =
          List<QuestionEntity>.from(currentState.filteredQuestions);
      updatedFilteredQuestions.removeAt(event.questionIndex);

      if (updatedFilteredQuestions.isEmpty) {
        emit(const BrowseError('No more questions available'));
        return;
      }

      // Adjust current index if necessary
      var newIndex = currentState.currentIndex;
      if (newIndex >= updatedFilteredQuestions.length) {
        newIndex = updatedFilteredQuestions.length - 1;
      }

      emit(BrowseQuestionDeleted(
        questions: updatedQuestions,
        filteredQuestions: updatedFilteredQuestions,
        currentIndex: newIndex,
        subject: currentState.subject,
        type: currentState.type,
        showAnswers: currentState.showAnswers,
      ));
    } catch (e) {
      emit(BrowseError('Failed to delete question: ${e.toString()}'));
    }
  }

  /// Handle toggle answer visibility
  void _onToggleAnswerVisibility(
    ToggleAnswerVisibility event,
    Emitter<BrowseState> emit,
  ) {
    final currentState = state;
    if (currentState is! BrowseLoaded) return;

    emit(currentState.copyWith(
      showAnswers: !currentState.showAnswers,
    ));
  }

  /// Handle filter questions
  void _onFilterQuestions(
    FilterQuestions event,
    Emitter<BrowseState> emit,
  ) {
    final currentState = state;
    if (currentState is! BrowseLoaded) return;

    final filteredQuestions = _applyFilters(
      currentState.questions,
      questionType: event.questionType,
      showAnswered: event.showAnswered,
      showUnanswered: event.showUnanswered,
      showCorrect: event.showCorrect,
      showIncorrect: event.showIncorrect,
      searchKeyword: currentState.searchKeyword,
    );

    emit(currentState.copyWith(
      filteredQuestions: filteredQuestions,
      currentIndex: 0,
      questionTypeFilter: event.questionType,
      showAnswered: event.showAnswered,
      showUnanswered: event.showUnanswered,
      showCorrect: event.showCorrect,
      showIncorrect: event.showIncorrect,
      canGoNext: filteredQuestions.length > 1,
      canGoPrevious: false,
    ));
  }

  /// Handle search questions
  void _onSearchBrowseQuestions(
    SearchBrowseQuestions event,
    Emitter<BrowseState> emit,
  ) {
    final currentState = state;
    if (currentState is! BrowseLoaded) return;

    final filteredQuestions = _applyFilters(
      currentState.questions,
      questionType: currentState.questionTypeFilter,
      showAnswered: currentState.showAnswered,
      showUnanswered: currentState.showUnanswered,
      showCorrect: currentState.showCorrect,
      showIncorrect: currentState.showIncorrect,
      searchKeyword: event.keyword,
    );

    emit(currentState.copyWith(
      filteredQuestions: filteredQuestions,
      currentIndex: 0,
      searchKeyword: event.keyword,
      canGoNext: filteredQuestions.length > 1,
      canGoPrevious: false,
    ));
  }

  /// Handle clear search
  void _onClearBrowseSearch(
    ClearBrowseSearch event,
    Emitter<BrowseState> emit,
  ) {
    final currentState = state;
    if (currentState is! BrowseLoaded) return;

    final filteredQuestions = _applyFilters(
      currentState.questions,
      questionType: currentState.questionTypeFilter,
      showAnswered: currentState.showAnswered,
      showUnanswered: currentState.showUnanswered,
      showCorrect: currentState.showCorrect,
      showIncorrect: currentState.showIncorrect,
      searchKeyword: '',
    );

    emit(currentState.copyWith(
      filteredQuestions: filteredQuestions,
      currentIndex: 0,
      searchKeyword: '',
      canGoNext: filteredQuestions.length > 1,
      canGoPrevious: false,
    ));
  }

  /// Handle reset browse
  void _onResetBrowse(
    ResetBrowse event,
    Emitter<BrowseState> emit,
  ) {
    emit(const BrowseInitial());
  }

  /// Handle load error questions
  Future<void> _onLoadBrowseErrorQuestions(
    LoadBrowseErrorQuestions event,
    Emitter<BrowseState> emit,
  ) async {
    emit(const BrowseLoading());

    try {
      final questions = await _questionRepository.getErrorQuestions(
        subject: event.subject,
      );

      if (questions.isEmpty) {
        emit(const BrowseError('No error questions found'));
        return;
      }

      emit(BrowseLoaded(
        questions: questions,
        filteredQuestions: questions,
        currentIndex: 0,
        subject: event.subject,
        type: 'error',
        showAnswers: true,
        canGoNext: questions.length > 1,
        canGoPrevious: false,
      ));
    } catch (e) {
      emit(BrowseError('Failed to load error questions: ${e.toString()}'));
    }
  }

  /// Handle load collected questions
  Future<void> _onLoadBrowseCollectedQuestions(
    LoadBrowseCollectedQuestions event,
    Emitter<BrowseState> emit,
  ) async {
    emit(const BrowseLoading());

    try {
      final questions = await _questionRepository.getCollectedQuestions(
        subject: event.subject,
      );

      if (questions.isEmpty) {
        emit(const BrowseError('No collected questions found'));
        return;
      }

      emit(BrowseLoaded(
        questions: questions,
        filteredQuestions: questions,
        currentIndex: 0,
        subject: event.subject,
        type: 'collect',
        showAnswers: true,
        canGoNext: questions.length > 1,
        canGoPrevious: false,
      ));
    } catch (e) {
      emit(BrowseError('Failed to load collected questions: ${e.toString()}'));
    }
  }

  /// Apply filters to questions list
  List<QuestionEntity> _applyFilters(
    List<QuestionEntity> questions, {
    String? questionType,
    bool? showAnswered,
    bool? showUnanswered,
    bool? showCorrect,
    bool? showIncorrect,
    String? searchKeyword,
  }) {
    var filtered = List<QuestionEntity>.from(questions);

    // Filter by question type
    if (questionType != null) {
      QuestionType? type;
      switch (questionType) {
        case 'single':
          type = QuestionType.single;
          break;
        case 'multiple':
          type = QuestionType.multiple;
          break;
        case 'judge':
          type = QuestionType.judge;
          break;
      }
      if (type != null) {
        filtered = filtered.where((q) => q.type == type).toList();
      }
    }

    // Filter by answered status
    if (showAnswered == true && showUnanswered != true) {
      filtered = filtered.where((q) => q.isAnswered).toList();
    } else if (showUnanswered == true && showAnswered != true) {
      filtered = filtered.where((q) => !q.isAnswered).toList();
    }

    // Filter by correctness
    if (showCorrect == true && showIncorrect != true) {
      filtered = filtered.where((q) => q.isAnswered && q.isCorrect).toList();
    } else if (showIncorrect == true && showCorrect != true) {
      filtered = filtered.where((q) => q.isAnswered && !q.isCorrect).toList();
    }

    // Filter by search keyword
    if (searchKeyword != null && searchKeyword.isNotEmpty) {
      final keyword = searchKeyword.toLowerCase();
      filtered = filtered.where((q) {
        return q.title.toLowerCase().contains(keyword) ||
            q.options
                .any((option) => option.text.toLowerCase().contains(keyword)) ||
            (q.explanation?.toLowerCase().contains(keyword) ?? false) ||
            (q.skills?.toLowerCase().contains(keyword) ?? false);
      }).toList();
    }

    return filtered;
  }
}
