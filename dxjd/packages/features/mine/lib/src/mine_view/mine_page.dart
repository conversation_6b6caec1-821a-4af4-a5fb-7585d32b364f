import 'dart:async';
import 'dart:convert';
import 'dart:ffi';
import 'dart:io';

import 'package:component_library/component_library.dart';
import 'package:dxjd/app_routes.dart';
import 'package:dxjd/mainController.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:home/home.dart';
import 'package:home_repository/home_repository.dart';
import 'package:login/login.dart';
import 'package:mine/mine.dart';
import 'package:mine/src/mine_view/coustom_widget/custom_switch.dart';
import 'package:mine/src/mine_view/coustom_widget/custom_view.dart';
import 'package:mop/mop.dart';
import 'package:quiz/quiz.dart';
import 'package:timing_repository/timing_repository.dart';
import 'package:tools/tools.dart';

import 'MineController.dart';

class MinePage extends StatefulWidget {
  @override
  State<MinePage> createState() => _MinePageState();
}

class _MinePageState extends State<MinePage>
    with AutomaticKeepAliveClientMixin {
  bool showSharePanel = false;

  //头部
  Widget _headerView2(MineController logic) {
    return GestureDetector(
        onTap: () {
          // gotoPersonalInfo.call();
          Get.toNamed(AppRoutes.personalInfoPath);
        },
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            //头像   左侧
            SizedBox(
              height: 70.h,
              // color: Colors.white,
              // width: 60.w,
              child: Stack(
                children: [
                  Center(
                      child: logic.userAccount?.image != null &&
                              logic.userAccount!.image!.isNotEmpty
                          ? ClipOval(
                              child: netWorkCacheImg2(
                              img: logic.userAccount!.image,
                              w: 50.0.w,
                              h: 50.0.w,
                              fit: BoxFit.fill,
                            ))
                          : assImg2(img: 'avatar', w: 50.w, h: 50.w)),
                  Obx(
                    () => logic.userRepository.isExistBuyVip.value
                        ? Center(
                            child: Container(
                                height: 52.w,
                                width: 52.w,
                                decoration: BoxDecoration(
                                  // color: Color(0xffF0AB5C),
                                  borderRadius: BorderRadius.circular(25.r),
                                  border: Border.all(
                                      width: 2.w, color: Color(0xffF0AB5C)),
                                )),
                          )
                        : Center(
                            child: Container(
                                height: 52.w,
                                width: 52.w,
                                decoration: BoxDecoration(
                                  // color: Color(0xffF0AB5C),
                                  borderRadius: BorderRadius.circular(25.r),
                                  border: Border.all(
                                      width: 2.w, color: Colors.white),
                                )),
                          ),
                  ),
                  Obx(
                    () => logic.userRepository.isExistBuyVip.value
                        ? Positioned(
                            left: 0,
                            bottom: 0.h,
                            right: 0,
                            child:
                                assImg2(img: 'icon_my_vip', h: 19.h, w: 45.w),
                          )
                        : Positioned(
                            left: 0,
                            bottom: 0.h,
                            right: 0,
                            child: assImg2(
                                img: 'icon_my_not_vip', h: 19.h, w: 45.w),
                          ),
                  )
                ],
              ),
            ).paddingOnly(left: 31.w, right: 13.w),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                    // crossAxisAlignment: CrossAxisAlignment.start,
                    //   mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      app14sp268Text(logic.userAccount?.name ?? "loading...",
                          fontFamily: 'PingFangSC-Semibold',
                          color: Colors.black,
                          textAlign: TextAlign.start),
                      SizedBox(width: 17.w),
                      Container(
                          height: 19.h,
                          width: 46.w,
                          decoration: BoxDecoration(
                              color: logic.userAccount?.isRecorded == 1
                                  ? AppColors.C0091FF
                                  : AppColors.F62020,
                              borderRadius: BorderRadius.circular(4.r)),
                          child: Center(
                              child: app12spAAAText(
                                  logic.userAccount?.isRecorded == 1
                                      ? "已备案"
                                      : '未备案',
                                  color: logic.userAccount?.isRecorded == 1
                                      ? AppColors.white
                                      : Colors.white,
                                  textAlign: TextAlign.center,
                                  fontFamily: 'PingFangSC-Medium'))),
                    ]),
                SizedBox(height: 7.h),
                logic.userAccount?.platSchoolName == null
                    ? GestureDetector(
                        onTap: () {
                          // widget.gotoSelectTrain.call();
                          Get.toNamed(AppRoutes.homeStudyTypeSelect);
                        },
                        child: app12sp268FF7Text('${"未绑定驾校，去绑定驾校"}',
                                color: AppColors.C55AEFE,
                                maxLines: 1,
                                fontFamily: 'PingFangSC-Regular')
                            .paddingOnly(bottom: 18.h),
                      )
                    : SizedBox(
                        width: 135.w,
                        child: app12spAAAText(logic.userAccount?.platSchoolName,
                            color: AppColors.C55AEFE,
                            overflow: TextOverflow.ellipsis,
                            fontFamily: 'PingFangSC-Semibold',
                            textAlign: TextAlign.start),
                      ),
              ],
            ).paddingOnly(top: 15.h),
            Spacer(),
            GestureDetector(
              onTap: () {
                // gotoSetting.call();
                Get.toNamed(AppRoutes.settings);
                // Toast.show("设置");
              },
              child: assImg2(img: 'nav_icon_shezhi', w: 24.w, h: 24.h)
                  .paddingOnly(right: 16.w),
            ).paddingOnly(top: 14.h),
          ],
        )
        // Column(
        //   children: [
        //     Stack(
        //       children: [
        //         // Positioned(
        //         //     left: 0,
        //         //       bottom: 0,
        //         //       right: 0,
        //         //       top: 0,
        //         //     child:
        //         Center(
        //                 child: logic.userAccount?.image != null &&
        //                         logic.userAccount!.image!.isNotEmpty
        //                     ? ClipOval(
        //                         // borderRadius: BorderRadius.circular(99.r),
        //                         // radius: 50.r,
        //                         child: netWorkCacheImg2(
        //                         img: logic.userAccount!.image,
        //                         w: 120.0.w,
        //                         h: 120.0.w,
        //                         fit: BoxFit.cover,
        //                       ))
        //                     : assImg2(img: 'avatar', w: 120.w, h: 120.w))
        //             .paddingOnly(top: 0.h),
        //         // ),
        //         Obx(
        //           () => logic.userRepository.isExistBuyVip.value
        //               ? Center(
        //                   child: assImg2(
        //                       img: 'img_my_touxiang_vip', w: 150.w, h: 150.w))
        //               : Container(),
        //         ),
        //         // Container(
        //         //   padding: EdgeInsets.all(10.h),
        //         //   height: 150.h,
        //         //   width: 150.w,
        //         //   decoration: BoxDecoration(
        //         //     color: Colors.cyan,
        //         //       // image: logic.isTiming?DecorationImage(image: assetImg2(img: 'img_my_touxiang_vip'),fit: BoxFit.fill):null
        //         //   ),
        //         //   child: userAccount?.image == null||userAccount?.image==''? assImg2(img: 'avatar',w: 120.w,h: 120.h):
        //         //   FadeInImage.assetNetwork(placeholder: 'placeholder', image: userAccount!.image!,width: 120.w,height: 120.h,),
        //         // ),
        //         // Positioned(
        //         //   left: 0,
        //         //   bottom: 0,
        //         //   right: 0,
        //         //   top: 0,
        //         //   child: assImg2(img: 'img_my_touxiang_vip',w: 320.w,h: 320.h),
        //         // ),
        //         Obx(
        //           () => logic.userRepository.isExistBuyVip.value
        //               ? Positioned(
        //                   left: 0,
        //                   bottom: 5.h,
        //                   right: 0,
        //                   child: assImg2(img: 'icon_my_vip', h: 20.h, w: 46.w),
        //                 )
        //               : Container(),
        //         )
        //         // Positioned(
        //         //     bottom: 0,
        //         //     left: 95.w,
        //         //     child: assImg2(img: 'icon_my_vip',h: 20.h,w: 46.w)
        //         // ),
        //       ],
        //     ),
        //
        //     // avatar
        //     // CircleAvatar(
        //     //   backgroundColor: Colors.transparent,
        //     //   radius: 50,
        //     //   backgroundImage: assetImg2(img: 'img_my_touxiang_vip'),
        //     //   child: assImg2(img: 'avatar',w: 120.w,h: 120.h).paddingSymmetric(horizontal: 80.w),
        //     // ),
        //     Stack(
        //       // mainAxisAlignment: MainAxisAlignment.start,
        //       // crossAxisAlignment: CrossAxisAlignment.center,
        //       children: [
        //         // Positioned(child: child)
        //         Positioned(
        //           right: 96.w,
        //           top: 10.h,
        //           child: Container(
        //                   height: 18.h,
        //                   width: 46.w,
        //                   decoration: BoxDecoration(
        //                       color: logic.userAccount?.isRecorded == 1
        //                           ? AppColors.E1F1FF
        //                           : AppColors.FF6D5E,
        //                       borderRadius: BorderRadius.circular(4.r)),
        //                   child: Center(
        //                       child: app10sp268FF7Text(
        //                           "${logic.userAccount?.isRecorded == 1 ? "已备案" : '未备案'}",
        //                           color: logic.userAccount?.isRecorded == 1
        //                               ? AppColors.other_login_text
        //                               : Colors.white,
        //                           textAlign: TextAlign.center,
        //                           fontFamily: 'PingFangSC-Regular')))
        //               .paddingOnly(
        //             left: 10.w,
        //           ),
        //         ),
        //         Center(
        //           child: app16spA1Text(
        //                   "${logic.userAccount?.name ?? "loading..."}",
        //                   fontFamily: 'PingFangSC-Medium',
        //                   textAlign: TextAlign.end)
        //               .paddingOnly(top: 8.h, bottom: 9.h),
        //           // child: app16spA1Text("爱卡卡是的哇哇哇",textAlign: TextAlign.end).paddingOnly(top: 8.h,bottom: 9.h),
        //         ),
        //         // Container(width: 220.w,color: Colors.pink,
        //         //     child: app16spA1Text("${userAccount?.name??"loading..."}",textAlign: TextAlign.end).paddingOnly(top: 8.h,bottom: 9.h)),
        //       ],
        //     ),
        //     logic.userAccount?.platSchoolName == null
        //         ? GestureDetector(
        //             onTap: () {
        //               // widget.gotoSelectTrain.call();
        //               Get.toNamed(AppRoutes.homeStudyTypeSelect);
        //             },
        //             child: app12sp268FF7Text('${"未绑定驾校，去绑定驾校"}',
        //                     maxLines: 1, fontFamily: 'PingFangSC-Regular')
        //                 .paddingOnly(bottom: 18.h),
        //           )
        //         : app12spAAAText(logic.userAccount?.platSchoolName,
        //             fontFamily: 'PingFangSC-Regular',
        //             textAlign: TextAlign.center),
        //   ],
        // ),
        ).paddingOnly(top: 60.h);
  }

  Widget _mainView(BuildContext context, MineController logic) {
    return Container(
        padding: Platform.isAndroid
            ? REdgeInsets.only(top: 21)
            : EdgeInsets.only(top: 41.h),
        height: MediaQuery.of(context).size.height,
        decoration: BoxDecoration(
            image:
                DecorationImage(image: assetImg(img: 'bg'), fit: BoxFit.fill)),
        child: CustomScrollView(
          controller: logic.scrollController,
          slivers: [
            SliverToBoxAdapter(child: _headerView2(logic)),
            SliverToBoxAdapter(
              child: white8radiusContainer(
                height: 169.h,
                color: Colors.transparent,
                width: 155.w,
                child: cardCarousel(logic),
                // ListView.builder(
                //   itemBuilder: (_, index) {
                //     return Container(
                //       height: 50.h,
                //       width: 372,
                //       color: Colors.cyan,
                //       child: app14sp268Text('data$index'),
                //     ).paddingOnly(left: 11.w);
                //   },
                //   itemCount: 3,
                //   padding: EdgeInsets.zero,
                //   shrinkWrap: true,
                //   scrollDirection: Axis.horizontal,
                //   // physics: const NeverScrollableScrollPhysics(),
                // )
              ).paddingOnly(top: 11.h),
            ),
            SliverToBoxAdapter(
                child: Container(
              margin: EdgeInsets.all(11.w),
              padding: EdgeInsets.only(bottom: 7.h),
              width: double.infinity,
              // height: 220.h,
              decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(10.r)),
              child: Column(
                children: [
                  Center(
                    child: Container(
                      height: 34.h,
                      margin: EdgeInsets.symmetric(vertical: 11.w),
                      width: 186.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20.r),
                        color: AppColors.F5F7F9,
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: () async {
                                logic.isSubOne = true;
                                logic.update();
                              },
                              child: subjectTabContainer(
                                  co1: logic.isSubOne
                                      ? AppColors.C249FFB
                                      : Colors.transparent,
                                  co2: logic.isSubOne
                                      ? AppColors.C0283E3
                                      : Colors.transparent,
                                  boxShadowColor: logic.isSubOne
                                      ? AppColors.C80C9FF
                                      : Colors.transparent,
                                  // color: logic.isSubOne ? AppColors.C0283E3 : null,
                                  child: Center(
                                      child: logic.isSubOne
                                          ? app16spAC5000Text('科一',
                                              color: AppColors.white,
                                              fontFamily: 'PingFangSC-Semibold')
                                          : app16spAC5000Text('科一',
                                              color: Colors.black,
                                              fontFamily:
                                                  'PingFangSC-Medium'))),
                            ).paddingAll(2.w),
                          ),
                          Expanded(
                            child: GestureDetector(
                              onTap: () async {
                                logic.isSubOne = false;
                                logic.update();
                              },
                              child: subjectTabContainer(
                                  co1: logic.isSubOne
                                      ? Colors.transparent
                                      : AppColors.C249FFB,
                                  co2: logic.isSubOne
                                      ? Colors.transparent
                                      : AppColors.C0283E3,
                                  boxShadowColor: logic.isSubOne
                                      ? Colors.transparent
                                      : AppColors.C80C9FF,
                                  // color: logic.isSubOne ? null : AppColors.C0283E3,
                                  child: Center(
                                      child: logic.isSubOne
                                          ? app16spAC5000Text('科四',
                                              color: Colors.black,
                                              fontFamily: 'PingFangSC-Medium')
                                          : app16spAC5000Text('科四',
                                              color: AppColors.white,
                                              fontFamily:
                                                  'PingFangSC-Semibold'))),
                            ).paddingAll(2.w),
                          ),
                        ],
                      ),
                    ),
                  ),
                  // SizedBox(
                  //   height: 50.h,
                  //   child: Row(
                  //     mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  //     crossAxisAlignment: CrossAxisAlignment.center,
                  //     children: [
                  //
                  //
                  //       const Spacer(
                  //         flex: 1,
                  //       ),
                  //       app12sp268FF7Text("计时开关",
                  //           fontFamily: 'PingFangSC-Regular'),
                  //       CustomSwitch(
                  //           value: logic.isTiming,
                  //           onChanged: (val) {
                  //             if (logic.isTiming) {
                  //               DialogHelper.showDialogTips(
                  //                   context,
                  //                   1,
                  //                   '温馨提醒',
                  //                   '关闭计时系统会禁用计时哦，确定关闭吗！',
                  //                   '取消',
                  //                   '确定', (String? type) {
                  //                 if (type == 'confirm') {
                  //                   logic.isTiming = val;
                  //                   Storage.setCloseTiming(!logic.isTiming);
                  //                   logic.update();
                  //                 }
                  //               });
                  //             } else {
                  //               if (logic.userAccount?.isRecorded != 1) {
                  //                 Toast.show("未备案，学时不计入有效。请联系驾校备案");
                  //               }
                  //               logic.isTiming = val;
                  //               Storage.setCloseTiming(!logic.isTiming);
                  //               logic.update();
                  //             }
                  //           }).paddingOnly(left: 8.w),
                  //       //  FlutterSwitch(
                  //       //      height: 25.h,
                  //       //      activeColor: AppColors.C7DBEFF,
                  //       //      width: 60.w,
                  //       //      padding: 4.w,
                  //       //      value: logic.isTiming,
                  //       //      onToggle: (val){
                  //       //        logic.isTiming = val;
                  //       //        setState(() {});
                  //       //      }
                  //       // ),
                  //       // Transform.scale(
                  //       //   scale: 0.8,
                  //       //   child: Switch(
                  //       //       value: logic.isTiming,
                  //       //       activeColor: AppColors.C7DBEFF,
                  //       //       onChanged: (va) {
                  //       //
                  //       //       }
                  //       //   ),
                  //       // ),
                  //       InkWell(
                  //         child:
                  //             assImg2(img: 'icon_my_wenhao', w: 14.w, h: 14.h)
                  //                 .paddingSymmetric(horizontal: 11.w),
                  //         onTap: () {
                  //           showDialog(
                  //             context: context,
                  //             builder: (BuildContext context) {
                  //               return Dialog(
                  //                 shape: RoundedRectangleBorder(
                  //                   borderRadius: BorderRadius.circular(12),
                  //                 ),
                  //                 child: ConstrainedBox(
                  //                   constraints: BoxConstraints(
                  //                     maxWidth:
                  //                         MediaQuery.of(context).size.width -
                  //                             56, // 减去左右各28像素
                  //                   ),
                  //                   child: Container(
                  //                     decoration: BoxDecoration(
                  //                       borderRadius: BorderRadius.circular(12),
                  //                       color: Colors.white,
                  //                     ),
                  //                     padding: EdgeInsets.only(
                  //                         left: 16.0.w,
                  //                         right: 16.0.w,
                  //                         top: 34.0.h,
                  //                         bottom: 34.0.h),
                  //                     child: Column(
                  //                       mainAxisSize: MainAxisSize.min,
                  //                       crossAxisAlignment:
                  //                           CrossAxisAlignment.start,
                  //                       children: [
                  //                         Text('计时',
                  //                             style: TextStyle(
                  //                                 fontSize: 16.sp,
                  //                                 fontFamily:
                  //                                     'PingFangSC-Regular')),
                  //                         Text(
                  //                             '指的是各地区政策要求，驾驶员培训学习时，每个科目需要学习一定的时长，上传至监管平台。',
                  //                             style: TextStyle(
                  //                                 fontSize: 12.sp,
                  //                                 fontFamily:
                  //                                     'PingFangSC-Regular')),
                  //                         Text(
                  //                             '审核认定的学习时长，满足“地区政策要求时长”后，才能进行约考。',
                  //                             style: TextStyle(
                  //                                 fontSize: 12.sp,
                  //                                 fontFamily:
                  //                                     'PingFangSC-Regular')),
                  //                         SizedBox(
                  //                           height: 10.h,
                  //                         ),
                  //                         Text(
                  //                             '您可以打开此开关，使用【大象驾到Pro APP】进行科目一/科目四的计时学习，并可以查看全科目的学时数据。',
                  //                             style: TextStyle(
                  //                                 fontSize: 12.sp,
                  //                                 fontFamily:
                  //                                     'PingFangSC-Regular')),
                  //                         Text(
                  //                             '如您不需要计时，或者学时已打满，可以关闭“计时开关”进行学习。',
                  //                             style: TextStyle(
                  //                                 fontSize: 12.sp,
                  //                                 fontFamily:
                  //                                     'PingFangSC-Regular')),
                  //                       ],
                  //                     ),
                  //                   ),
                  //                 ),
                  //               );
                  //             },
                  //           );
                  //         },
                  //       )
                  //     ],
                  //   ),
                  // ),
                  Container(
                    padding: EdgeInsets.only(top: 14.h, bottom: 16.h),
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius:
                            BorderRadius.only(topRight: Radius.circular(12.r))),
                    child: Column(
                      children: [
                        Row(
                          key: UniqueKey(),
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Expanded(
                              child: InkWell(
                                splashColor: Colors.transparent,
                                focusColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                hoverColor: Colors.transparent,
                                child: Column(
                                  children: [
                                    app24spFF5757Text(
                                        "${logic.isSubOne ? (IHome.get().quiz1?.done ?? 0) : (IHome.get().quiz4?.done ?? 0)}",
                                        color: AppColors.C333333,
                                        fontFamily: 'PingFangSC-Semibold'),
                                    app14sp268Text('已做题',
                                            color: AppColors.C333333,
                                            fontFamily: 'PingFangSC-Semibold')
                                        .paddingOnly(bottom: 4.h),
                                    app12spAAAText(
                                        color: AppColors.C999999,
                                        "正确率${logic.isSubOne ? Math.getDouble(Math.divided((IHome.get().quiz1?.correct ?? 0) * 100, IHome.get().quiz1?.done), 2) : Math.getDouble(Math.divided((IHome.get().quiz4?.correct ?? 0) * 100, IHome.get().quiz4?.done), 2)}%",
                                        fontFamily: 'PingFangSC-Medium'),
                                  ],
                                ),
                                onTap: () {
                                  BuryingPointUtils.instance.addPoint(
                                      buryingPointList: BuryingPointList(
                                          eventType: 8,
                                          entranceType: 1,
                                          action: 1,
                                          browseDuration: 0));
                                  JumpUtils.get().jumpChapter(
                                      logic.isSubOne ? 1 : 4,
                                      isTiming: false);
                                },
                              ),
                            ),
                            Expanded(
                              child: InkWell(
                                splashColor: Colors.transparent,
                                focusColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                hoverColor: Colors.transparent,
                                child: Column(
                                  children: [
                                    app24sp3333Text(
                                        "${logic.isSubOne ? (IHome.get().exam1?.average ?? 0) : (IHome.get().exam4?.average ?? 0)}",
                                        color: AppColors.C333333,
                                        fontFamily: 'PingFangSC-Semibold'),
                                    app14sp268Text("模拟均分",
                                            color: AppColors.C333333,
                                            fontFamily: 'PingFangSC-Semibold')
                                        .paddingOnly(bottom: 4.h),
                                    app12spAAAText(
                                        "累计考试${logic.isSubOne ? IHome.get().exam1?.record ?? 0 : IHome.get().exam4?.record ?? 0}次",
                                        fontFamily: 'PingFangSC-Medium'),
                                  ],
                                ),
                                onTap: () {
                                  BuryingPointUtils.instance.addPoint(
                                      buryingPointList: BuryingPointList(
                                          eventType: 8,
                                          entranceType: 2,
                                          action: 1,
                                          browseDuration: 0));
                                  JumpUtils.get().jumpMockHome(
                                      logic.isSubOne ? 1 : 4,
                                      isTiming: false);
                                },
                              ),
                            ),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  app18spFFFFText(
                                      "${logic.isSubOne ? IHome.get().pass1?.rate ?? 0 : IHome.get().pass4?.rate ?? 0}%",
                                      color: AppColors.C0888E8,
                                      fontFamily: 'PingFangSC-Semibold'),
                                  app12spAAAText("考试通过率",
                                          color: AppColors.C0888E8,
                                          fontFamily: 'PingFangSC-Medium')
                                      .paddingOnly(top: 2.h)
                                      .paddingOnly(bottom: 4.h),
                                  InkWell(
                                    splashColor: Colors.transparent,
                                    focusColor: Colors.transparent,
                                    highlightColor: Colors.transparent,
                                    hoverColor: Colors.transparent,
                                    child: Container(
                                    width: 76.w,
                                      padding: EdgeInsets.only(
                                          top: 2.h,
                                          bottom: 2.h,),
                                      decoration: BoxDecoration(
                                        borderRadius:
                                            BorderRadius.circular(20.r),
                                        gradient: const LinearGradient(
                                            begin: Alignment.topCenter,
                                            colors: [
                                              AppColors.C26A1FD,
                                              AppColors.C0081E2
                                            ],
                                            end: Alignment.bottomCenter),
                                      ),
                                      child: Center(
                                        child: app14sp268Text('VIP课程',
                                            color: Colors.white,
                                            fontFamily: 'PingFangSC-Medium'),
                                      ),
                                    ).paddingSymmetric(horizontal: 1.w),
                                    onTap: () {
                                      BuryingPointUtils.instance.addPoint(
                                          buryingPointList: BuryingPointList(
                                              eventType: 8,
                                              entranceType: 15,
                                              action: 1,
                                              browseDuration: 0));
                                      JumpUtils.get().jumpPromote(
                                          logic.isSubOne ? 1 : 4,
                                          isTiming: false,
                                          vipType: 66,
                                          type: 'jingxuan600ti');
                                    },
                                  ),
                                  SizedBox(height: 1.h)
                                ],
                              ),
                            ),
                          ],
                        ).paddingOnly(bottom: 15.h),
                        Row(
                          key: UniqueKey(),
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Expanded(
                              child: InkWell(
                                splashColor: Colors.transparent,
                                focusColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                hoverColor: Colors.transparent,
                                child: Container(
                                  // padding: EdgeInsets.only(
                                  //     left: 12.w, top: 8.h, bottom: 8.h),
                                  // decoration: BoxDecoration(
                                  //     image: DecorationImage(
                                  //         image: assetImg2(img: 'my_bg_cuoti'),
                                  //         fit: BoxFit.cover)),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      app14sp268Text(
                                          '${logic.isSubOne ? IHome.get().quiz1?.wrong ?? 0 : IHome.get().quiz4?.wrong ?? 0}',
                                          color: AppColors.C333333,
                                          fontFamily: 'PingFangSC-Semibold'),
                                      app12spAAAText('我的错题',
                                          color: AppColors.C333333,
                                          fontFamily: 'PingFangSC-Medium')
                                    ],
                                  ),
                                ).paddingOnly(left: 1.w),
                                onTap: () {
                                  BuryingPointUtils.instance.addPoint(
                                      buryingPointList: BuryingPointList(
                                          eventType: 8,
                                          entranceType: 3,
                                          action: 1,
                                          browseDuration: 0));
                                  JumpUtils.get().jumpFolder(
                                      logic.isSubOne ? 1 : 4, JkKey.KEY_ERROR,
                                      isTiming: false);
                                },
                              ),
                            ),
                            Container(
                              height: 12.h,
                              width: 1.w,
                              color: AppColors.DEECFA,
                            ),
                            Expanded(
                              child: InkWell(
                                splashColor: Colors.transparent,
                                focusColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                hoverColor: Colors.transparent,
                                child: Container(
                                  // padding: EdgeInsets.only(
                                  //     left: 12.w, top: 8.h, bottom: 8.h),
                                  // decoration: BoxDecoration(
                                  //     image: DecorationImage(
                                  //         image:
                                  //             assetImg2(img: 'my_bg_shoucang'),
                                  //         fit: BoxFit.cover)),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      app14sp268Text(
                                          "${logic.isSubOne ? IHome.get().quiz1?.collect ?? 0 : IHome.get().quiz4?.collect ?? 0}",
                                          fontFamily: 'PingFangSC-Semibold',
                                          color: AppColors.C333333),
                                      app12spAAAText("我的收藏",
                                          color: AppColors.C333333,
                                          fontFamily: 'PingFangSC-Medium'),
                                    ],
                                  ),
                                ),
                                onTap: () {
                                  BuryingPointUtils.instance.addPoint(
                                      buryingPointList: BuryingPointList(
                                          eventType: 8,
                                          entranceType: 4,
                                          action: 1,
                                          browseDuration: 0));
                                  JumpUtils.get().jumpFolder(
                                      logic.isSubOne ? 1 : 4, JkKey.KEY_COLLECT,
                                      isTiming: false);
                                },
                              ),
                            ),
                            Container(
                              height: 12.h,
                              width: 1.w,
                              color: AppColors.DEECFA,
                            ),
                            Expanded(
                              child: InkWell(
                                splashColor: Colors.transparent,
                                focusColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                hoverColor: Colors.transparent,
                                child: Container(
                                  // padding: EdgeInsets.only(
                                  //     left: 12.w, top: 8.h, bottom: 8.h),
                                  // decoration: BoxDecoration(
                                  //     image: DecorationImage(
                                  //         image:
                                  //             assetImg2(img: 'my_bg_shoucang'),
                                  //         fit: BoxFit.cover)),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      app12spAAAText("点击查看",
                                          fontFamily: 'PingFangSC-Semibold',
                                          color: AppColors.C333333),
                                      app12spAAAText("我的成绩",
                                          color: AppColors.C333333,
                                          fontFamily: 'PingFangSC-Medium'),
                                    ],
                                  ),
                                ),
                                onTap: () {
                                  BuryingPointUtils.instance.addPoint(
                                      buryingPointList: BuryingPointList(
                                          eventType: 8,
                                          entranceType: 5,
                                          action: 1,
                                          browseDuration: 0));
                                  const shopPath = '/pages/grade/index/index';
                                  JumpSmallProgramUtils.jump(
                                      shopPath, "fc2230308094514309");
                                },
                              ),
                            ),
                          ],
                        )
                      ],
                    ),
                  ),
                ],
              ),
            )),
            SliverToBoxAdapter(
              child: Container(
                width: double.infinity,
                height: 50.h,
                decoration: BoxDecoration(
                    image: DecorationImage(
                  image: assetImg2(img: 'jishi_bg'),
                  fit: BoxFit.fill,
                )),
                child: Row(
                  children: [
                    SizedBox(width: 15.w),
                    logic.isTiming
                        ? Image(
                            image: AssetImage(
                                "assets/home_img/jishi_zhongbiao_do.gif"),
                            width: 44.w,
                            height: 44.w,
                          )
                        : assImg2(img: 'jishi_zhongbiao', w: 44.w, h: 44.h),
                    SizedBox(width: 12.w),
                    app14sp268Text('计时开关',
                        color: AppColors.white,
                        fontFamily: 'PingFangSC-Semibold'),
                    Spacer(),
                    CustomSwitch(
                        value: logic.isTiming,
                        onChanged: (val) {
                          if (logic.isTiming) {
                            DialogHelper.showDialogTips(
                                context,
                                1,
                                '温馨提醒',
                                '关闭计时系统会禁用计时哦，确定关闭吗！',
                                '取消',
                                '确定', (String? type) {
                              if (type == 'confirm') {
                                logic.isTiming = val;
                                Storage.setCloseTiming(!logic.isTiming);
                                logic.update();
                              }
                            });
                          } else {
                            if (logic.userAccount?.isRecorded != 1) {
                              Toast.show("未备案，学时不计入有效。请联系驾校备案");
                            }
                            logic.isTiming = val;
                            Storage.setCloseTiming(!logic.isTiming);
                            logic.update();
                          }
                        }).paddingOnly(left: 8.w, right: 10.w),
                  ],
                ),
              ).paddingSymmetric(horizontal: 11.h),
            ),
            SliverToBoxAdapter(
                child: Container(
              padding: EdgeInsets.only(top: 16.h, bottom: 16.h),
              margin: EdgeInsets.only(left: 11.w, right: 11.w, top: 11.h),
              width: double.infinity,
              decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.r)),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  meIconButton2(
                      w: 30.w,
                      h: 30.h,
                      isdesc: true,
                      icon: 'my_icon_wodedangan',
                      text: '我的档案',
                      color: AppColors.C333333,
                      fontSize: 13.sp,
                      onTap: () {
                        if (logic.userAccount?.platSchoolName == null) {
                          // widget.gotoSelectTrain.call();
                          Get.toNamed(AppRoutes.homeStudyTypeSelect);
                        } else {
                          // widget.gotoMyDoc.call();
                          Get.toNamed(AppRoutes.myDocPath);
                        }
                      }),
                  // meIconButton2(
                  //     icon: 'my_icon_wodechengji',
                  //     text: '我的成绩',
                  //     onTap: () {
                  //       BuryingPointUtils.instance.addPoint(
                  //           buryingPointList: BuryingPointList(
                  //               eventType: 8,
                  //               entranceType: 5,
                  //               action: 1,
                  //               browseDuration: 0));
                  //       const shopPath = '/pages/grade/index/index';
                  //       JumpSmallProgramUtils.jump(
                  //           shopPath, "fc2230308094514309");
                  //     }),
                  meIconButton2(
                      w: 30.w,
                      h: 30.h,
                      isdesc: true,
                      icon: 'my_icon_wodedingdan',
                      text: '我的订单',
                      color: AppColors.C333333,
                      fontSize: 13.sp,
                      onTap: () async {
                        const orderPath = '/pages/shop/order/list/list';
                        // Mop.instance
                        //     .openApplet('fc2259710004813765', path: orderPath);
                        JumpSmallProgramUtils.jump(
                            orderPath, "fc2259710004813765");
                      }),
                  Stack(
                    alignment: Alignment.topRight,
                    children: [
                      meIconButton2(
                          w: 30.w,
                          h: 30.h,
                          isdesc: true,
                          icon: 'icon_coupon',
                          text: '优惠券',
                          color: AppColors.C333333,
                          fontSize: 13.sp,
                          onTap: () {
                            if (!MainController.isLoginIntercept()) {
                              return;
                            }
                            logic.changeShowRedPointInCoupon();
                            Get.toNamed(AppRoutes.couponPage);
                          }),
                      logic.isShowRedPointInCoupon
                          ? Container(
                              height: 12.h,
                              width: 12.h,
                              decoration: BoxDecoration(
                                color: AppColors.FF3639,
                                borderRadius: BorderRadius.circular(55.r),
                              ))
                          : const SizedBox()
                    ],
                  ),
                  meIconButton2(
                      w: 30.w,
                      h: 30.h,
                      isdesc: true,
                      icon: 'my_icon_fuwu',
                      text: '我的服务',
                      color: AppColors.C333333,
                      fontSize: 13.sp,
                      onTap: () {
                        const shopPath = 'pages/service/service';
                        // Mop.instance
                        //     .openApplet('fc2230308094514309', path: shopPath);
                        JumpSmallProgramUtils.jump(
                            shopPath, "fc2230308094514309");
                      }),
                ],
              ),
            )),
            SliverToBoxAdapter(
              child: white8radiusContainer(
                  // height: 371.h,
                  child: ListView.separated(
                separatorBuilder: (BuildContext context, int index) {
                  return Container(
                    margin: REdgeInsets.symmetric(horizontal: 18),
                    height: 1.h,
                    child: DashedLineWidget(
                      width: double.infinity,
                      height: 1.h,
                      dashWidth: 1,
                      dashSpace: 1,
                      color: const Color(0xFFF6F8FA),
                    ),
                  );
                },
                itemBuilder: (_, index) {
                  return GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () async {
                      if (index == 0) {
                        const shopPath = '/pages/operate/notice/list/list';
                        // Mop.instance
                        //     .openApplet('fc2230308094514309', path: shopPath);
                        JumpSmallProgramUtils.jump(
                            shopPath, "fc2230308094514309");
                      } else if (index == 1) {
                        showDialog(
                          context: Get.context!,
                          barrierDismissible: false, // 点击空白区域不可关闭
                          builder: (BuildContext context) {
                            return SecretDialog();
                          },
                        );
                        logic.onClickMijiRp();
                      } else if (index == 3) {
                        try {
                          if (showSharePanel) {
                            return;
                          }
                          showSharePanel = true;
                          var result =
                              await logic.channel.invokeMethod('linkShare', {
                            'url':
                                "https://dxjk.daxiangjd.com/share/?Id=${logic.userAccount?.sid}",
                            'title': '刚拿驾证，快来沾沾喜气',
                            'description': '送你亲测好用的备考神器',
                            'thumbUrl':
                                "https://ts1.cn.mm.bing.net/th?id=OIP-C.U2jmxpR92QEva8rxgAikIwHaE8&w=306&h=204&c=8&rs=1&qlt=90&o=6&dpr=2&pid=3.1&rm=2"
                          });
                          // logic.channel.invokeMethod(
                          //     'shareImage', {'url':"https://ts1.cn.mm.bing.net/th?id=OIP-C.U2jmxpR92QEva8rxgAikIwHaE8&w=306&h=204&c=8&rs=1&qlt=90&o=6&dpr=2&pid=3.1&rm=2"
                          // });
                          showSharePanel = false;
                          if (result == 'noInstall') {
                            Toast.show('应用未安装，请安装后再试');
                          }
                        } catch (e) {
                          showSharePanel = false;
                          Toast.show('分享失败，请稍后再试');
                        }
                      } else if (index == 2) {
                        // Get.toNamed(AppRoutes.dyStudyAttentionPage);
                        Get.to(DyStudyAttentionPage(
                            timingRepository: logic.timingRepository));
                        logic.onClickDyguanzhuRp();
                      }
                    },
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Stack(
                          children: [
                            assImg2(
                                img: logic.iconList[index], w: 25.w, h: 25.h),
                            logic.isShowMijiRp && index == 1
                                ? Positioned(
                                    top: 0,
                                    right: 0,
                                    child: Container(
                                      height: 12.h,
                                      width: 12.h,
                                      decoration: BoxDecoration(
                                        color: AppColors.FF3639,
                                        borderRadius:
                                            BorderRadius.circular(55.r),
                                      ),
                                      child: Center(
                                        child: app10spAAAA7Text('1',
                                            color: Colors.white,
                                            fontFamily: 'PingFangSC-Semibold',
                                            height: 1.0.h),
                                      ),
                                    ))
                                : logic.isShowDyguanzhuRp && index == 2
                                    ? Positioned(
                                        top: 0,
                                        right: 0,
                                        child: Container(
                                          height: 12.h,
                                          width: 12.h,
                                          decoration: BoxDecoration(
                                            color: AppColors.FF3639,
                                            borderRadius:
                                                BorderRadius.circular(55.r),
                                          ),
                                          child: Center(
                                            child: app10spAAAA7Text('1',
                                                color: Colors.white,
                                                fontFamily:
                                                    'PingFangSC-Semibold',
                                                height: 1.0.h),
                                          ),
                                        ))
                                    : Container()
                          ],
                        ),
                        SizedBox(width: 12.w),
                        app14spA1Text(logic.titleList[index],
                            fontFamily: 'PingFangSC-Medium'),
                        Spacer(),
                        index == 1
                            ? Row(
                                children: [
                                  Text(
                                    '免费领取',
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: Color(0xff007AFF),
                                      fontFamily: 'PingFangSC-Medium',
                                    ),
                                  ),
                                  assImg2(img: 'icon_more', w: 16.w, h: 16.h),
                                ],
                              )
                            : index == 2
                                ? Row(
                                    children: [
                                      Text(
                                        '免费领取',
                                        style: TextStyle(
                                          fontSize: 12.sp,
                                          color: Color(0xff007AFF),
                                          fontFamily: 'PingFangSC-Medium',
                                        ),
                                      ),
                                      assImg2(
                                          img: 'icon_more', w: 16.w, h: 16.h),
                                    ],
                                  )
                                : assImg2(img: 'icon_more', w: 16.w, h: 16.h),
                      ],
                    ).paddingAll(11.h),
                    // ListTile(
                    //   leading:
                    //       assImg2(img: logic.iconList[index], w: 24.w, h: 24.h),
                    //   title: app14spA1Text(logic.titleList[index],fontFamily: 'PingFangSC-Regular'),
                    //   trailing: index == 1? Row(
                    //     children: [
                    //       app14sp268Text('data'),
                    //       assImg2(img: 'icon_more', w: 24.w, h: 24.h),
                    //     ],
                    //   ) : assImg2(img: 'icon_more', w: 16.w, h: 16.h),
                    // ),
                  );
                },
                itemCount: logic.iconList.length,
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
              )).paddingSymmetric(horizontal: 11.w, vertical: 11.h),
            ),
            SliverToBoxAdapter(
              child: assImg2(img: 'dxjd_mine_bottom_logo', w: 215.w, h: 54.h)
                  .paddingOnly(bottom: 16.h),
              // Container(
              //   height: 69.h,
              //   width: double.infinity,
              //   decoration: BoxDecoration(
              //     image: DecorationImage(
              //         image: assetImg2(img: 'dxjd_mine_bottom_logo'),
              //         fit: BoxFit.cover),
              //   ),
              // ),
            )
          ],
        ));
  }

  Widget cardCarousel(MineController logic) {
    return Column(
      children: [
        Expanded(
          child: NotificationListener<ScrollNotification>(
            onNotification: (ScrollNotification notification) {
              if (notification is ScrollEndNotification) {
                final currentPage = logic.pageController.page?.round() ?? 0;
                if (currentPage == 0) {
                  // 滑动到开头时动画跳转到倒数第二项
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    logic.pageController.jumpToPage(
                      logic.extended_vip_sub_List.length - 2,
                    );
                  });
                } else if (currentPage ==
                    logic.extended_vip_sub_List.length - 1) {
                  // 滑动到末尾时动画跳转到第二项
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    logic.pageController.jumpToPage(
                      1,
                    );
                  });
                }
              }
              return false;
            },
            child: PageView.builder(
              controller: logic.pageController,
              onPageChanged: (index) {},
              itemCount: logic.extended_vip_sub_List.length,
              itemBuilder: (context, index) {
                return AnimatedBuilder(
                  animation: logic.pageController,
                  builder: (context, child) {
                    double value = 0;
                    if (logic.pageController.position.haveDimensions) {
                      value =
                          index.toDouble() - (logic.pageController.page ?? 0);
                      value = (value * 0.1).clamp(-1, 1); // 控制缩放系数
                    }
                    return Transform.scale(
                      scale: 1 - value.abs() * 0.2, // 缩放动画
                      child: child,
                    );
                  },
                  child: _buildCard(countIndex(index), logic),
                );
              },
            ),
          ),
        ),
        // SizedBox(height: 20),
        // _buildPageIndicator(),
      ],
    );
  }

  int countIndex(int index) {
    if (index == 0) {
      return 3;
    } else if (index < 5) {
      return index - 1;
    } else {
      return 0;
    }
  }

  Widget _buildCard(int index, MineController logic) {
    return Container(
        margin: EdgeInsets.symmetric(horizontal: 1),
        decoration: BoxDecoration(
          // color: color,
          image: DecorationImage(
              image: assetImg2(img: 'mine_vip_card_bg'), fit: BoxFit.fill),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          children: [
            Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        assImg2(
                            img: 'mine_vip_card_huangguan', w: 16.w, h: 16.h),
                        SizedBox(width: 4.w),
                        app16spAC5000Text('${logic.vip_sub_List[index]}VIP',
                            color: AppColors.E2AE73,
                            fontFamily: 'PingFangSC-Semibold')
                      ],
                    ),
                    app14sp268Text(
                        logic.vipTimeList[index] != null &&
                                logic.vipTimeList[index] != 0
                            ? '${DateUtilForDxjk.getChineseYmdDate(logic.vipTimeList[index] ?? 0)}到期'
                            : '${logic.vip_sub_List_tips[index]}',
                        color: AppColors.E2AE73,
                        fontFamily: 'PingFangSC-Medium')
                  ],
                ),
                Spacer(),
                GestureDetector(
                  onTap: logic.isVipSubList[index]
                      ? () async {
                          if (index == 0) {
                            JumpUtils.get().jumpVipCourse(1, isTiming: false);
                          } else if (index == 1) {
                            var data;
                            try {
                              data = await logic.homeRepository
                                  .getExaminationRoomRoute(
                                subject: 2,
                                index: 1,
                                rows: 20,
                                division: int.parse(logic
                                        .homeRepository.cityInfo.cityCode) ??
                                    440100,
                              );
                            } catch (e) {
                              data = null;
                            }
                            if (data != null) {
                              Get.toNamed(
                                AppRoutes.examinationRoomSelectScreen,
                              );
                            } else {
                              showDialog(
                                context: Get.context!,
                                barrierDismissible: false, // 点击空白区域不可关闭
                                builder: (BuildContext context) {
                                  return Dialog(
                                    child: U3dConfirmDialog(
                                      content: '即将打开3D练车',
                                      cancelText: '取消',
                                      confirmText: '确认',
                                      onCancel: () {
                                        Get.back();
                                      },
                                      onConfirm: () {
                                        Get.back();
                                        IPlatform.get().jumpLightSimulation(
                                            'subject2', '');
                                      },
                                    ),
                                  );
                                },
                              );
                            }
                          } else if (index == 2) {
                            var data;
                            try {
                              data = await logic.homeRepository
                                  .getExaminationRoomRoute(
                                subject: 3,
                                index: 1,
                                rows: 200,
                                division: int.parse(logic
                                        .homeRepository.cityInfo.cityCode) ??
                                    440100,
                              );
                            } catch (e) {
                              data = null;
                            }
                            if (data != null) {
                              if (Get.isRegistered<SectionThreeController>(
                                tag: ExaminationPageState.key.toString(),
                              )) {
                                Get.find<SectionThreeController>(
                                  tag: ExaminationPageState.key.toString(),
                                ).goCatalogPage(3);
                                return;
                              }
                              Get.toNamed(AppRoutes.subjectThreeCatalogPage,
                                  arguments: {
                                    'selectIndex': 3,
                                    'isNeedLoadingData': true
                                  });
                            } else {
                              // IPlatform.get()
                              //     .jumpLightSimulation('subject3', '');
                              TheoryVideoListDm? theoryVideoListDM;
                              try {
                                theoryVideoListDM = await logic.homeRepository
                                    .getSubjectThreePracticalVideo(subject: 3);
                                if (theoryVideoListDM != null) {
                                  // theoryVideoListDm = value;
                                  // update();
                                  logic.homeRepository
                                      .setSubThreePracticalModeAndIndex(
                                          theoryVideoListDM,
                                          0,
                                          jsonDecode(theoryVideoListDM.listDM[0]
                                              .address)['Data']['vid']);
                                  Get.toNamed(
                                      AppRoutes.subThreePracticalVideoDetail);
                                }
                              } catch (e) {
                                theoryVideoListDM = null;
                                // update();
                                // if (e is DomainException) {
                                //   Toast.show(e.message);
                                // } else {
                                //   Toast.show(e.toString());
                                // }
                                // rethrow;
                                Toast.show('获取视频列表失败');
                              }
                            }
                          } else if (index == 3) {
                            JumpUtils.get().jumpVipCourse(4, isTiming: false);
                          }
                        }
                      : () {
                          debugPrint('点击立即开通$index');
                          if (index == 0) {
                            JumpUtils.get()
                                .jumpBuyVip(vipType: 1, type: 'jingxuan600ti');
                          } else if (index == 1) {
                            _buyVio2(logic, type: 'quanshikaochang');
                          } else if (index == 2) {
                            logic.timingRepository.goBuyTypeVip(6);
                          } else if (index == 3) {
                            JumpUtils.get()
                                .jumpBuyVip(vipType: 4, type: 'jingxuan600ti');
                          }
                        },
                  child: Container(
                    height: 30.h,
                    width: 90.w,
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                          begin: Alignment.centerLeft,
                          colors: [AppColors.DDAA6D, AppColors.F1AE55]),
                      borderRadius: BorderRadius.circular(15.r),
                    ),
                    child: Center(
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          logic.isVipSubList[index]
                              ? app13spC66666Text('立即使用',
                                  color: AppColors.C663409,
                                  fontFamily: 'PingFangSC-Semibold')
                              : app13spC66666Text('立即开通',
                                  color: AppColors.C663409,
                                  fontFamily: 'PingFangSC-Semibold'),
                          assImg2(img: 'mine_vip_card_sy', w: 16.w, h: 16.h)
                        ],
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 15.w),
              ],
            ).paddingOnly(left: 20.w, top: 12.h, bottom: 15.h),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                GestureDetector(
                  onTap: logic.isVipSubList[index]
                      ? () async {
                          if (index == 0) {
                            JumpUtils.get().jumpVipCourse(1, isTiming: false);
                          } else if (index == 1) {
                            var data;
                            try {
                              data = await logic.homeRepository
                                  .getExaminationRoomRoute(
                                subject: 2,
                                index: 1,
                                rows: 20,
                                division: int.parse(logic
                                        .homeRepository.cityInfo.cityCode) ??
                                    440100,
                              );
                            } catch (e) {
                              data = null;
                            }
                            if (data != null) {
                              Get.toNamed(
                                AppRoutes.examinationRoomSelectScreen,
                              );
                            } else {
                              showDialog(
                                context: Get.context!,
                                barrierDismissible: false, // 点击空白区域不可关闭
                                builder: (BuildContext context) {
                                  return Dialog(
                                    child: U3dConfirmDialog(
                                      content: '即将打开3D练车',
                                      cancelText: '取消',
                                      confirmText: '确认',
                                      onCancel: () {
                                        Get.back();
                                      },
                                      onConfirm: () {
                                        Get.back();
                                        IPlatform.get().jumpLightSimulation(
                                            'subject2', '');
                                      },
                                    ),
                                  );
                                },
                              );
                            }
                          } else if (index == 2) {
                            var data;
                            try {
                              data = await logic.homeRepository
                                  .getExaminationRoomRoute(
                                subject: 3,
                                index: 1,
                                rows: 200,
                                division: int.parse(logic
                                        .homeRepository.cityInfo.cityCode) ??
                                    440100,
                              );
                            } catch (e) {
                              data = null;
                            }
                            if (data != null) {
                              if (Get.isRegistered<SectionThreeController>(
                                tag: ExaminationPageState.key.toString(),
                              )) {
                                Get.find<SectionThreeController>(
                                  tag: ExaminationPageState.key.toString(),
                                ).goCatalogPage(3);
                                return;
                              }
                              Get.toNamed(AppRoutes.subjectThreeCatalogPage,
                                  arguments: {
                                    'selectIndex': 3,
                                    'isNeedLoadingData': true
                                  });
                            } else {
                              // IPlatform.get()
                              //     .jumpLightSimulation('subject3', '');
                              TheoryVideoListDm? theoryVideoListDM;
                              try {
                                theoryVideoListDM = await logic.homeRepository
                                    .getSubjectThreePracticalVideo(subject: 3);
                                if (theoryVideoListDM != null) {
                                  // theoryVideoListDm = value;
                                  // update();
                                  logic.homeRepository
                                      .setSubThreePracticalModeAndIndex(
                                          theoryVideoListDM,
                                          0,
                                          jsonDecode(theoryVideoListDM.listDM[0]
                                              .address)['Data']['vid']);
                                  Get.toNamed(
                                      AppRoutes.subThreePracticalVideoDetail);
                                }
                              } catch (e) {
                                theoryVideoListDM = null;
                                // update();
                                // if (e is DomainException) {
                                //   Toast.show(e.message);
                                // } else {
                                //   Toast.show(e.toString());
                                // }
                                // rethrow;
                                Toast.show('获取视频列表失败');
                              }
                            }
                          } else if (index == 3) {
                            JumpUtils.get().jumpVipCourse(4, isTiming: false);
                          }
                        }
                      : () {
                          if (index == 0) {
                            JumpUtils.get()
                                .jumpBuyVip(vipType: 1, type: 'jingxuan600ti');
                          } else if (index == 1) {
                            _buyVio2(logic, type: 'quanshikaochang');
                          } else if (index == 2) {
                            logic.timingRepository.goBuyTypeVip(6);
                          } else if (index == 3) {
                            JumpUtils.get()
                                .jumpBuyVip(vipType: 4, type: 'jingxuan600ti');
                          }
                        },
                  child: Column(
                    children: [
                      index == 1 || index == 2
                          ? assImg2(img: 'mine_vip_card_xdw', w: 34.w, h: 34.h)
                          : assImg2(img: 'mine_vip_card_jxt', w: 34.w, h: 34.h),
                      SizedBox(height: 8.h),
                      index == 1
                          ? app13spC66666Text('学点位',
                              color: AppColors.white,
                              fontFamily: 'PingFangSC-Medium')
                          : index == 2
                              ? app13spC66666Text('学路线',
                                  color: AppColors.white,
                                  fontFamily: 'PingFangSC-Medium')
                              : app13spC66666Text('精选题',
                                  color: AppColors.white,
                                  fontFamily: 'PingFangSC-Medium'),
                      SizedBox(height: 3.h),
                      index == 1 || index == 2
                          ? app11spAAAText('必考讲解',
                              color: AppColors.F9DBBC,
                              fontFamily: 'PingFangSC-Regular')
                          : app11spAAAText('600题',
                              color: AppColors.F9DBBC,
                              fontFamily: 'PingFangSC-Regular')
                    ],
                  ),
                ),
                assImg2(img: 'mine_vip_card_jiantou', w: 14.w, h: 14.h)
                    .paddingOnly(top: 10.h),
                GestureDetector(
                  onTap: logic.isVipSubList[index]
                      ? () {
                          if (index == 0) {
                            JumpUtils.get()
                                .jumpMockHome(JkKey.SUBONE, isTiming: false);
                          } else if (index == 1) {
                            showDialog(
                              context: Get.context!,
                              barrierDismissible: false, // 点击空白区域不可关闭
                              builder: (BuildContext context) {
                                return Dialog(
                                  child: U3dConfirmDialog(
                                    content: '即将打开3D练车',
                                    cancelText: '取消',
                                    confirmText: '确认',
                                    onCancel: () {
                                      Get.back();
                                    },
                                    onConfirm: () {
                                      Get.back();
                                      IPlatform.get()
                                          .jumpLightSimulation('subject2', '');
                                    },
                                  ),
                                );
                              },
                            );
                            // IPlatform.get().jumpLightSimulation('subject2', '');
                          } else if (index == 2) {
                            showDialog(
                              context: Get.context!,
                              barrierDismissible: false, // 点击空白区域不可关闭
                              builder: (BuildContext context) {
                                return Dialog(
                                  child: U3dConfirmDialog(
                                    content: '即将打开3D练车',
                                    cancelText: '取消',
                                    confirmText: '确认',
                                    onCancel: () {
                                      Get.back();
                                    },
                                    onConfirm: () {
                                      Get.back();
                                      IPlatform.get()
                                          .jumpLightSimulation('subject3', '');
                                    },
                                  ),
                                );
                              },
                            );
                          } else if (index == 3) {
                            JumpUtils.get()
                                .jumpMockHome(JkKey.SUBFOUR, isTiming: false);
                          }
                        }
                      : () {
                          if (index == 0) {
                            JumpUtils.get()
                                .jumpBuyVip(vipType: 1, type: 'kaochangmoni');
                          } else if (index == 1) {
                            _buyVio2(logic);
                          } else if (index == 2) {
                            logic.timingRepository.goBuyTypeVip(3);
                          } else if (index == 3) {
                            JumpUtils.get()
                                .jumpBuyVip(vipType: 4, type: 'kaochangmoni');
                          }
                        },
                  child: Column(
                    children: [
                      index == 1 || index == 2
                          ? assImg2(img: 'mine_vip_card_l3d', w: 34.w, h: 34.h)
                          : assImg2(
                              img: 'mine_vip_card_mock', w: 34.w, h: 34.h),
                      SizedBox(height: 8.h),
                      index == 1 || index == 2
                          ? app13spC66666Text('练3D',
                              color: AppColors.white,
                              fontFamily: 'PingFangSC-Medium')
                          : app13spC66666Text('模考',
                              color: AppColors.white,
                              fontFamily: 'PingFangSC-Medium'),
                      SizedBox(height: 3.h),
                      index == 1 || index == 2
                          ? app11spAAAText('记点位',
                              color: AppColors.F9DBBC,
                              fontFamily: 'PingFangSC-Regular')
                          : app11spAAAText('考场1:1',
                              color: AppColors.F9DBBC,
                              fontFamily: 'PingFangSC-Regular')
                    ],
                  ),
                ),
                assImg2(img: 'mine_vip_card_jiantou', w: 14.w, h: 14.h)
                    .paddingOnly(top: 10.h),
                GestureDetector(
                  onTap: logic.isVipSubList[index]
                      ? () async {
                          if (index == 0) {
                            JumpUtils.get()
                                .jumpEssence(JkKey.SUBONE, isTiming: false);
                          } else if (index == 1) {
                            showDialog(
                              context: Get.context!,
                              barrierDismissible: false, // 点击空白区域不可关闭
                              builder: (BuildContext context) {
                                return Dialog(
                                  child: U3dConfirmDialog(
                                    content: '即将打开3D练车',
                                    cancelText: '取消',
                                    confirmText: '确认',
                                    onCancel: () {
                                      Get.back();
                                    },
                                    onConfirm: () {
                                      Get.back();
                                      IPlatform.get()
                                          .jumpLightSimulation('subject2', '');
                                    },
                                  ),
                                );
                              },
                            );
                          } else if (index == 2) {
                            var data;
                            try {
                              data = await logic.homeRepository
                                  .getExaminationRoomRoute(
                                subject: 3,
                                index: 1,
                                rows: 200,
                                division: int.parse(logic
                                        .homeRepository.cityInfo.cityCode) ??
                                    440100,
                              );
                            } catch (e) {
                              data = null;
                            }
                            if (data != null) {
                              if (Get.isRegistered<SectionThreeController>(
                                tag: ExaminationPageState.key.toString(),
                              )) {
                                Get.find<SectionThreeController>(
                                  tag: ExaminationPageState.key.toString(),
                                ).goCatalogPage(1);
                                return;
                              }
                              Get.toNamed(AppRoutes.subjectThreeCatalogPage,
                                  arguments: {
                                    'selectIndex': 1,
                                    'isNeedLoadingData': true
                                  });
                            } else {
                              showDialog(
                                context: Get.context!,
                                barrierDismissible: false, // 点击空白区域不可关闭
                                builder: (BuildContext context) {
                                  return Dialog(
                                    child: U3dConfirmDialog(
                                      content: '即将打开3D练车',
                                      cancelText: '取消',
                                      confirmText: '确认',
                                      onCancel: () {
                                        Get.back();
                                      },
                                      onConfirm: () {
                                        Get.back();
                                        IPlatform.get().jumpLightSimulation(
                                            'subject3', '');
                                      },
                                    ),
                                  );
                                },
                              );
                            }
                          } else if (index == 3) {
                            JumpUtils.get()
                                .jumpEssence(JkKey.SUBFOUR, isTiming: false);
                          }
                        }
                      : () {
                          if (index == 0) {
                            JumpUtils.get()
                                .jumpBuyVip(vipType: 1, type: 'kaoqianmijuan');
                          } else if (index == 1) {
                            _buyVio2(logic);
                          } else if (index == 2) {
                            logic.timingRepository.goBuyTypeVip(2);
                          } else if (index == 3) {
                            JumpUtils.get()
                                .jumpBuyVip(vipType: 4, type: 'kaoqianmijuan');
                          }
                        },
                  child: Column(
                    children: [
                      index == 1 || index == 2
                          ? assImg2(img: 'mine_vip_card_kmn', w: 34.w, h: 34.h)
                          : assImg2(img: 'mine_vip_card_mj', w: 34.w, h: 34.h),
                      SizedBox(height: 8.h),
                      index == 1 || index == 2
                          ? app13spC66666Text('考模拟',
                              color: AppColors.white,
                              fontFamily: 'PingFangSC-Medium')
                          : app13spC66666Text('密卷',
                              color: AppColors.white,
                              fontFamily: 'PingFangSC-Medium'),
                      SizedBox(height: 3.h),
                      index == 1 || index == 2
                          ? app11spAAAText('考场1:1',
                              color: AppColors.F9DBBC,
                              fontFamily: 'PingFangSC-Regular')
                          : app11spAAAText('考前押题',
                              color: AppColors.F9DBBC,
                              fontFamily: 'PingFangSC-Regular')
                    ],
                  ),
                ),
              ],
            )
          ],
        )
        // Center(
        //   child: Text(
        //     '卡片 ${_colors.indexOf(color) + 1}',
        //     style: TextStyle(color: Colors.white, fontSize: 24),
        //   ),
        // ),
        );
  }

  _buyVio2(MineController logic, {type = '3dlianche'}) {
    int province =
        stringToIntConverterProvince(logic.homeRepository.cityInfo?.cityCode);
    String cityName =
        cityMap[logic.homeRepository.cityInfo?.cityCode.substring(0, 4)];
    final shopPath =
        '/pages/vip/part2/part2?province=$province&city=${logic.homeRepository.cityInfo?.cityCode}&cityname=$cityName&type=$type';
    JumpSmallProgramUtils.jump(shopPath, "fc2259710004813765");
  }

  //省级区划代码
  int stringToIntConverterProvince(String? str) {
    if (str == null || str.isEmpty) return 0;
    str = str.substring(0, 2);
    str = '${str}0000';
    return int.parse(str);
  }

  @override
  Widget build(
    BuildContext context,
  ) {
    return GetBuilder<MineController>(
        init: MineController(),
        builder: (logic) {
          return Padding(
            padding: Platform.isAndroid
                ? const EdgeInsets.only(bottom: kBottomNavigationBarHeight)
                : const EdgeInsets.only(
                    bottom: kBottomNavigationBarHeight + 30),
            child: Scaffold(
              backgroundColor: AppColors.F6F5F5,
              extendBodyBehindAppBar: true,
              appBar: AppBar(
                systemOverlayStyle: SystemUiOverlayStyle(
                    statusBarColor: Colors.transparent,
                    statusBarIconBrightness: Brightness.dark),
                backgroundColor: logic.appBarColor,
                leading: GestureDetector(
                  onTap: logic.userAccount?.isBind == 1 && logic.userAccount?.platGradStatus !=3
                      ? () {
                          Toast.show('您已绑定驾校,不可切换题库!');
                        }
                      : logic.userAccount?.isBind == 1 && logic.userAccount?.platGradStatus ==3
                      ?(){
                    Get.toNamed(AppRoutes.topicSelectPage);
                  }:() {
                          // widget.gotoSelectTrain.call();
                          Get.toNamed(AppRoutes.homeStudyTypeSelect);
                        },
                  child: Row(
                    children: [
                      Align(
                              alignment: Alignment.centerLeft,
                              child: app14spA1Text(
                                  logic.isChangeCarType==null||logic.isChangeCarType!.isEmpty? "题库：${trainTypeMap[logic.userAccount?.topicType ?? "C1"] ?? 'load...'}"
                                      :"题库：${trainTypeMap[logic.isChangeCarType ?? "C1"] ?? 'load...'}",
                                  // "题库：${trainTypeMap[logic.userAccount?.topicType ?? "C1"]}",
                                  fontFamily: 'PingFangSC-Medium'))
                          .paddingOnly(left: 16.w, right: 2.w),
                      assImg2(img: 'car_pull', w: 6.w, h: 6.h)
                          .paddingOnly(top: 2.h)
                    ],
                  ),
                ),
                leadingWidth: double.infinity,
                // actions: [
                //   GestureDetector(
                //     onTap: () {
                //       // gotoSetting.call();
                //       Get.toNamed(AppRoutes.settings);
                //       // Toast.show("设置");
                //     },
                //     child: assImg2(img: 'nav_icon_shezhi', w: 24.w, h: 24.h)
                //         .paddingOnly(right: 16.w),
                //   ),
                // ],
              ),
              body: _mainView(context, logic),

              //     SingleChildScrollView(
              //           child: Container(
              //              child: Column(
              //                children: [
              //                  SliverAppBar(
              //           expandedHeight: 350.0.h,
              //           floating: true,
              //           snap: true,
              //           leading: Center(child: app14spA1Text("题库：小车")),
              //           // Container(
              //           //   padding: EdgeInsets.only(left: 16.w),
              //           //   child: Center(child: app14spA1Text("题库：C1"),)
              //           // ),
              //           leadingWidth: 100.w,
              //           // titleSpacing: 80,
              //
              //           // primary: true,
              //           automaticallyImplyLeading: false,
              //           pinned: true,
              //           stretch: true,
              //            // stretchTriggerOffset: 100,
              //           flexibleSpace: FlexibleSpaceBar(
              //               centerTitle: true,
              //               titlePadding: EdgeInsetsDirectional.only(
              //               top: 124.h,bottom: 0
              //               ),
              //               collapseMode: CollapseMode.none,
              //               title: Expanded(
              //               // padding: EdgeInsets.all(30),
              //               // width: 120,height: 120,
              //                 child: SingleChildScrollView(
              //                     child: GestureDetector(
              //                       onTap: (){
              //                         gotoPersonalInfo.call();
              //                         Toast.show('跳转个人中心');
              //                       },
              //                       child: Column(
              //                         children: [
              //                           Stack(
              //                             children: [
              //                               Container(
              //                                 // height: 160.h,
              //                                 // width: 160.w,
              //                                 decoration: BoxDecoration(
              //                                     image: logic.isTiming?DecorationImage(image: assetImg2(img: 'img_my_touxiang_vip'),fit: BoxFit.contain):null
              //                                 ),
              //                                 child: assImg2(img: 'avatar',w: 120.w,h: 120.h).paddingSymmetric(horizontal: 80.w),
              //                               ),
              //                               Positioned(
              //                                   bottom: 0,
              //                                   left: 95.w,
              //                                   child: assImg2(img: 'icon_my_vip',h: 20.h,w: 46.w)
              //                               ),
              //                             ],
              //                           ),
              //
              //                           // avatar
              //                           // CircleAvatar(
              //                           //   backgroundColor: Colors.transparent,
              //                           //   radius: 50,
              //                           //   backgroundImage: assetImg2(img: 'img_my_touxiang_vip'),
              //                           //   child: assImg2(img: 'avatar',w: 120.w,h: 120.h).paddingSymmetric(horizontal: 80.w),
              //                           // ),
              //
              //                           app16spA1Text("${'ddddd'}").paddingOnly(top: 15.h,bottom: 9.h),
              //                           app12sp268FF7Text("未绑定驾校，去绑定驾校",maxLines: 1).paddingOnly(bottom: 20.h)
              //                         ],
              //                       ),
              //                     )
              //
              //                 )
              //               ),
              //               background: assImg(img: 'bg',fit: BoxFit.fill)
              //           ),
              //         ),
              //                  SliverToBoxAdapter(
              //                   child: Container(
              //                     margin: EdgeInsets.all(16.w),
              //                     width: double.infinity,
              //                     decoration: BoxDecoration(
              //                         color: AppColors.E1F1FF,
              //                         borderRadius: BorderRadius.circular(8.r)
              //                     ),
              //                     child: Column(
              //                       children: [
              //                         SizedBox(
              //                           height: 50.h,
              //                           child: Row(
              //                             mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              //                             crossAxisAlignment: CrossAxisAlignment.center,
              //                             children: [
              //                               GestureDetector(
              //                                 onTap: (){
              //                                   setState(() {
              //                                     logic.isSubOne=true;
              //                                   });
              //                                 },
              //                                 child: subjectTabContainer(
              //                                     color: logic.isSubOne?AppColors.white:null,
              //                                     child:Center(child: logic.isSubOne?app16sp268fText('科一'):app14sp4e4eText('科一'))
              //                                 ),
              //                               ),
              //                               GestureDetector(
              //                                 onTap: (){
              //                                   setState(() {
              //                                     logic.isSubOne=false;
              //                                   });
              //                                 },
              //                                 child: subjectTabContainer(
              //                                     color: logic.isSubOne?null:AppColors.white,
              //                                     child:Center(child: logic.isSubOne?app14sp4e4eText('科四'):app16sp268fText('科四'))
              //                                 ),
              //                               ),
              //
              //                               const Spacer(
              //                                 flex: 1,
              //                               ),
              //                               app10sp268FF7Text("计时开关"),
              //                               CustomSwitch(
              //                                 value: logic.isTiming,
              //                                 onChanged: (val){
              //                                   logic.isTiming = val;
              //                                   setState(() {});
              //                                 }
              //                               ).paddingOnly(left: 8.w),
              //                              //  FlutterSwitch(
              //                              //      height: 25.h,
              //                              //      activeColor: AppColors.C7DBEFF,
              //                              //      width: 60.w,
              //                              //      padding: 4.w,
              //                              //      value: logic.isTiming,
              //                              //      onToggle: (val){
              //                              //        logic.isTiming = val;
              //                              //        setState(() {});
              //                              //      }
              //                              // ),
              //                               // Transform.scale(
              //                               //   scale: 0.8,
              //                               //   child: Switch(
              //                               //       value: logic.isTiming,
              //                               //       activeColor: AppColors.C7DBEFF,
              //                               //       onChanged: (va) {
              //                               //
              //                               //       }
              //                               //   ),
              //                               // ),
              //                               assImg2(img: 'icon_my_wenhao',w: 12.w,h: 12.h).paddingSymmetric(horizontal: 11.w),
              //                             ],
              //                           ),
              //                         ),
              //                         Container(
              //                           padding: EdgeInsets.only(top: 14.h,bottom: 16.h),
              //                           decoration: BoxDecoration(
              //                               color: Colors.white, borderRadius: BorderRadius.only(topRight: Radius.circular(12.r))
              //                           ),
              //                           child: Column(
              //                             children: [
              //                               Row(
              //                                 mainAxisAlignment: MainAxisAlignment.spaceAround,
              //                                 crossAxisAlignment: CrossAxisAlignment.center,
              //                                 children: [
              //                                   Column(
              //                                     children: [
              //                                       app24spFF5757Text("1,250"),
              //                                       app12sp1A1AText('已做题').paddingOnly(top: 10.h,bottom: 4.h),
              //                                       app10spAAAA7Text("正确率40%"),
              //                                     ],
              //                                   ),
              //                                   Column(
              //                                     children: [
              //                                       app24sp3333Text("98"),
              //                                       app12sp1A1AText("模拟均分").paddingOnly(top: 10.h,bottom: 4.h),
              //                                       app10spAAAA7Text("累计考试12次"),
              //                                     ],
              //                                   ),
              //                                   Container(
              //                                     // height: 98.h,
              //                                     // width: 88.h,
              //                                     decoration: BoxDecoration(
              //                                         image: DecorationImage(
              //                                             image: assetImg2(img: 'bg_my_vipkecheng'),
              //                                             fit: BoxFit.fill)
              //                                     ),
              //                                     child: Column(
              //                                       crossAxisAlignment: CrossAxisAlignment.center,
              //                                       mainAxisAlignment: MainAxisAlignment.center,
              //                                       children: [
              //                                         app24spA86000Text("80%"),
              //                                         app10spA86000Text("考试通过率").paddingOnly(top: 4.h,bottom: 12.h),
              //                                         Container(
              //                                           padding: EdgeInsets.only(top: 2.h,left: 8.w,bottom: 2.h,right: 5.w),
              //                                           decoration: BoxDecoration(
              //                                             borderRadius: BorderRadius.circular(10.r),
              //                                             gradient: const LinearGradient(
              //                                                 begin: Alignment.centerLeft,
              //                                                 colors: [AppColors.FE9229,AppColors.FC5912],
              //                                                 end: Alignment.centerRight),
              //                                           ),
              //                                           child: Center(
              //                                             child: app12spFFFFFText('VIP课程'),
              //                                           ),
              //                                         ).paddingSymmetric(horizontal: 16.w),
              //                                         SizedBox(height: 12.h)
              //                                       ],
              //                                     ),
              //                                   ),
              //                                 ],
              //                               ).paddingOnly(bottom: 10.h),
              //                               Row(
              //                                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //                                 crossAxisAlignment: CrossAxisAlignment.center,
              //                                 children: [
              //                                   Expanded(
              //                                     child: Container(
              //                                       padding: EdgeInsets.only(left: 12.w,top: 8.h,bottom: 8.h),
              //                                       decoration: BoxDecoration(
              //                                           image: DecorationImage(
              //                                               image: assetImg2(img: 'my_bg_cuoti'),
              //                                               fit: BoxFit.cover)
              //                                       ),
              //                                       child: Column(
              //                                         crossAxisAlignment: CrossAxisAlignment.start,
              //                                         mainAxisAlignment: MainAxisAlignment.center,
              //                                         children: [
              //                                           app20sp33333Text('1,250'),
              //                                           app10spAAAA7Text('我的错题')
              //                                         ],
              //                                       ),
              //                                     ).paddingOnly(left: 16.w),
              //                                   ),
              //                                   SizedBox(
              //                                     width: 12.w,
              //                                   ),
              //                                   Expanded(
              //                                     child: Container(
              //                                       padding: EdgeInsets.only(left: 12.w,top: 8.h,bottom: 8.h),
              //                                       decoration: BoxDecoration(
              //                                           image: DecorationImage(
              //                                               image: assetImg2(img: 'my_bg_shoucang'),
              //                                               fit: BoxFit.cover)),
              //                                       child: Column(
              //                                         crossAxisAlignment: CrossAxisAlignment.start,
              //                                         mainAxisAlignment: MainAxisAlignment.center,
              //                                         children: [
              //                                           app20sp33333Text("1,250"),
              //                                           app10spAAAA7Text("我的收藏"),
              //                                         ],
              //                                       ),
              //                                     ),
              //                                   ),
              //                                 ],
              //                               )
              //                             ],
              //                           ),
              //                         ),
              //                       ],
              //                     ),
              //                   )
              //               ),
              //                  SliverToBoxAdapter(
              //                   child: Container(
              //                     padding: EdgeInsets.only(top: 16.h,bottom: 16.h),
              //                     margin: EdgeInsets.only(left: 16.w,right: 16.w,bottom: 20.h),
              //                     width: double.infinity,
              //                     decoration: BoxDecoration(
              //                         color: Colors.white, borderRadius: BorderRadius.circular(8.r)
              //                     ),
              //                     child: Row(
              //                       mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              //                       children: [
              //                         meIconButton2(icon: 'my_icon_wodechengji', text: '我的成绩', onTap: () {}),
              //                         meIconButton2(icon: 'my_icon_wodedingdan', text: '我的订单', onTap: () {}),
              //                         meIconButton2(icon: 'my_icon_fuwu', text: '我的服务', onTap: () {}),
              //                         meIconButton2(icon: 'my_icon_youhuiquan', text: '优惠券', onTap: () {})
              //                       ],
              //                     ),
              //                   )
              //               ),
              //                  SliverToBoxAdapter(
              //           child: white8radiusContainer(
              //             height: 371.h,
              //             child: ListView.builder(itemBuilder: (_,index){
              //               return ListTile(
              //                 leading: assImg2(img: iconList[index],w: 24.w,h: 24.h),
              //                 title: app14spA1Text(titleList[index]),
              //                 trailing: assImg2(img: 'icon_more',w: 16.w,h: 16.h),
              //               );
              //             },itemCount: iconList.length,physics: const NeverScrollableScrollPhysics(),)
              //           ).paddingSymmetric(horizontal: 16.w),
              //         )
              //       ],
              //     )
              // )
              // )
            ),
          );
        });
  }

  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;
}

class SecretDialog extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Dialog(
      surfaceTintColor: Colors.transparent,
      backgroundColor: Colors.transparent, // 背景透明
      child: Stack(
        alignment: Alignment.center,
        children: [
          Container(
            margin: const EdgeInsets.all(40),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
            ),
            child: InkWell(
                onTap: () {
                  Get.back();
                  Get.find<MainController>()
                      .timingRepository
                      .openFluwxMiniProgram(
                          "pages/home/<USER>",
                          "gh_b47e16cc160c");
                },
                child: assImg2(img: "bg_secret")),
          ),
          Positioned(
            right: 0,
            top: 0,
            child: IconButton(
              icon: Icon(
                Icons.close,
                size: 30.w,
                color: Colors.white,
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ),
        ],
      ),
    );
  }
}

// class CardCarouselDemo extends StatefulWidget {
//
//   @override
//   _CardCarouselDemoState createState() => _CardCarouselDemoState();
// }
//
// class _CardCarouselDemoState extends State<CardCarouselDemo> {
//   final PageController _pageController = PageController(
//     viewportFraction: 0.96, // 控制卡片可见区域（留出两侧部分空间）
//   );
//   int _currentPage = 0;
//   final List<Color> _colors = [
//     Colors.blue,
//     Colors.green,
//     Colors.orange,
//     Colors.purple,
//   ];
//
//   @override
//   void initState() {
//     super.initState();
//     // 可选：自动轮播
//     // _startAutoPlay();
//   }
//
//   void _startAutoPlay() {
//     Future.delayed(Duration(seconds: 3), () {
//       if (_currentPage < _colors.length - 1) {
//         _currentPage++;
//       } else {
//         _currentPage = 0;
//       }
//       _pageController.animateToPage(
//         _currentPage,
//         duration: Duration(milliseconds: 500),
//         curve: Curves.ease,
//       );
//       _startAutoPlay();
//     });
//   }
//
//   @override
//   void dispose() {
//     _pageController.dispose();
//     super.dispose();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       children: [
//         Expanded(
//           child: PageView.builder(
//             controller: _pageController,
//             onPageChanged: (index) {
//               setState(() => _currentPage = index);
//             },
//             itemCount: _colors.length,
//             itemBuilder: (context, index) {
//               return AnimatedBuilder(
//                 animation: _pageController,
//                 builder: (context, child) {
//                   double value = 0;
//                   if (_pageController.position.haveDimensions) {
//                     value = index.toDouble() - (_pageController.page ?? 0);
//                     value = (value * 0.1).clamp(-1, 1); // 控制缩放系数
//                   }
//                   return Transform.scale(
//                     scale: 1 - value.abs() * 0.2, // 缩放动画
//                     child: child,
//                   );
//                 },
//                 child: _buildCard(_colors[index],index),
//               );
//             },
//           ),
//         ),
//         // SizedBox(height: 20),
//         // _buildPageIndicator(),
//       ],
//     );
//   }
//
//   Widget _buildCard(Color color,int index) {
//     return Container(
//       margin: EdgeInsets.symmetric(horizontal: 1),
//       decoration: BoxDecoration(
//         // color: color,
//         image: DecorationImage(
//           image: assetImg2(img: 'mine_vip_card_bg'),
//         ),
//         borderRadius: BorderRadius.circular(16),
//       ),
//       child: Column(
//         children: [
//           Row(
//             children: [
//               Column(
//                 children: [
//                   Row(
//                     children: [
//                       assImg2(img: 'mine_vip_card_huangguan', w: 16.w, h: 16.h),
//                       SizedBox(width: 4.w),
//                       app16spAC5000Text('科目一VIP', color: AppColors.E2AE73,fontFamily: 'PingFangSC-Semibold')
//                     ],
//                   ),
//                   app14sp268Text('3天轻松过科一', color: AppColors.E2AE73, fontFamily: 'PingFangSC-Medium')
//                 ],
//               ),
//               Spacer(),
//               GestureDetector(
//                 onTap: (){
//                   debugPrint('点击立即开通$index');
//                 },
//                 child: Container(
//                   height: 30.h,
//                   width: 90.w,
//                   decoration: BoxDecoration(
//                     gradient: const LinearGradient(
//                         begin: Alignment.centerLeft,
//                         colors: [AppColors.DDAA6D, AppColors.F1AE55]),
//                     borderRadius: BorderRadius.circular(15.r),
//                   ),
//                   child: Center(
//                     child: app13spC66666Text('立即开通 >', color: AppColors.C663409,fontFamily: 'PingFangSC-Semibold'),
//                   ),
//                 ),
//               ),
//               SizedBox(width: 15.w),
//             ],
//           ).paddingOnly(left: 20.w, top: 12.h, bottom: 15.h),
//           Row(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//             children: [
//               Column(
//                 children: [
//                   assImg2(img: 'mine_vip_card_jxt', w: 34.w, h: 34.h),
//                   SizedBox(height: 8.h),
//                   app13spC66666Text('精选题',color: AppColors.white,fontFamily: 'PingFangSC-Medium'),
//                   SizedBox(height: 3.h),
//                   app11spAAAText('600题',color: AppColors.F9DBBC,fontFamily: 'PingFangSC-Regular')
//                 ],
//               ),
//               assImg2(img: 'mine_vip_card_jiantou', w: 14.w, h: 14.h).paddingOnly(top: 10.h),
//               Column(
//                 children: [
//                   assImg2(img: 'mine_vip_card_mock', w: 34.w, h: 34.h),
//                   SizedBox(height: 8.h),
//                   app13spC66666Text('模考',color: AppColors.white,fontFamily: 'PingFangSC-Medium'),
//                   SizedBox(height: 3.h),
//                   app11spAAAText('考场1:1',color: AppColors.F9DBBC,fontFamily: 'PingFangSC-Regular')
//                 ],
//               ),
//               assImg2(img: 'mine_vip_card_jiantou', w: 14.w, h: 14.h).paddingOnly(top: 10.h),
//               Column(
//                 children: [
//                   assImg2(img: 'mine_vip_card_mj', w: 34.w, h: 34.h),
//                   SizedBox(height: 8.h),
//                   app13spC66666Text('密卷',color: AppColors.white,fontFamily: 'PingFangSC-Medium'),
//                   SizedBox(height: 3.h),
//                   app11spAAAText('考前押题',color: AppColors.F9DBBC,fontFamily: 'PingFangSC-Regular')
//                 ],
//               ),
//             ],
//           )
//         ],
//       )
//       // Center(
//       //   child: Text(
//       //     '卡片 ${_colors.indexOf(color) + 1}',
//       //     style: TextStyle(color: Colors.white, fontSize: 24),
//       //   ),
//       // ),
//     );
//   }
//
//   Widget _buildPageIndicator() {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.center,
//       children: _colors.map((color) {
//         int index = _colors.indexOf(color);
//         return Container(
//           width: 8,
//           height: 8,
//           margin: EdgeInsets.symmetric(horizontal: 4),
//           decoration: BoxDecoration(
//             shape: BoxShape.circle,
//             color: _currentPage == index ? Colors.blue : Colors.grey,
//           ),
//         );
//       }).toList(),
//     );
//   }
// }
