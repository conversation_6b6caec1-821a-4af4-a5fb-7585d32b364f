import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import 'package:exercise_bloc/src/presentation/bloc/exercise/exercise_bloc.dart';
import 'package:exercise_bloc/src/presentation/bloc/exercise/exercise_event.dart';
import 'package:exercise_bloc/src/presentation/bloc/exercise/exercise_state.dart';
import 'package:exercise_bloc/src/domain/entities/question_entity.dart';
import 'package:exercise_bloc/src/domain/entities/exercise_progress.dart';
import 'package:exercise_bloc/src/domain/usecases/load_questions.dart';
import 'package:exercise_bloc/src/domain/usecases/answer_question.dart';
import 'package:exercise_bloc/src/domain/usecases/save_progress.dart';
import 'package:exercise_bloc/src/domain/repositories/question_repository.dart';

// Mock classes
class MockLoadQuestions extends Mock implements LoadQuestions {}
class MockAnswerQuestion extends Mock implements AnswerQuestion {}
class MockSaveProgress extends Mock implements SaveProgress {}
class MockQuestionRepository extends Mock implements QuestionRepository {}

void main() {
  group('ExerciseBloc', () {
    late ExerciseBloc exerciseBloc;
    late MockLoadQuestions mockLoadQuestions;
    late MockAnswerQuestion mockAnswerQuestion;
    late MockSaveProgress mockSaveProgress;
    late MockQuestionRepository mockQuestionRepository;

    setUp(() {
      mockLoadQuestions = MockLoadQuestions();
      mockAnswerQuestion = MockAnswerQuestion();
      mockSaveProgress = MockSaveProgress();
      mockQuestionRepository = MockQuestionRepository();

      exerciseBloc = ExerciseBloc(
        loadQuestions: mockLoadQuestions,
        answerQuestion: mockAnswerQuestion,
        saveProgress: mockSaveProgress,
        questionRepository: mockQuestionRepository,
      );
    });

    tearDown(() {
      exerciseBloc.close();
    });

    test('initial state is ExerciseInitial', () {
      expect(exerciseBloc.state, equals(const ExerciseInitial()));
    });

    group('LoadQuestions', () {
      final mockQuestions = [
        const QuestionEntity(
          id: 1,
          sortId: 1,
          title: 'Test Question 1',
          type: QuestionType.single,
          options: [
            QuestionOption(key: 'a', text: 'Option A'),
            QuestionOption(key: 'b', text: 'Option B'),
          ],
          correctAnswer: 'a',
        ),
        const QuestionEntity(
          id: 2,
          sortId: 1,
          title: 'Test Question 2',
          type: QuestionType.multiple,
          options: [
            QuestionOption(key: 'a', text: 'Option A'),
            QuestionOption(key: 'b', text: 'Option B'),
            QuestionOption(key: 'c', text: 'Option C'),
          ],
          correctAnswer: 'ab',
        ),
      ];

      blocTest<ExerciseBloc, ExerciseState>(
        'emits [ExerciseLoading, ExerciseLoaded] when LoadQuestions is added successfully',
        build: () {
          when(() => mockLoadQuestions(any())).thenAnswer((_) async => mockQuestions);
          return exerciseBloc;
        },
        act: (bloc) => bloc.add(const LoadQuestions(
          subject: 1,
          type: 'chapter',
          sortId: 1,
        )),
        expect: () => [
          const ExerciseLoading(),
          isA<ExerciseLoaded>()
            .having((state) => state.questions.length, 'questions length', 2)
            .having((state) => state.currentIndex, 'current index', 0)
            .having((state) => state.progress.subject, 'progress subject', 1)
            .having((state) => state.progress.type, 'progress type', 'chapter'),
        ],
        verify: (_) {
          verify(() => mockLoadQuestions(any())).called(1);
        },
      );

      blocTest<ExerciseBloc, ExerciseState>(
        'emits [ExerciseLoading, ExerciseError] when LoadQuestions fails',
        build: () {
          when(() => mockLoadQuestions(any())).thenThrow(Exception('Network error'));
          return exerciseBloc;
        },
        act: (bloc) => bloc.add(const LoadQuestions(
          subject: 1,
          type: 'chapter',
          sortId: 1,
        )),
        expect: () => [
          const ExerciseLoading(),
          isA<ExerciseError>()
            .having((state) => state.message, 'error message', contains('Failed to load questions')),
        ],
      );

      blocTest<ExerciseBloc, ExerciseState>(
        'emits [ExerciseLoading, ExerciseError] when no questions found',
        build: () {
          when(() => mockLoadQuestions(any())).thenAnswer((_) async => []);
          return exerciseBloc;
        },
        act: (bloc) => bloc.add(const LoadQuestions(
          subject: 1,
          type: 'chapter',
          sortId: 1,
        )),
        expect: () => [
          const ExerciseLoading(),
          const ExerciseError('No questions found'),
        ],
      );
    });

    group('AnswerQuestion', () {
      const mockQuestion = QuestionEntity(
        id: 1,
        sortId: 1,
        title: 'Test Question',
        type: QuestionType.single,
        options: [
          QuestionOption(key: 'a', text: 'Option A'),
          QuestionOption(key: 'b', text: 'Option B'),
        ],
        correctAnswer: 'a',
      );

      const mockProgress = ExerciseProgress(
        subject: 1,
        type: 'chapter',
        currentIndex: 0,
        totalQuestions: 1,
        correctCount: 0,
        wrongCount: 0,
        answeredQuestions: {},
      );

      const initialState = ExerciseLoaded(
        questions: [mockQuestion],
        currentIndex: 0,
        progress: mockProgress,
      );

      const mockAnswerResult = AnswerResult(
        isCorrect: true,
        correctAnswer: 'a',
        explanation: 'Test explanation',
      );

      blocTest<ExerciseBloc, ExerciseState>(
        'emits QuestionAnswered when answer is correct',
        build: () {
          when(() => mockAnswerQuestion(any())).thenAnswer((_) async => mockAnswerResult);
          when(() => mockSaveProgress(any())).thenAnswer((_) async {});
          return exerciseBloc;
        },
        seed: () => initialState,
        act: (bloc) => bloc.add(const AnswerQuestion(
          questionIndex: 0,
          answer: 'a',
        )),
        expect: () => [
          isA<QuestionAnswered>()
            .having((state) => state.isCorrect, 'is correct', true)
            .having((state) => state.correctAnswer, 'correct answer', 'a')
            .having((state) => state.explanation, 'explanation', 'Test explanation')
            .having((state) => state.progress.correctCount, 'correct count', 1),
        ],
        verify: (_) {
          verify(() => mockAnswerQuestion(any())).called(1);
          verify(() => mockSaveProgress(any())).called(1);
        },
      );

      blocTest<ExerciseBloc, ExerciseState>(
        'does not answer if question is already answered',
        build: () => exerciseBloc,
        seed: () => ExerciseLoaded(
          questions: [mockQuestion.copyWith(isAnswered: true)],
          currentIndex: 0,
          progress: mockProgress,
        ),
        act: (bloc) => bloc.add(const AnswerQuestion(
          questionIndex: 0,
          answer: 'a',
        )),
        expect: () => [],
        verify: (_) {
          verifyNever(() => mockAnswerQuestion(any()));
        },
      );
    });

    group('Navigation', () {
      const mockQuestions = [
        QuestionEntity(
          id: 1,
          sortId: 1,
          title: 'Question 1',
          type: QuestionType.single,
          options: [QuestionOption(key: 'a', text: 'Option A')],
          correctAnswer: 'a',
        ),
        QuestionEntity(
          id: 2,
          sortId: 1,
          title: 'Question 2',
          type: QuestionType.single,
          options: [QuestionOption(key: 'a', text: 'Option A')],
          correctAnswer: 'a',
        ),
      ];

      const mockProgress = ExerciseProgress(
        subject: 1,
        type: 'chapter',
        currentIndex: 0,
        totalQuestions: 2,
        correctCount: 0,
        wrongCount: 0,
        answeredQuestions: {},
      );

      blocTest<ExerciseBloc, ExerciseState>(
        'navigates to next question',
        build: () => exerciseBloc,
        seed: () => const ExerciseLoaded(
          questions: mockQuestions,
          currentIndex: 0,
          progress: mockProgress,
        ),
        act: (bloc) => bloc.add(const NextQuestion()),
        expect: () => [
          isA<ExerciseLoaded>()
            .having((state) => state.currentIndex, 'current index', 1)
            .having((state) => state.canGoPrevious, 'can go previous', true)
            .having((state) => state.canGoNext, 'can go next', false),
        ],
      );

      blocTest<ExerciseBloc, ExerciseState>(
        'navigates to previous question',
        build: () => exerciseBloc,
        seed: () => const ExerciseLoaded(
          questions: mockQuestions,
          currentIndex: 1,
          progress: mockProgress,
        ),
        act: (bloc) => bloc.add(const PreviousQuestion()),
        expect: () => [
          isA<ExerciseLoaded>()
            .having((state) => state.currentIndex, 'current index', 0)
            .having((state) => state.canGoPrevious, 'can go previous', false)
            .having((state) => state.canGoNext, 'can go next', true),
        ],
      );

      blocTest<ExerciseBloc, ExerciseState>(
        'jumps to specific question',
        build: () => exerciseBloc,
        seed: () => const ExerciseLoaded(
          questions: mockQuestions,
          currentIndex: 0,
          progress: mockProgress,
        ),
        act: (bloc) => bloc.add(const JumpToQuestion(1)),
        expect: () => [
          isA<ExerciseLoaded>()
            .having((state) => state.currentIndex, 'current index', 1),
        ],
      );
    });
  });
}
