import 'package:cached_network_image/cached_network_image.dart';
import 'package:component_library/component_library.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class LoadingGifWidget extends StatelessWidget {
  const LoadingGifWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Image.asset(
        "assets/home_img/loading.gif",
        width: 100.w,
        height: 100.w,
        fit: BoxFit.fill,
      ),
    );
  }
}

class DashedLineWidget extends StatelessWidget {
  final double width;
  final double height;
  final double dashWidth;
  final double dashSpace;
  final Color color;

  DashedLineWidget({
    required this.width,
    required this.height,
    required this.dashWidth,
    required this.dashSpace,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: Size(width, height),
      painter: Dashed<PERSON>inePainter(dashWidth, dashSpace, color),
    );
  }
}

class DashedLinePainter extends CustomPainter {
  final double dashWidth;
  final double dashSpace;
  final Color color;

  DashedLinePainter(this.dashWidth, this.dashSpace, this.color);

  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint()
      ..color = color
      ..strokeWidth = size.height
      ..style = PaintingStyle.stroke;

    double startX = 0;
    while (startX < size.width) {
      canvas.drawLine(Offset(startX, 0), Offset(startX + dashWidth, 0), paint);
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class InformationBannerWidget extends StatelessWidget {
  int subject;
  InformationBannerWidget({super.key, required this.subject});

  @override
  Widget build(BuildContext context) {
    if (defaultTargetPlatform == TargetPlatform.android) {
      return Obx(() => BeiziAdPlugin.instance.isCloseInformation.value
          ? CachedNetworkImage(
        imageUrl:
        "https://dx-stg.oss-cn-shenzhen.aliyuncs.com/app/resources/dxjd-home-banner-default.png",
        width: 353.w,
        height: 100.h,
      ):Stack(
        children: [
          SizedBox(
            height: 100.h,
            width: 353.w,
            child: AndroidView(
              viewType: 'plugins.beizi_ad_sdk/BannerAd',
              creationParams: {
                'avrId': "118226",
              },
              creationParamsCodec: StandardMessageCodec(),
            ),
          ),
          // Positioned(
          //   right: 4.w,
          //   top: 3.h,
          //   child: InkWell(
          //     onTap: () {
          //       BeiziAdPlugin.instance.setIsInformationClose = true;
          //     },
          //     child: Image.asset(
          //       "assets/home_img/icon_close_write.png",
          //       width: 20.w,
          //       height: 20.w,
          //     ),
          //   ),
          // )
        ],
      ));
    } else if (defaultTargetPlatform == TargetPlatform.iOS) {
      return Obx(() => BeiziAdPlugin.instance.isCloseInformation.value
          ? CachedNetworkImage(
              imageUrl:
                  "https://dx-stg.oss-cn-shenzhen.aliyuncs.com/app/resources/dxjd-home-banner-default.png",
              width: 353.w,
              height: 100.h,
              fit: BoxFit.fill,
            )
          : SizedBox(
            height: 100.h,
            width: 353.w,
            child: UiKitView(
              viewType: 'beizi_banner_view',
              creationParams: {
                'width': 353.w,
                'height': 100.h,
              },
              creationParamsCodec: const StandardMessageCodec(),
            ),
          ));
    } else {
      return Container(
          margin: const EdgeInsets.fromLTRB(11, 0, 11, 0),
          child: ClipRRect(
              borderRadius: const BorderRadius.all(Radius.circular(6)),
              child: CachedNetworkImage(
                imageUrl:
                    "https://dx-stg.oss-cn-shenzhen.aliyuncs.com/app/resources/dxjd-home-banner-default.png",
                width: 353.h,
                height: 100.w,
                errorWidget: (context, str, error) {
                  return SizedBox(
                    width: 100.w,
                    height: 353.h,
                  );
                },
                fit: BoxFit.fill,
              )));
    }
  }
}

class U3dConfirmDialog extends StatelessWidget {
  String content;
  String cancelText;
  String confirmText;
  Function onCancel;
  Function onConfirm;
  U3dConfirmDialog(
      {super.key,
      required this.content,
      required this.cancelText,
      required this.confirmText,
      required this.onCancel,
      required this.onConfirm});
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        gradient: const LinearGradient(
            colors: [Color(0xFFFFFFFF), Color(0xFFE5F3FF)],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: REdgeInsets.symmetric(vertical: 18, horizontal: 38),
            child: Text(
              content,
              style: TextStyle(
                  color: Color(0xFF000000),
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  fontFamily: "FingFangSC-Bold"),
            ),
          ),
          Container(
            color: const Color(0xFF99B0B9),
            height: 0.3.h,
            width: double.infinity,
          ),
          SizedBox(
            height: 44.h,
            child: Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () {
                      onCancel();
                    },
                    child: Container(
                      alignment: Alignment.center,
                      child: Text(
                        cancelText,
                        style: TextStyle(
                            color: Color(0xFF007AFF),
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w600,
                            fontFamily: "FingFangSC-Medium"),
                      ),
                    ),
                  ),
                ),
                Container(
                  color: const Color(0xFF99B0B9),
                  width: 0.3.h,
                ),
                Expanded(
                  child: InkWell(
                    onTap: () {
                      onConfirm();
                    },
                    child: Container(
                      alignment: Alignment.center,
                      child: Text(
                        confirmText,
                        style: TextStyle(
                            color: Color(0xFF007AFF),
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w600,
                            fontFamily: "FingFangSC-Medium"),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
