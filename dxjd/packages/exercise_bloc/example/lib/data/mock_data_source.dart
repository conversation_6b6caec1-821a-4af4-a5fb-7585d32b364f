import 'package:exercise_bloc/exercise_bloc.dart';

/// 模拟数据源，提供测试数据
class MockDataSource {
  /// 获取模拟题目数据
  static List<QuestionEntity> getMockQuestions() {
    return [
      QuestionEntity(
        id: 1,
        subId: 1,
        question: '机动车驾驶人初次申请机动车驾驶证和增加准驾车型后的实习期是多长时间？',
        options: ['6个月', '12个月', '3个月', '2年'],
        correctAnswer: 'B',
        userAnswer: '',
        explanation: '根据《机动车驾驶证申领和使用规定》，机动车驾驶人初次申请机动车驾驶证和增加准驾车型后的实习期是12个月。',
        imageUrl: '',
        audioUrl: '',
        videoUrl: '',
        difficulty: QuestionDifficulty.easy,
        tags: ['交通法规', '驾驶证'],
        isMarked: false,
        isAnswered: false,
        answerTime: 0,
      ),
      QuestionEntity(
        id: 2,
        subId: 1,
        question: '驾驶机动车在高速公路上行驶，遇有雾、雨、雪、沙尘、冰雹等低能见度气象条件时，能见度在50米以下时，以下做法正确的是什么？',
        options: [
          '加速驶离高速公路',
          '在应急车道上停车等待',
          '可以继续行驶，但需降低车速',
          '尽快驶离高速公路'
        ],
        correctAnswer: 'D',
        userAnswer: '',
        explanation: '能见度在50米以下时，应当尽快驶离高速公路。根据《道路交通安全法实施条例》相关规定，此时不得继续行驶。',
        imageUrl: 'https://example.com/question2.jpg',
        audioUrl: 'https://example.com/question2.mp3',
        videoUrl: 'https://example.com/question2.mp4',
        difficulty: QuestionDifficulty.medium,
        tags: ['高速公路', '恶劣天气'],
        isMarked: false,
        isAnswered: false,
        answerTime: 0,
      ),
      QuestionEntity(
        id: 3,
        subId: 1,
        question: '这个标志是何含义？',
        options: ['注意行人', '人行横道', '注意儿童', '学校区域'],
        correctAnswer: 'A',
        userAnswer: '',
        explanation: '此标志为注意行人标志，用于提醒驾驶人注意前方可能有行人通过。',
        imageUrl: 'https://example.com/traffic_sign_pedestrian.jpg',
        audioUrl: '',
        videoUrl: '',
        difficulty: QuestionDifficulty.easy,
        tags: ['交通标志', '行人'],
        isMarked: false,
        isAnswered: false,
        answerTime: 0,
      ),
      QuestionEntity(
        id: 4,
        subId: 1,
        question: '在这种急弯道路上行车应交替使用远近光灯。',
        options: ['正确', '错误'],
        correctAnswer: 'A',
        userAnswer: '',
        explanation: '在急弯道路上行车时，应当交替使用远近光灯，以提醒对向来车注意，确保行车安全。',
        imageUrl: 'https://example.com/curved_road.jpg',
        audioUrl: 'https://example.com/question4.mp3',
        videoUrl: '',
        difficulty: QuestionDifficulty.medium,
        tags: ['夜间行驶', '弯道'],
        isMarked: false,
        isAnswered: false,
        answerTime: 0,
      ),
      QuestionEntity(
        id: 5,
        subId: 1,
        question: '驾驶机动车在道路上向左变更车道时，应当提前开启左转向灯，在确认安全后变更车道。',
        options: ['正确', '错误'],
        correctAnswer: 'A',
        userAnswer: '',
        explanation: '变更车道时必须提前开启转向灯，并确认安全后才能变更，这是基本的行车规范。',
        imageUrl: '',
        audioUrl: '',
        videoUrl: 'https://example.com/lane_change.mp4',
        difficulty: QuestionDifficulty.easy,
        tags: ['变更车道', '转向灯'],
        isMarked: false,
        isAnswered: false,
        answerTime: 0,
      ),
      QuestionEntity(
        id: 6,
        subId: 1,
        question: '机动车在高速公路上发生故障时，在来车方向150米以外设置警告标志。',
        options: ['正确', '错误'],
        correctAnswer: 'A',
        userAnswer: '',
        explanation: '根据规定，机动车在高速公路上发生故障时，应当在来车方向150米以外设置警告标志。',
        imageUrl: 'https://example.com/highway_breakdown.jpg',
        audioUrl: 'https://example.com/question6.mp3',
        videoUrl: 'https://example.com/highway_safety.mp4',
        difficulty: QuestionDifficulty.medium,
        tags: ['高速公路', '故障处理'],
        isMarked: false,
        isAnswered: false,
        answerTime: 0,
      ),
      QuestionEntity(
        id: 7,
        subId: 1,
        question: '这个标志的含义是提醒车辆驾驶人前方是非机动车道。',
        options: ['正确', '错误'],
        correctAnswer: 'B',
        userAnswer: '',
        explanation: '此标志实际含义是提醒车辆驾驶人前方是机动车道，不是非机动车道。',
        imageUrl: 'https://example.com/motor_vehicle_lane.jpg',
        audioUrl: '',
        videoUrl: '',
        difficulty: QuestionDifficulty.hard,
        tags: ['交通标志', '车道'],
        isMarked: false,
        isAnswered: false,
        answerTime: 0,
      ),
      QuestionEntity(
        id: 8,
        subId: 1,
        question: '驾驶机动车通过铁路道口时，最高速度不能超过多少？',
        options: ['15公里/小时', '20公里/小时', '30公里/小时', '40公里/小时'],
        correctAnswer: 'C',
        userAnswer: '',
        explanation: '根据交通法规，驾驶机动车通过铁路道口时，最高速度不能超过30公里/小时。',
        imageUrl: 'https://example.com/railway_crossing.jpg',
        audioUrl: 'https://example.com/question8.mp3',
        videoUrl: '',
        difficulty: QuestionDifficulty.medium,
        tags: ['铁路道口', '限速'],
        isMarked: false,
        isAnswered: false,
        answerTime: 0,
      ),
      QuestionEntity(
        id: 9,
        subId: 1,
        question: '在这种情况下可以借右侧公交车道超车。',
        options: ['正确', '错误'],
        correctAnswer: 'B',
        userAnswer: '',
        explanation: '不可以借用公交车道超车，公交车道是专用车道，其他车辆不得占用。',
        imageUrl: 'https://example.com/bus_lane.jpg',
        audioUrl: '',
        videoUrl: 'https://example.com/bus_lane_rules.mp4',
        difficulty: QuestionDifficulty.easy,
        tags: ['公交车道', '超车'],
        isMarked: false,
        isAnswered: false,
        answerTime: 0,
      ),
      QuestionEntity(
        id: 10,
        subId: 1,
        question: '驾驶机动车在雨天行车时，为避免发生"水滑"现象而造成方向失控，要控制速度行驶。',
        options: ['正确', '错误'],
        correctAnswer: 'A',
        userAnswer: '',
        explanation: '雨天路面湿滑，高速行驶容易发生"水滑"现象，导致车辆失控，因此必须控制速度行驶。',
        imageUrl: 'https://example.com/rainy_driving.jpg',
        audioUrl: 'https://example.com/question10.mp3',
        videoUrl: 'https://example.com/rainy_safety.mp4',
        difficulty: QuestionDifficulty.medium,
        tags: ['雨天行驶', '安全驾驶'],
        isMarked: false,
        isAnswered: false,
        answerTime: 0,
      ),
    ];
  }

  /// 获取模拟视频数据
  static List<VideoEntity> getMockVideos() {
    return [
      VideoEntity(
        id: 1,
        subId: 1,
        aliyunVid: 'video_001',
        cover: 'https://example.com/video1_cover.jpg',
        favoriteCover: 'https://example.com/video1_fav.jpg',
        title: '交通标志识别详解',
        duration: 180,
        currentPosition: 0,
      ),
      VideoEntity(
        id: 2,
        subId: 1,
        aliyunVid: 'video_002',
        cover: 'https://example.com/video2_cover.jpg',
        favoriteCover: 'https://example.com/video2_fav.jpg',
        title: '高速公路安全驾驶',
        duration: 240,
        currentPosition: 0,
      ),
      VideoEntity(
        id: 3,
        subId: 1,
        aliyunVid: 'video_003',
        cover: 'https://example.com/video3_cover.jpg',
        favoriteCover: 'https://example.com/video3_fav.jpg',
        title: '雨天驾驶技巧',
        duration: 200,
        currentPosition: 0,
      ),
    ];
  }

  /// 获取模拟进度数据
  static ExerciseProgress getMockProgress() {
    return const ExerciseProgress(
      totalQuestions: 10,
      answeredQuestions: 0,
      correctCount: 0,
      wrongCount: 0,
      accuracy: 0.0,
      timeSpent: 0,
      completionRate: 0.0,
    );
  }
}
