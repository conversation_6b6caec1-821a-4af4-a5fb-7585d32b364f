import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../injection/injection.dart';
import '../bloc/exercise/exercise_bloc.dart';
import '../bloc/listen/listen_bloc.dart';
import '../bloc/browse/browse_bloc.dart';
import '../bloc/mode_switch/mode_switch_bloc.dart';
import '../bloc/mode_switch/mode_switch_event.dart';
import '../bloc/mode_switch/mode_switch_state.dart';
import '../widgets/exercise_view.dart';
import '../widgets/listen_view.dart';
import '../widgets/browse_view.dart';
import '../widgets/video_view.dart';

/// Main exercise page with mode switching
class ExercisePage extends StatelessWidget {
  const ExercisePage({
    super.key,
    required this.subject,
    required this.type,
    this.sortId,
    this.mainId,
    this.childId,
    this.luid,
    this.ids,
    this.initialMode = ExerciseMode.exercise,
  });

  final int subject;
  final String type;
  final int? sortId;
  final int? mainId;
  final int? childId;
  final int? luid;
  final List<int>? ids;
  final ExerciseMode initialMode;

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<ExerciseBloc>(
          create: (context) => getIt<ExerciseBloc>(),
        ),
        BlocProvider<ListenBloc>(
          create: (context) => getIt<ListenBloc>(),
        ),
        BlocProvider<BrowseBloc>(
          create: (context) => getIt<BrowseBloc>(),
        ),
        BlocProvider<ModeSwitchBloc>(
          create: (context) => getIt<ModeSwitchBloc>()
            ..add(InitializeMode(
              initialMode: initialMode,
              exerciseType: type,
            )),
        ),
      ],
      child: ExercisePageView(
        subject: subject,
        type: type,
        sortId: sortId,
        mainId: mainId,
        childId: childId,
        luid: luid,
        ids: ids,
      ),
    );
  }
}

/// Exercise page view with mode switching logic
class ExercisePageView extends StatefulWidget {
  const ExercisePageView({
    super.key,
    required this.subject,
    required this.type,
    this.sortId,
    this.mainId,
    this.childId,
    this.luid,
    this.ids,
  });

  final int subject;
  final String type;
  final int? sortId;
  final int? mainId;
  final int? childId;
  final int? luid;
  final List<int>? ids;

  @override
  State<ExercisePageView> createState() => _ExercisePageViewState();
}

class _ExercisePageViewState extends State<ExercisePageView> {
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<ModeSwitchBloc, ModeSwitchState>(
        listener: (context, state) {
          // Handle mode switch animations
          if (state is ModeSwitching) {
            _animateToPage(state.toMode.index);
          } else if (state.isActive) {
            _animateToPage(state.currentMode.index);
          }
        },
        builder: (context, state) {
          return Column(
            children: [
              // Custom app bar (following original PageChapter design)
              _buildCustomAppBar(context, state),

              // Main content with IndexedStack for better performance
              Expanded(
                child: IndexedStack(
                  index: state.currentMode.index,
                  children: [
                    ExerciseView(
                      subject: widget.subject,
                      type: widget.type,
                      sortId: widget.sortId,
                      mainId: widget.mainId,
                      childId: widget.childId,
                      luid: widget.luid,
                      ids: widget.ids,
                    ),
                    ListenView(
                      subject: widget.subject,
                      type: widget.type,
                      sortId: widget.sortId,
                      mainId: widget.mainId,
                      childId: widget.childId,
                      luid: widget.luid,
                      ids: widget.ids,
                    ),
                    BrowseView(
                      subject: widget.subject,
                      type: widget.type,
                      sortId: widget.sortId,
                      mainId: widget.mainId,
                      childId: widget.childId,
                      luid: widget.luid,
                      ids: widget.ids,
                    ),
                    VideoView(
                      subject: widget.subject,
                      type: widget.type,
                      sortId: widget.sortId,
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  /// Build custom app bar (following original PageChapter design)
  Widget _buildCustomAppBar(BuildContext context, ModeSwitchState state) {
    return Container(
      height: 48.0 + MediaQuery.of(context).padding.top,
      child: Container(
        margin: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
        width: double.infinity,
        height: 48.0,
        child: Stack(
          children: [
            // Left: Back button
            Positioned(
              left: 0,
              top: 0,
              bottom: 0,
              child: GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12.0),
                  height: 48.0,
                  alignment: AlignmentDirectional.center,
                  child: const Icon(
                    Icons.arrow_back_ios,
                    size: 18.0,
                    color: Colors.black87,
                  ),
                ),
              ),
            ),

            // Center: TTabBar (做题、听题、背题、视频)
            Container(
              alignment: AlignmentDirectional.center,
              child: Container(
                width: 204.0,
                alignment: AlignmentDirectional.center,
                child: _buildTabBar(context, state),
              ),
            ),

            // Right: Function button (字体大小等)
            if (!state.isSwitching)
              Positioned(
                right: 0,
                top: 0,
                bottom: 0,
                child: GestureDetector(
                  onTap: () => _showFontSizeDialog(context),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    height: 48.0,
                    alignment: AlignmentDirectional.center,
                    child: const Text(
                      '字体',
                      style: TextStyle(
                        color: Colors.black87,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),

            // Show loading indicator when switching
            if (state.isSwitching)
              Positioned(
                right: 16.0,
                top: 0,
                bottom: 0,
                child: Container(
                  alignment: Alignment.center,
                  child: const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Build tab bar (following original TTabBar design)
  Widget _buildTabBar(BuildContext context, ModeSwitchState state) {
    const tabs = ['做题', '听题', '背题', '视频'];
    const height = 32.0;

    return Stack(
      children: [
        // Background container
        Container(
          height: height,
          decoration: const BoxDecoration(
            color: Color(0x991992EF), // Style.blue3_60 equivalent
            borderRadius: BorderRadius.all(Radius.circular(8)),
          ),
        ),
        // Tab items
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: tabs.asMap().entries.map((entry) {
            final index = entry.key;
            final title = entry.value;
            final isSelected = index == state.currentMode.index;

            return Expanded(
              child: GestureDetector(
                onTap: state.canSwitchModes
                    ? () => _switchMode(
                        context, ExerciseModeExtension.fromIndex(index))
                    : null,
                child: Container(
                  height: height,
                  decoration: isSelected
                      ? const BoxDecoration(
                          color: Color(0xFF1992EF), // Style.blue1 equivalent
                          borderRadius: BorderRadius.all(Radius.circular(8)),
                        )
                      : null,
                  alignment: Alignment.center,
                  child: Text(
                    title,
                    style: isSelected
                        ? const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          )
                        : TextStyle(
                            color: state.canSwitchModes
                                ? const Color(0xFF666666)
                                : const Color(0xFFCCCCCC),
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// Switch mode
  void _switchMode(BuildContext context, ExerciseMode mode) {
    final modeSwitchBloc = context.read<ModeSwitchBloc>();

    switch (mode) {
      case ExerciseMode.exercise:
        modeSwitchBloc.add(const SwitchToExercise());
        break;
      case ExerciseMode.listen:
        modeSwitchBloc.add(const SwitchToListen());
        break;
      case ExerciseMode.browse:
        modeSwitchBloc.add(const SwitchToBrowse());
        break;
      case ExerciseMode.video:
        modeSwitchBloc.add(const SwitchToVideo());
        break;
    }
  }

  /// Show font size dialog (placeholder implementation)
  void _showFontSizeDialog(BuildContext context) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('字体大小'),
        content: const Text('字体大小调节功能'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// Animate to page
  void _animateToPage(int index) {
    if (_pageController.hasClients) {
      _pageController.animateToPage(
        index,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// Show confirmation dialog
  void _showConfirmationDialog(BuildContext context, ModeSwitchState state) {
    if (state is! ModeSwitchConfirmationRequired) return;

    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认切换模式'),
        content: Text(state.reason),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<ModeSwitchBloc>().add(const CancelModeSwitch());
            },
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<ModeSwitchBloc>().add(
                    ConfirmModeSwitch(targetMode: state.targetMode),
                  );
            },
            child: const Text('确认'),
          ),
        ],
      ),
    );
  }
}
