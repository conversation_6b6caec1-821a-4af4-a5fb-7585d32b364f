import 'package:get_it/get_it.dart';
import 'package:dio/dio.dart';

import '../domain/repositories/question_repository.dart';
import '../domain/repositories/audio_repository.dart';
import '../domain/repositories/progress_repository.dart';

import '../domain/usecases/load_questions.dart';
import '../domain/usecases/answer_question.dart';
import '../domain/usecases/play_audio.dart';
import '../domain/usecases/save_progress.dart';

import '../presentation/bloc/exercise/exercise_bloc.dart';
import '../presentation/bloc/listen/listen_bloc.dart';
import '../presentation/bloc/browse/browse_bloc.dart';
import '../presentation/bloc/mode_switch/mode_switch_bloc.dart';

// Data layer imports
import '../data/repositories/question_repository_impl.dart';
import '../data/repositories/audio_repository_impl.dart' as audio_repo;
import '../data/repositories/progress_repository_impl.dart';
import '../data/datasources/question_remote_datasource.dart';
import '../data/datasources/question_local_datasource.dart';
import '../data/datasources/progress_local_datasource.dart';
import '../data/datasources/progress_remote_datasource.dart';
import '../data/datasources/audio_datasource.dart' as audio_ds;

final getIt = GetIt.instance;

/// Manual dependency injection setup
void setupDependencies({
  String? baseUrl,
  String? databasePath,
}) {
  // Configure Dio for HTTP requests
  final dio = Dio();
  if (baseUrl != null) {
    dio.options.baseUrl = baseUrl;
  }
  dio.options.connectTimeout = const Duration(seconds: 30);
  dio.options.receiveTimeout = const Duration(seconds: 30);

  getIt.registerLazySingleton<Dio>(() => dio);

  // Data sources
  getIt.registerLazySingleton<QuestionRemoteDataSource>(
    () => QuestionRemoteDataSourceImpl(
      dio: getIt<Dio>(),
      baseUrl: baseUrl ?? 'https://api.example.com',
    ),
  );

  getIt.registerLazySingleton<QuestionLocalDataSource>(
    () => QuestionLocalDataSourceImpl(
      databasePath: databasePath ?? 'exercise.db',
    ),
  );

  getIt.registerLazySingleton<ProgressLocalDataSource>(
    () => ProgressLocalDataSourceImpl(
      databasePath: databasePath ?? 'progress.db',
    ),
  );

  getIt.registerLazySingleton<ProgressRemoteDataSource>(
    () => ProgressRemoteDataSourceImpl(
      dio: getIt<Dio>(),
      baseUrl: baseUrl ?? 'https://api.example.com',
    ),
  );

  // Repositories
  getIt.registerLazySingleton<QuestionRepository>(
    () => QuestionRepositoryImpl(
      remoteDataSource: getIt<QuestionRemoteDataSource>(),
      localDataSource: getIt<QuestionLocalDataSource>(),
    ),
  );

  getIt.registerLazySingleton<AudioRepository>(
    () => audio_repo.AudioRepositoryImpl(
      audioDataSource: audio_ds.AudioDataSourceImpl(),
    ),
  );

  getIt.registerLazySingleton<ProgressRepository>(
    () => ProgressRepositoryImpl(
      localDataSource: getIt<ProgressLocalDataSource>(),
      remoteDataSource: getIt<ProgressRemoteDataSource>(),
    ),
  );

  // Use cases
  getIt.registerLazySingleton<LoadQuestions>(
    () => LoadQuestions(getIt<QuestionRepository>()),
  );

  getIt.registerLazySingleton<AnswerQuestion>(
    () => AnswerQuestion(getIt<QuestionRepository>()),
  );

  getIt.registerLazySingleton<PlayAudio>(
    () => PlayAudio(
      getIt<AudioRepository>(),
      getIt<QuestionRepository>(),
    ),
  );

  getIt.registerLazySingleton<SaveProgress>(
    () => SaveProgress(getIt<ProgressRepository>()),
  );

  // BLoCs
  getIt.registerFactory<ExerciseBloc>(
    () => ExerciseBloc(
      loadQuestions: getIt<LoadQuestions>(),
      answerQuestion: getIt<AnswerQuestion>(),
      saveProgress: getIt<SaveProgress>(),
      questionRepository: getIt<QuestionRepository>(),
    ),
  );

  getIt.registerFactory<ListenBloc>(
    () => ListenBloc(
      loadQuestions: getIt<LoadQuestions>(),
      playAudio: getIt<PlayAudio>(),
      saveProgress: getIt<SaveProgress>(),
      audioRepository: getIt<AudioRepository>(),
    ),
  );

  getIt.registerFactory<BrowseBloc>(
    () => BrowseBloc(
      loadQuestions: getIt<LoadQuestions>(),
      questionRepository: getIt<QuestionRepository>(),
    ),
  );

  getIt.registerLazySingleton<ModeSwitchBloc>(
    () => ModeSwitchBloc(),
  );
}

/// Initialize data sources
Future<void> initializeDataSources() async {
  // Initialize local database
  await getIt<QuestionLocalDataSource>().initialize();
}

/// Reset all dependencies (for testing)
void resetDependencies() {
  getIt.reset();
}
